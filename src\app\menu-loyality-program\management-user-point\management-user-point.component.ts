import {Component, Inject, OnInit} from '@angular/core';
import {EmployeeType, UserCategory} from "../../shared/enums/user-category.enum";
import {FidelityStatus, Particular,} from "../../shared/models/user.models";
import {CommonService} from "../../shared/services/common.service";
import {UserService} from "../../menu-management-account/services/user.service";
import {ConfirmationService, MessageService} from "primeng/api";
import {APP_BASE_HREF} from "@angular/common";
import {t} from "../../shared/functions/global.function";
import {HttpErrorResponse} from "@angular/common/http";
import {CompanyCategory} from "../../shared/enums/Company-category.enum";
import {CompanyService} from "../../menu-management-account/companie-account/company.service";

@Component({
  selector: 'mcw-management-user-point',
  templateUrl: './management-user-point.component.html',
  styles: [
  ],
  providers: [ConfirmationService, MessageService],

})
export class ManagementUserPointComponent implements OnInit {
  userType: string = this.commonService.setStatusLabel(UserCategory.Particular);
  title: string = '';
  showSideBar: boolean;
  isLoading: boolean;
  showDialogDetail: boolean;
  showDialogExport: boolean;
  dataUser: any;
  isLoadingInModal: boolean;
  filterForm = {
    email: '',
    tel: '',
  };
  usersEmails: any;
  usersTels: any;
  users: any[] = [];
  offset:number = 0;
  limit:number = 50;
  total:number = 0;
  language:string = this.baseHref?.replace(/\//g, '');

  isValidated: boolean;

  constructor(
    public commonService: CommonService,
    public userService: UserService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private companySrv: CompanyService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getTitle(this.userType);
    this.userType = this.commonService.setStatusLabel(UserCategory.Particular);
    await this.handleChangeTabUser()
    await this.getElementsForFilters();

  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.handleChangeTabUser()
  }

  async getUsers(): Promise<boolean> {
    this.isLoading = true;
    this.showSideBar = false;
    const query: any = {
      ...this.filterForm,
      offset: this.offset,
      limit: this.limit,
      isValidated: this.isValidated
    };
    let result;
    if (UserCategory[this.userType] == UserCategory.Particular) {
      query.category = UserCategory[this.userType];
      result = await this.userService.getUsersPoint(query);
    } else {
      result = await this.companySrv.getAllCompaniesPoints(query);
    }
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('GETDATA_ERROR')}`,
        detail: result.error.message
      });
      return this.isLoading = false;
    }
    this.total = result.count
    this.users = result?.data;
    return this.isLoading = false;
  }

  async getElementsForFilters() {
    const keyForFilters = ['email', 'tel'];
    const usersInfos = await this.commonService.getElementForFilterByKeys('users', { keyForFilters })
    this.usersEmails = usersInfos?.dataemail;
    this.usersTels = usersInfos?.datatel;
  }

  async handleChangeTabUser(): Promise<void | boolean> {
    this.isLoading = true;

    if (this.userService.indexTabClient === 0) {
      this.userType = this.commonService.setStatusLabel(UserCategory.Particular);
    }
    if (this.userService.indexTabClient === 1) {
      this.userType = this.commonService.setStatusLabel(UserCategory.CompanyUser);

    }

    await this.getTitle(this.userType);
    await this.getUsers();
  }

  async reset(): Promise<void> {
    this.offset = 0;

    this.filterForm = {
      email: '',
      tel: '',
    };
    await this.getUsers();
  }

  async getTitle(user: string): Promise<void> {
    if (user === 'Particular') { this.title = `indirect` }
    if (user === 'CompanyUser') { this.title = `direct` }
  }

  async exportToExcel(): Promise<void> {
    this.isLoading = true;
    await this.getUsers();


    if (this.userService.indexTabClient === 0) {
      this.dataUser = this.users.map(elt => {
        const data:{} = {};
        data['NOM'] = elt?.lastName || 'N/A';
        data['EMAIL'] = elt?.email || 'N/A';
        data['CNI'] = elt?.cni;
        data['REGION'] = elt?.address?.region || 'N/A';
        data['VILLE'] = elt?.address?.city || 'N/A';
        data['TELEPHONE'] = elt?.tel || 'N/A';
        data['Point non validé'] = elt?.points?.unvalidated || 'N/A';
        data['Point validé'] = elt?.points?.validate || 'N/A';
        data['Niveau Fidélité'] =  FidelityStatus[elt?.points?.status]

        return data;
      });    }
    if (this.userService.indexTabClient === 1) {
      this.dataUser = this.users.map(elt => {
        const data = {};
      data['Raison social'] = elt?.name || 'N/A';
      data['Commercial region'] = elt?.address?.region;
      data['Régistre de commerce'] = elt?.rccm;
      data['Solto'] = elt?.erpSoldToId;
      data['Région'] = elt?.address?.region;
      data['Ville'] = elt?.address?.city;
      data['Telephone'] = elt?.tel;
      data['Point non validé'] = elt?.points?.unvalidated || 'N/A';
      data['Point validé'] = elt?.points?.validate || 'N/A';
      data['Niveau Fidélité'] =  FidelityStatus[elt?.points?.status]
      return data;
      });    }
   
    this.commonService.exportRetriveExcelFile(this.dataUser, 'Liste des points  utiilisateurs');
    this.isLoading = false;
  }

  getColor(category: number): string {
    if (category === CompanyCategory.Baker) { return 'bg-primary-100' }
    if (category === CompanyCategory.WholeSaler) { return 'bg-secondary-300' }
    if (category === CompanyCategory.GMS) { return 'bg-tertiary-100' }
    if (category === CompanyCategory.Group) { return 'bg-info-100' }
    if (category === CompanyCategory.Industry) { return 'bg-warning-100' }
    if (category === CompanyCategory.EXPORT) { return 'bg-warning-200' }

    return '';
  }

  getColorLoyaltyProgram(status: FidelityStatus.PRIVILEGE): string {
    if (status === FidelityStatus.PRIVILEGE) { return 'bg-info-400' }
    if (status === FidelityStatus.DIAMOND) { return 'bg-primary-400' }
    if (status === FidelityStatus.PREMIUM) { return 'bg-warning-600' }

    return 'bg-tertiary-400';
  }
}
