import { Point } from "chart.js";
import { OrderStatus, OrderValidation } from "./order";
import { Particular, User } from "./user.models";
import { Category } from "./category.model";
import { Discount } from "../types";

export class OrderItem {

    _id?: string;
    cart?: MarketPlaceCart;
    user: Particular;
    status?: OrderStatus;
    appReference?: string;
    validation?: {
        user: Partial<User>;
        date: number;
        reason: string;
    }
    created_at?: number
    isMobile?:boolean;

}

export class MarketPlaceCart {
    items: Items;
    quantity: number;
    amount?: MarketOrderPrice;
    optionsDiscount?: { points?: boolean };
}
export class Items {
    _id: string
    name: string;
    image: string;
    sku: string;
    price: number;
    description: string;
    category: Category;
}

export declare type MarketOrderPrice = {
    HT: number;
    VAT: number;
    TTC: number;
    discount?: Discount;
};
