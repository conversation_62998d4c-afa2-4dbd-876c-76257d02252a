import { Injectable } from '@angular/core';;
import { HttpClient, HttpParams } from '@angular/common/http';
import { lastValueFrom } from 'rxjs';
import { environment } from 'src/environments/environment';
import { BaseUrlService } from '../../shared/services/base-url.service';
import { CommonService } from '../../shared/services/common.service';
import { CompanyEmployee, Logo } from '../../shared/models/user.models';
import { Company } from 'src/app/shared/models/company.models';
import { CompanyAction } from 'src/app/shared/actions/company-authorization.actions';
import { QueryResult } from 'src/app/shared/types';
import { t } from 'src/app/shared/functions/global.function';
import { Balance } from 'src/app/shared/models/balance.model';

@Injectable({
  providedIn: 'root',
})
export class CompanyService {
  base_url: string;
  companyAction = CompanyAction;
  filterlistCompanyForm = {
    name: '',
    erpShipToId: '',
    erpSoldToId: '',
    commercialRegion: '',
    city: '',
    enable: true,
    region: ''
  };

  constructor(
    private baseUrl: BaseUrlService,
    private http: HttpClient,
    private commonSrv: CommonService
  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
    this.base_url += '/companies';
  }

  async create(company: Company): Promise<QueryResult> {
    try {
      delete company._id;
      company.tel = +company.tel;
      return await lastValueFrom(this.http.post<QueryResult>(this.base_url, company,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async getAllCompanies(param?: any): Promise<{ data: Company[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();

      const { offset, limit, name, erpSoldToId, projection, commercialRegion,
        tel, city, region, enable = true, isLoyaltyProgDistributor } = param;

      if (commercialRegion) { params = params.append('address.commercialRegion', commercialRegion); }
      if (isLoyaltyProgDistributor) { params = params.append('isLoyaltyProgDistributor', isLoyaltyProgDistributor); }
      if (name) { params = params.append('name', name); }
      if (city) { params = params.append('address.city', city.toUpperCase()); }
      if (region) { params = params.append('address.region', region.toUpperCase()); }
      if (erpSoldToId) { params = params.append('erpSoldToId', erpSoldToId); }
      if (tel) { params = params.append('tel', `${tel}`); }
      if (projection) { params = params.append('projection', projection); }
      if (!['null', 'undefined', null, undefined].includes(offset)) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<{ data: Company[], count: number }>(this.base_url, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async getAllCompaniesPoints(param?: any): Promise<{ data: Company[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();

      const { category, offset, limit, name, erpSoldToId, projection, commercialRegion,
        tel, city, region, enable = true, isLoyaltyProgDistributor } = param;

      if (commercialRegion) { params = params.append('address.commercialRegion', commercialRegion); }
      if (isLoyaltyProgDistributor) { params = params.append('isLoyaltyProgDistributor', isLoyaltyProgDistributor); }
      if (name) { params = params.append('name', name); }
      if (city) { params = params.append('address.city', city.toUpperCase()); }
      if (region) { params = params.append('address.region', region.toUpperCase()); }
      if (erpSoldToId) { params = params.append('erpSoldToId', erpSoldToId); }
      if (tel) { params = params.append('tel', `${tel}`); }
      if (projection) { params = params.append('projection', projection); }
      if (!['null', 'undefined', null, undefined].includes(offset)) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<{ data: Company[], count: number }>(this.base_url, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }


  async find(idCompany: string): Promise<Company> {
    try {
      return await lastValueFrom(
        this.http.get<Company>(this.base_url + '/' + idCompany));
    } catch (error) {
      return new Company();
    }
  }

  async update(company: Company): Promise<QueryResult> {
    try {
      if (company?.tel) company.tel = +company.tel;
      delete company.points
      delete company['advantageId'];

      return await lastValueFrom(
        this.http.patch(`${this.base_url}/${company._id}`, company,
          { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async changeStatut(company: Company): Promise<QueryResult> {
    try {
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/${company._id}`, { enable: !company?.enable },
          { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return this.commonSrv.getError('Une erreur est survenue', error);
    }
  }

  async getBalance(companyId: string): Promise<Balance | QueryResult> {
    try {
      let params = new HttpParams();
      params = params.set('_id', companyId);
      return await lastValueFrom(this.http.get<Balance>(`${this.base_url}/balance`, { params: params }));
    } catch (error) {
      return this.commonSrv.getError('Une erreur est survenue', error);
    }
  }

  async getUsersCompany(id: string, param?: { email: string, enable: boolean }): Promise<{ data: CompanyEmployee[], count: number }> {
    try {
      let params = new HttpParams();
      const { email, enable = true } = param;

      if (email) { params = params.append('email', email); }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<{ data: CompanyEmployee[], count: number }>
        (`${this.base_url}/${id}/users`, { params }));
    } catch (error) {
      return error;
    }
  }

  async createUsersCompany(id: string, user: CompanyEmployee): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.post
        (`${this.base_url}/${id}/users`, user));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async updateUsersCompany(id: string, user: CompanyEmployee): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch
        (`${this.base_url}/${id}/users`, user));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async saveLogoCompany(id: string, logo: Partial<Logo>): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch
        (`${this.base_url}/${id}/logo`, { ...logo }));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }
}
