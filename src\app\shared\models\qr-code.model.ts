import { Packaging } from "./packaging.model";
import { Product } from "./product.model";

export class QRCodeData {
  _id?: string;
  code: string;
  status: QRCodeStatus;
  product: Partial<Product>;
  packaging: Partial<Packaging>;
  enable?: boolean;
  created_at?: number;
  updated_at?: number;
  user_id?: string;
  qrCodeFileReference: string;
}

export enum QRCodeStatus {
  ACTIVE = 100,
  SCANNED = 200,
  USED = 300,
  INACTIVE = 99,
}


export enum QrCodeAction {
  CREATE = 'create_qr_code',
  UPDATE = 'update_qr_code',
  DELETE = 'delete_qr_code',
  VIEW = 'view_qr_code',
}