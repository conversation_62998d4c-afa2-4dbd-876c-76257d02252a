<div class="history-list-page page-container container">
  <div class="history-list-page-container">
    <div class="header-list">
      <div class="title-container">
        <h2 class="title" i18n="@@historyOrders-List">Liste des commandes</h2>
        <div class="section-button">

          <button type="button" pButton label="Réinitialiser" i18n-label="@@historyOrders-Reset" icon="pi pi-replay"
            class="p-button-text p-button-warning" (click)="reset()">
          </button>

          <button type="button" pButton class="p-button-secondary" i18n-label="@@historyOrders-label" label="Filtre"
            icon="pi pi-filter" (click)="showSideBar = true">
          </button>

          <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
            icon="pi pi-file-pdf" (click)="showDialogExport = true">
          </button>

        </div>
      </div>

    </div>

    <div class="align-container">
      <div class="align-paginator">
        <div i18n="@@historyOrders-Ton" class="title-h4">{{totalOrders}} commande(s), {{totalTonnes | number:"1.0"}}
          Tonne(s),
          {{totalAmount | currency:'':'':"3.0"}} FCFA<span *ngIf="commonSrv.user.category === 200">, Region commerciale
            :
            {{commonSrv.user?.address?.commercialRegion}}</span></div>
        <p-paginator (onPageChange)="paginate($event)" [rows]="limit" [totalRecords]="total">
        </p-paginator>
      </div>
      <div class="list-container">
        <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
        <p-table [value]="orders" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
          *ngIf="orders?.length > 0 || isLoading">
          <ng-template pTemplate="header">
            <tr>
              <th style="min-width: 30px; max-width: 30px">N°</th>
              <th style="min-width: 180px; max-width: 180px" i18n="@@historyOrders-ListNumber">N° Commande</th>
              <th style="min-width: 180px; max-width: 180px" i18n="@@historyOrders-ListJDE">Ref Commande</th>
              <th style="min-width: 100px; max-width: 100px" class="row-clr">
                <div class="elt">Type</div>
              </th>
              <th style="min-width: 200px; max-width: 200px">Client
              </th>

              <th style="min-width: 115px; max-width: 115px" class="row-clr">
                <div class="elt" i18n="@@historyOrders-ListCtgory">Catégorie</div>
              </th>

              <th style="min-width: 70px; max-width: 70px">Region
              </th>

              <th style="min-width: 180px; max-width: 180px" i18n="@@historyOrders-ListDate">Date création</th>
              <th style="min-width: 150px; max-width: 150px" i18n="@@historyOrders-ListCost">Montant</th>
              <th style="min-width: 120px; max-width: 120px" class="row-clr">
                <div class="elt" i18n="@@historyOrders-ListStatut">Statut</div>
              </th>
              <th i18n="@@historyOrders-ListDetail">Détails</th>
              <th style="min-width: 40px; max-width: 40px" class="iconsBtn"></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-order let-i="rowIndex">
            <tr [ngClass]="{ 'blink': order?.cancellationStatus === orderCancelEnum.ISSUE }">
              <td style="min-width: 30px; max-width: 30px">{{i + offset + 1}}</td>

              <td style="min-width: 180px; max-width: 180px"
                pTooltip="{{order?.customerReference || order?.appReference}}" tooltipPosition="top">
                {{order?.customerReference || order?.appReference || 'N/A' | truncateString:18}}
              </td>

              <td style="min-width: 180px; max-width: 180px" pTooltip="{{order?.erpReference}}" tooltipPosition="top">
                {{ order?.erpReference || 'N/A' | truncateString:20}}</td>

              <td style="min-width: 100px; max-width: 100px" class="row-clr">
                <button class="elt" [ngClass]="order?.cart?.renderType | colorRenderTypeOrder">
                  {{order?.cart?.renderType | renderTypeStatusOrder }}
                </button>
              </td>

              <td style="min-width: 200px; max-width: 200px"
                pTooltip="{{order?.company?.name || order?.user?.firstName + ' ' +order?.user?.lastName}}"
                tooltipPosition="top">
                {{order?.company?.name || order?.user?.firstName + ' ' +order?.user?.lastName || 'N/A' |
                truncateString:23 }}
              </td>
              <td style="min-width: 115px; max-width: 115px" class="row-clr">
                <button class="elt" [ngClass]="order.company?.category | colorCategoryClient">
                  {{(order?.company?.category || order?.user?.category) | categoryUserName}}
                </button>
              </td>
              <td style="min-width: 70px; max-width: 70px" pTooltip="{{order?.company?.address?.commercialRegion}}"
                tooltipPosition="top">
                {{order?.company?.address?.commercialRegion || 'N/A'}}
              </td>

              <td style="min-width: 180px; max-width: 180px" pTooltip="{{order?.created_at | date : 'short'}}"
                tooltipPosition="top">
                {{order?.created_at | date : 'dd/MM/yyyy' | truncateString:20 }}
                à {{order?.created_at |date : 'shortTime' : '' : 'fr'}}</td>
              <td style="min-width: 150px; max-width: 150px">{{order?.cart?.amount?.TTC | number }}
                XAF</td>
              <td style="min-width: 120px; max-width: 120px" class="row-clr">
                <button class="elt" [ngClass]="order?.status | colorStatusOrder">
                  {{(order?.status | statusOrder : order?.user | async) + ' '}}{{ (order?.nberModif &&
                  order?.status === orderStatus?.PAID) ? ' : ' + order?.nberModif : ''}}
                </button>
              </td>

              <td class="item" *ngFor="let item of order?.cart?.items?.slice(0, 2)">
                <img alt="image" [src]="item?.product?.image" />
                <div class="small-text">
                  {{item?.quantity + ' SAC ' + item.packaging?.label + ' - ' + (item?.quantity
                  / item.packaging?.unit?.ratioToTone)+'T'}}</div>
              </td>
              <td class="iconsBtn" style="min-width: 40px; max-width: 40px">
                <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                </button>
                <p-overlayPanel #op [style]="{width: '10 0px'}">
                  <ng-template pTemplate>
                    <div class="iconsBtn">
                      <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                        tooltipPosition="top" (click)="showModalDetail(order)">
                        <i class="pi pi-book"></i>
                      </div>
                      <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@listUpdate"
                        *ngIf="commonSrv?.user?.authorizations?.includes(orderAction?.VALIDATE) && order?.status === orderStatus.PAID"
                        pTooltip="validée" tooltipPosition="top" (click)="showModalValidate(order)">
                        <i class="pi pi-check"></i>
                      </div>
                      <!-- <div class="btn btn-icon btn-icon-view bg-200-success" i18n-pTooltip="@@listDetail"
                        pTooltip="Modifier la commande" tooltipPosition="top" (click)="showModalEditOrder(order)">
                        <i class="pi pi-pencil"></i>
                      </div> -->
                      <div class="btn btn-icon btn-icon-delete" i18n-pTooltip="@@listUpdate"
                        *ngIf="commonSrv?.user?.authorizations?.includes(orderAction?.DELETE) && order?.status === orderStatus.PAID"
                        pTooltip="Renvoyée" tooltipPosition="top" (click)="showModalReject(order)">
                        <i class="pi pi-times"></i>
                      </div>
                      <div class="btn btn-icon btn-icon-trash" i18n-pTooltip="@@listUpdate"
                        *ngIf="commonSrv?.user?.authorizations?.includes(orderAction?.CANCEL) &&
                      (order?.status === orderStatus.VALIDATED || order?.status === orderStatus.CREDIT_REJECTED || order?.status === orderStatus.PAID )" pTooltip="Annulée"
                        tooltipPosition="top" (click)="showModalCancel(order)">
                        <i class="pi pi-trash" style="color: brown;"></i>
                      </div>
                      <div class="btn btn-icon btn-icon-delete" i18n-pTooltip="@@listUpdate"
                        *ngIf="commonSrv?.user?.authorizations?.includes(orderAction?.CANCEL) && order?.cancellationStatus === orderCancelEnum.ISSUE"
                        pTooltip="Demande d'annulation" tooltipPosition="top"
                        (click)="showOrderRequestCancelMOdal(order)">
                        <i class="pi pi-reply" style="color: brown;"></i>
                      </div>

                    </div>
                  </ng-template>
                </p-overlayPanel>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <div *ngIf="orders?.length <= 0 && !isLoading">
          <div class="not-found">
            <img src="assets/icons/data-not-fount.svg" alt="">
            <h6 i18n="@@manageOrderEmpty">
              Aucune commande trouvée
            </h6>
          </div>
        </div>
      </div>
    </div>

  </div>

  <p-sidebar [(visible)]="showSideBar" [style]="{width:'22em'}" position="right">
    <section class="filter-sidebar">
      <section class="header-filter">
        <button type="button" pButton i18n-label="@@historyOrders-Reset" label="Réinitialiser" icon="pi pi-replay"
          class="p-button-text p-button-warning" (click)="reset()"></button>
      </section>
      <section class="body-filter">
        <form class="form">
          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-List_Date">Date de début</label>
            <p-calendar [(ngModel)]="filterForm.date.start" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>

          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-ListEndDate">Date de fin</label>
            <p-calendar [(ngModel)]="filterForm.date.end" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>

          <div class="input-group">
            <label for="status" i18n="@@region">Region</label>
            <p-dropdown name="type" [options]="region" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.region" i18n-placeholder="@@regionselect" placeholder="Sélectionner une region">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="user" i18n="@@historyOrders-List_customer">Client </label>
            <p-dropdown name="userCategory" [options]="userLabels" optionLabel="label" optionValue="label"
              filterBy="label" [filter]="true" [showClear]="true" [(ngModel)]="filterForm.customer"
              placeholder="Sélectionner un client">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="status" i18n="@@historyOrders-ListType">Statut commande </label>
            <p-dropdown name="type" [options]="statusOrder" optionLabel="name" [(ngModel)]="filterForm.status"
              optionValue="code" i18n-placeholder="@@historyOrders-ListSelectStatut"
              placeholder="Sélectionner une commande" [showClear]="true">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="status" i18n="@@historyOrders-ListApp">N° commande </label>
            <p-dropdown name="type" [options]="orderNumbers" optionLabel="label" [(ngModel)]="filterForm.appReference"
              optionValue="label" i18n-placeholder="@@historyOrders-ListSelectAppRef" [filter]="true" filterBy="label"
              placeholder="Saisisser un numéro commande" [showClear]="true">
            </p-dropdown>
          </div>

          <!-- <div class="input-group">
            <label for="type" i18n="@@historyOrders-List_Statut">État </label>
            <p-dropdown name="type" [options]="statusDocument" optionLabel="name" optionValue="code"
              [(ngModel)]="filterForm.enable" i18n-placeholder="@@historyOrders-List_select" [showClear]="true"
              placeholder="Sélectionner un statut">
            </p-dropdown>
          </div> -->

          <div class="input-group">
            <label for="paymentMode" i18n="@@historyOrders-List_payment_mode">Mode de paiement </label>
            <p-dropdown name="paymentMode" [options]="statusPaiement" optionLabel="name" optionValue="code"
              [filter]="true" filterBy="name" [(ngModel)]="filterForm.paymentMode"
              i18n-placeholder="@@historyOrders-pclh_payment_mode" [showClear]="true"
              placeholder="Sélectionner un mode de paiement">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="userCategory" i18n="@@historyOrders-List_userType">Type utilisateur </label>
            <p-dropdown name="userCategory" [options]="categoriesUsers" optionLabel="name" optionValue="code"
              [showClear]="true" [(ngModel)]="filterForm.userCategory" placeholder="Sélectionner une catégorie">
            </p-dropdown>
          </div>


          <div class="input-group">
            <label for="product" i18n="@@historyOrders-List_product">Produit </label>
            <p-dropdown name="userCategory" [options]="productlabels" optionLabel="label" optionValue="label"
              [filter]="true" filterBy="label" [showClear]="true" [(ngModel)]="filterForm.product"
              placeholder="Sélectionner un produit">
            </p-dropdown>
          </div>

          <!-- <div class="input-group">
            <label for="userCategory">{{'TYPE_RENDER' | translate | async}}</label>
            <p-dropdown name="userCategory" [options]="renderType" optionLabel="name" optionValue="code"
              [showClear]="true" [(ngModel)]="filterForm.renderType"
              placeholder="{{'TYPE_RENDER' | translate | async}}">
            </p-dropdown>
          </div> -->
        </form>
      </section>
      <section class="footer-filter ">
        <button pButton pRipple type="button" i18n-label="@@historyOrders-ListOut" label="Annuler" icon="pi pi-times"
          class="p-button-outlined p-button-secondary" (click)=" showSideBar = false">
        </button>
        <button pButton pRipple type="button" i18n-label="@@historyOrders-ListFilter" label="Filtrer"
          icon="pi pi-search" class="p-button-success" (click)=" offset=0; getOrders(); showSideBar = false">
        </button>
        <p-toast></p-toast>
      </section>
    </section>
  </p-sidebar>
</div>

<p-dialog i18n-header="@@historyOrders-ListOrderDetail" header="DÉTAIL DE LA COMMANDE"
  [(visible)]="historyOrderSrv.modalDetail" [modal]="true" [style]="{width: '35%'}" [draggable]="false"
  [resizable]="false">
  <mcw-orders-detail [order]="order"></mcw-orders-detail>
</p-dialog>

<!-- <p-dialog header="Liste des Enlèvements" [(visible)]="modalDetailRemoval" [modal]="true" [style]="{width: '50%'}">
  <div class="removals-list">
    <div class="card" accent="success">
      <div class="list-container">
        <div class="list-elt-contain">
          <div class="seperate list-elt-header header-list">
            <div class="col-nberTruck item-group">
              <div class="col item">
              </div>
            </div>
            <div class="">
              <div class=" list-elt ">
                <div class="col col-removalDate">Date</div>
                <div class="col col-hourTime">Heure</div>
                <div class="col col-price">Quart temps</div>
                <div class="col col-weight">Quantite</div>
                <div class="col col-nberTruck">Nombre de camions</div>
              </div>
            </div>
          </div>
          <div class="seperate" *ngFor="let removal of order?.removals; let i = index">
            <div class="col-nberTruck item-group">
              <div class="col item">
                <img alt="image" [src]="getProductForSchedule(removal?.itemId)" />
                <div class="small-text">{{getQuantityProductForSchedule(removal?.itemId)+ ' ' +
                  order?.cart?.packaging?.label }}</div>
              </div>
            </div>
            <div class=" list-elt " *ngFor="let schedule of removal?.schedules">
              <div class="col col-removalDate ">{{schedule?.removalDate | date: 'dd/MM/YYYY'}}</div>
              <div class="col col-hourTime ">{{schedule?.hourTime}}</div>
              <div class="col col-price ">{{schedule?.quartTime}}</div>
              <div class="col col-weight "> {{schedule?.nbrTonnage}}T</div>
              <div class="col col-hourTime">{{schedule?.nbrTruck}}</div>
            </div>

          </div>

        </div>
      </div>
      <div class="form--footer">
        <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
          class="p-button bg-tertiary-400 border-tertiary-400" (click)="modalDetailRemoval = false">
        </button>
      </div>
    </div>
  </div>
</p-dialog> -->

<p-dialog [header]="'Éditon Commande ' + order?.appReference" [(visible)]="modalEditOrder" [modal]="true"
  [style]="{width: '50%'}">
  <div class="edit-order-list">
    <div class="card" accent="success">
      <div class="list-container">
        <div class="list-elt-contain">
          <div class="seperate list-elt-header header-list">
            <div class="col-nberTruck item-group">
              <div class="col item">
                Produits
              </div>
            </div>
            <div class="list-elt headers">
              <div class="col col-price">Packagings</div>
              <div class="col col-weight">Quantite</div>
              <div class="col col-hourTime">Prixs</div>
            </div>
          </div>
          <div class="row-data" *ngFor="let item of order?.cart?.items; let i = index">
            <div class="col-nberTruck item-group">
              <div class="col item">
                <img alt="image" [src]="item?.product?.image" />
                <div class="small-text">{{item.quantity + ' ' + item?.packaging?.label}}</div>
              </div>
            </div>
            <div class="list-elt data">
              <div class="col col-price">
                <p-dropdown class="form-input" [options]="packagings[i]" [(ngModel)]="item.packaging"
                  (onChange)="item.quantity = 0" i18n-placeholder="@@manageItemPackging"
                  placeholder="Sélectionner un packaging" optionLabel="label" [showClear]="true">
                </p-dropdown>
              </div>
              <div class="col col-weight">
                <p-inputNumber [(ngModel)]="item.quantity" (onInput)="getTotalPrice($event,item.packaging,i)"
                  type="number" [size]="15" class="form-input"></p-inputNumber>
              </div>
              <div class="col col-hourTime">
                <p-inputNumber [(ngModel)]="offerPriceAmount[i]" type="number" [size]="15" [disabled]="true"
                  class="form-input" [readOnly]="true"></p-inputNumber>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form--footer">
        <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
          class="p-button bg-tertiary-400 border-tertiary-400" (click)="modalEditOrder = false">
        </button>
        <button type="button" pButton i18n-label="@@listRegistered" label="Enregistrer" icon="pi pi-times"
          class="p-button bg-primary-400 border-primary-400" (click)="modalEditOrder = false">
        </button>
      </div>
    </div>
  </div>
</p-dialog>


<p-confirmDialog acceptLabel="{{'CONFIRM' | translate |async}}" [style]="{width: '30vw'}"
  i18n-rejectLabel="@@titleUpdatemodalOutUnit" rejectLabel="Annuler"
  rejectButtonStyleClass="p-button-text bg-tertiary-400 clr-default-400"
  [acceptButtonStyleClass]="filterForm?.enable? 'bg-secondary-400 border-secondary-400' : ''" defaultFocus="none">
</p-confirmDialog>

<p-dialog
  header="{{ isValid ? ('VALIDATION' | translate | async) : (isReject ? ('REJECTION' | translate | async) : (isCancel ? ('CANCELE' | translate | async) : '')) }}"
  [visible]="isValid  || isReject || isCancel" [modal]="true">
  <div class="dialog-prime">
    <div class="body">
      <i [class]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-info-circle'" style="font-size: 2rem"></i>
      <span [style.color]="isCancel ? 'red' : 'black'">
        {{ isValid
        ? ('CONFIRM_VALIDORDER' | translate | async)
        : (isReject
        ? ('CONFIRM_REJECTED' | translate | async)
        : (isCancel
        ? ('CONFIRM_CANCEL' | translate | async)
        : '')) }}
      </span>
    </div>


    <input *ngIf="isValid" style="width: 100%; margin-bottom: 1.5rem;" pInputText fullWidth size="medium"
      class="form-input" status="basic" placeholder="Reference commande" [(ngModel)]="reference">

    <div *ngIf="isReject" style="height: 12em;">
      <p-dropdown fullWidth size="medium" style="width: 100%; margin-bottom: 1.5rem;" class="form-input" status="basic"
        placeholder="Motif du rejet de la commande" [options]="rejectionOptions" [(ngModel)]="selectedRejectionOption"
        placeholder="Sélectionnez une raison" (onChange)="onRejectionOptionChange($event)">
      </p-dropdown>
      <input fullWidth size="medium" *ngIf="showCustomReasonInput" style="width: 100%; margin-bottom: 1.5rem;"
        pInputText fullWidth size="medium" class="form-input" status="basic" placeholder="Entrez la raison"
        [(ngModel)]="rejectReason">
    </div>
    <input *ngIf="isCancel" style="width: 100%; margin-bottom: 1.5rem;" pInputText fullWidth size="medium"
      class="form-input" status="basic" placeholder="Motif de l'annulation de la commande" [(ngModel)]="cancelReason">


    <div class="footer">
      <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
        class="p-button bg-tertiary-400 clr-default-400 border-tertiary-400"
        (click)="isValid ? isValid = false : (isReject ? isReject = false : (isCancel ? isCancel = false : null))">
      </button>

      <button *ngIf="isValid" type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-check'"
        [disabled]="isLoadingInModal || reference.length < 6"
        class="p-button clr-default-400 bg-primary-400 border-primary-400" (click)="changeStatus()">
      </button>

      <button *ngIf="isReject" type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-check'" [disabled]="!rejectReason"
        class="p-button clr-default-400 bg-primary-400 border-primary-400" (click)="rejectOrder()">
      </button>

      <button *ngIf="isCancel" type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-check'"
        [disabled]="isLoadingInModal || !cancelReason"
        class="p-button clr-default-400 bg-primary-400 border-primary-400" (click)="cancelOrder()">
      </button>
    </div>

  </div>

</p-dialog>

<p-dialog header="{{'TITLE_MODAL_RESEND' | translate | async}} n°{{order?.appReference}}" [(visible)]="isRegisterInJDE"
  [modal]="true">

  <div class="dialog-prime">
    <div class="body">
      <i [class]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-info-circle'" style="font-size: 2rem"></i>
      {{'CONFIRM_RESEND_ORDER_TO_JDE' | translate | async }}
    </div>
    <div>{{order?.erpError?.toString()}}</div>
    <div class="footer">
      <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
        class="p-button bg-tertiary-400 border-tertiary-400" (click)="isRegisterInJDE = false">
      </button>
      <button type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-cloud-upload'" [disabled]="isLoadingInModal"
        class="p-button bg-primary-400 border-primary-400" (click)="resendInJDE()">
      </button>
    </div>
  </div>

</p-dialog>

<p-dialog header="{{'CANCEL_JDE_NUMBER' | translate | async}} n°{{order?.appReference}}" [(visible)]="isCancelJdeNber"
  [modal]="true">

  <div class="dialog-prime">
    <div class="body">
      <i [class]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-info-circle'" style="font-size: 2rem"></i>
      {{'ABOUT_TO_CANCEL_JDE_NUMBER' | translate | async }}
    </div>
    <div class="footer">
      <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
        class="p-button bg-tertiary-400 border-tertiary-400" (click)="isCancelJdeNber = false">
      </button>
      <button type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-cloud-upload'" [disabled]="isLoadingInModal"
        class="p-button bg-secondary-400 border-secondary-400" (click)="cancelJdeNumber()">
      </button>
    </div>
  </div>

</p-dialog>

<p-dialog header="Annulation pour la commande n°{{ order?.appReference }}" [(visible)]="isRequestCancelOrder"
  [modal]="true" [style]="{ width: '600px' }">
  <div class="dialog-prime">
    <div class="body">
      <i [class]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-info-circle'" style="font-size: 2rem"></i>
      <h3 style="margin: 1rem 0;">Texte de la demande</h3>
      <span style="color: red;">{{ 'REQUEST_CANCEL_MESSAGE' | translate | async }}</span>
    </div>

    <div *ngIf="order?.messageCancellation" class="request-cancel-motif">
      <input id="cancelRequestText" class="form-input" style="width: 100%; margin-bottom: 1.5rem;"
        i18n-placeholder="@@manageProductinsertdesc" [(ngModel)]="order.messageCancellation" pInputText fullWidth
        size="medium">
    </div>

    <div class="footer">
      <button type="button" pButton i18n-label="@@managePlanifclose" label="Fermer" icon="pi pi-times"
        class="p-button bg-tertiary-400 clr-default-400 border-tertiary-400" (click)="isRequestCancelOrder = false">
      </button>

      <button type="button" pButton i18n-label="@@conrfirm15" label="Confirmer"
        [icon]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-check'"
        [disabled]="isLoadingInModal || !order?.messageCancellation"
        class="p-button clr-default-400 bg-primary-400 border-primary-400" (click)="rejectOrderCancelRequest()">
      </button>
    </div>
  </div>
</p-dialog>



<p-dialog header="EXPORTEZ LA LISTE " [(visible)]="showDialogExport" [modal]="true" [style]="{width: '45%'}"
  [draggable]="true" [resizable]="true">

  <section class="body-filter">
    <form class="form body-export">

      <div class="right-section">

        <div class="input-group">
          <label for="calendar" i18n="@@historyOrders-List_Date">Date de début</label>
          <p-calendar [(ngModel)]="filterFormExport.date.start" dateFormat="dd/mm/yy" InputId="clendar"
            i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
        </div>

        <div class="input-group">
          <label for="calendar" i18n="@@historyOrders-ListEndDate">Date de fin</label>
          <p-calendar [(ngModel)]="filterFormExport.date.end" dateFormat="dd/mm/yy" InputId="clendar"
            i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
        </div>

        <div class="input-group">
          <label for="user" i18n="@@historyOrders-List_customer">Client </label>
          <p-dropdown name="userCategory" [options]="userLabels" optionLabel="label" optionValue="label"
            filterBy="label" [filter]="true" [showClear]="true" [(ngModel)]="filterFormExport.customer"
            placeholder="Sélectionner un client">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="status" i18n="@@historyOrders-ListType">Statut commande </label>
          <p-dropdown name="type" [options]="statusOrder" optionLabel="name" [(ngModel)]="filterFormExport.status"
            optionValue="code" i18n-placeholder="@@historyOrders-ListSelectStatut" [showClear]="true"
            placeholder="Sélectionner une commande" [showClear]="true">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="status" i18n="@@historyOrders-ListApp">N° commande </label>
          <p-dropdown name="type" [options]="orderNumbers" optionLabel="label"
            [(ngModel)]="filterFormExport.appReference" optionValue="label"
            i18n-placeholder="@@historyOrders-ListSelectAppRef" [showClear]="true" [filter]="true" filterBy="label"
            placeholder="Saisisser un numéro commande" [showClear]="true">
          </p-dropdown>
        </div>

      </div>
      <div class="left-section">


        <div class="input-group">
          <label for="paymentMode" i18n="@@historyOrders-List_payment_mode">Mode de paiement </label>
          <p-dropdown name="paymentMode" [options]="statusPaiement" optionLabel="name" optionValue="code"
            [filter]="true" filterBy="name" [(ngModel)]="filterFormExport.paymentMode"
            i18n-placeholder="@@historyOrders-pclh_payment_mode" [showClear]="true"
            placeholder="Sélectionner un mode de paiement">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="userCategory" i18n="@@historyOrders-List_userType">Type utilisateur </label>
          <p-dropdown name="userCategory" [options]="categoriesUsers" optionLabel="name" optionValue="code"
            [showClear]="true" [(ngModel)]="filterFormExport.userCategory" placeholder="Sélectionner une catégorie">
          </p-dropdown>
        </div>


        <div class="input-group">
          <label for="product" i18n="@@historyOrders-List_product">Produit </label>
          <p-dropdown name="userCategory" [options]="productlabels" optionLabel="label" optionValue="label"
            [filter]="true" filterBy="label" [showClear]="true" [(ngModel)]="filterFormExport.product"
            placeholder="Sélectionner un produit">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="product" i18n="@@historyOrders-List_product">Code client </label>
          <p-dropdown name="userCategory" [options]="erpSoldToIdLabels" optionLabel="label" optionValue="label"
            [filter]="true" filterBy="label" [showClear]="true" [(ngModel)]="filterFormExport.erpSoldToId"
            placeholder="Sélectionner un code client">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="loadNumber">Totale d'éléments à exporter, Maximum: {{total}}</label>
          <p-inputNumber [(ngModel)]="filterFormExport.limit" [size]="100"></p-inputNumber>

        </div>


      </div>
    </form>

    <section class="footer-export">
      <button pButton pRipple type="button" label="{{'close' | translate | async}}" icon="pi pi-times"
        class="p-button-outlined p-button-secondary" (click)="showDialogExport = false">
      </button>
      <button pButton pRipple type="button" label="{{'EXPORT' | translate |async}}" icon="pi pi-search"
        [loading]="isLoading" class="p-button-success" (click)=" exportToExcel() ; showDialogExport = false ">
      </button>
    </section>
  </section>

</p-dialog>

<p-toast></p-toast>
