{"name": "la-pasta-backoffice", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve -c=fr ", "build": "ng build", "build:staging": "ng build -c=staging", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "dev:ssr": "ng run la-pasta-backoffice:serve-ssr", "serve:ssr": "node dist/la-pasta-backoffice/server/fr/main.js", "build:ssr:prod": "ng build --configuration production && ts-node brotli_compress.ts && ng run la-pasta-backoffice:server", "build:ssr:staging": "ng build --configuration staging && ts-node brotli_compress.ts && ng run la-pasta-backoffice:server", "prerender": "ng run la-pasta-backoffice:prerender"}, "private": true, "dependencies": {"@angular/animations": "~13.3.0", "@angular/cdk": "^13.3.9", "@angular/common": "~13.3.12", "@angular/compiler": "~13.3.0", "@angular/core": "^13.3.0", "@angular/forms": "~13.3.0", "@angular/material": "^13.3.9", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/platform-server": "~13.3.0", "@angular/router": "~13.3.0", "@fullcalendar/core": "^6.0.0-beta.1", "@fullcalendar/daygrid": "^6.0.0-beta.1", "@fullcalendar/interaction": "^6.0.0-beta.1", "@fullcalendar/web-component": "^6.0.0-beta.1", "@nguniversal/express-engine": "^13.1.1", "@types/pdfmake": "^0.2.11", "@types/qrcode": "^1.5.5", "chart.js": "^4.0.1", "crypto-js": "^3.1.9-1", "domino": "^2.1.6", "express": "^4.15.2", "express-static-gzip": "^2.1.7", "file-saver": "^2.0.5", "handlebars": "^4.7.8", "localstorage-polyfill": "^1.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "ngx-export-as": "^1.13.0", "ngx-image-compress": "^13.1.16", "pdfmake": "^0.2.18", "primeicons": "^5.0.0", "primeng": "^13.4.1", "qrcode": "^1.5.4", "rxjs": "^7.5.2", "spdy": "^4.0.2", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^13.1.0", "@angular-devkit/build-angular": "~13.3.9", "@angular-eslint/builder": "13.5.0", "@angular-eslint/eslint-plugin": "13.5.0", "@angular-eslint/eslint-plugin-template": "13.5.0", "@angular-eslint/schematics": "13.5.0", "@angular-eslint/template-parser": "13.5.0", "@angular/cli": "~13.3.9", "@angular/compiler-cli": "~13.3.0", "@angular/localize": "^13.3.11", "@nguniversal/builders": "^13.1.1", "@types/crypto-js": "^4.0.1", "@types/express": "^4.17.0", "@types/jasmine": "~3.10.0", "@types/moment": "^2.13.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.27.1", "@typescript-eslint/parser": "5.27.1", "brotli": "^1.3.3", "codelyzer": "^6.0.2", "eslint": "^8.17.0", "eslint-config-google": "^0.14.0", "jasmine-core": "~4.0.0", "jasmine-spec-reporter": "^7.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "moment-locales-webpack-plugin": "^1.2.0", "node-gzip": "^1.1.2", "protractor": "^7.0.0", "ts-node": "^10.9.1", "tslib": "^2.3.0", "typescript": "~4.6.2"}}