export const regions: string[] = [
  'Adamaoua',
  'Centre',
  'Est',
  'Extrême-Nord',
  'Littoral',
  'Nord',
  'Nord-Ouest',
  'Ouest',
  'Sud',
  'Sud-Ouest',
  'TCHAD',
  'RCA'
];

export const commercialRegions: { [key: string]: string[] } = {
  LSO: ['Littoral', 'Sud-Ouest'],
  ONO: ['Ouest', 'Nord-Ouest'],
  CS: ['Centre', 'Sud'],
  'GNO 1': ['Extrême-Nord', 'Nord'],
  'GNO 2': ['Est', 'Adamaoua'],
  'EXPORT': ['TCHAD','RCA'],
};

export const cities: any = {
  Adamaoua: ['Banyo', 'Meiganga', 'Ngaoundal', 'Ngaoundéré', 'Tibati'],
  Centre: [
    'Akonolinga',
    'Bafia',
    'Eséka',
    'Makénéné',
    'Mbalmayo',
    'Nanga-Eboko',
    'Nkoteng',
    'Obala',
    'Yaoundé',
  ],
  Est: [
    'Abong-Mbang',
    'Batouri',
    'Bélabo',
    'Bertoua',
    'Garoua-Boulaï',
    'Mbandjock',
    'Yokadouma',
  ],
  'Extrême-Nord': [
    'Blangoua',
    'Bogo',
    'Gazawa',
    'Guidiguis',
    'Maroua',
    'Kaélé',
    'Kousséri',
    'Maga',
    'Mokolo',
    'Mora',
    'Yagoua',
  ],
  Littoral: [
    'Edéa',
    'Douala',
    'Limbé',
    'Loum',
    'Manjo',
    'Mbanga',
    'Melong',
    'Nkongsamba',
    'Yabassi',
    'Kribi'
  ],
  Nord: ['Figuil', 'Garoua', 'Guider', 'Pitoa', 'Tcholliré', 'Touboro'],
  'Nord-Ouest': [
    'Bali',
    'Bamenda',
    'Batibo',
    'Fundong',
    'Kumbo',
    'Ndop',
    'Nkambé',
    'Wum',
  ],
  Ouest: [
    'Bafang',
    'Bafoussam',
    'Bangangté',
    'Dschang',
    'Foumban',
    'Foumbot',
    'Kékem',
    'Koutaba',
    'Magba',
    'Mbouda',
    'Tonga',
  ],
  Sud: ['Ebolowa', 'Sangmélima'],
  'Sud-Ouest': ['Buea', 'Fontem', 'Kumba', 'Mamfé', 'Muyuka', 'Tiko', 'Tombel'],
  'TCHAD': ['N\'Djamena', 'Moundou', 'Sarh', 'Abéché', 'Kélo', 'Mongo', 'Faya-Largeau', 'Koumra'],
  'RCA': ['Garoua-Boulaï','Bangui','Carnot', 'Berbérati', 'Bimbo', 'Bouar', 'Bossangoa', 'Kaga-Bandoro', 'Sibut', 'Bria', 'Bangassou', 'Mbaïki', 'Nola', 'Paoua', 'Yalinga', 'Birao']
};

export const distrutor: any = {
  Douala: ['FOKOU Yassa', 'SOCEPROD Village', 'COGENIE Bonamoussadi'],
  Yaounde: ['FOKOU Medong', 'SOCEPROD Nglonkak', 'SOCEPROD Acia'],
};
