import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import * as moment from 'moment';
import { lastValueFrom } from 'rxjs';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';
import { Archive } from 'src/app/shared/models/archive.model';
import { QueryFilter } from 'src/app/shared/types';

import { OrderSupplier } from 'src/app/shared/models/order-supplier';
import { Product } from 'src/app/shared/models/product.model';
import { User } from 'src/app/shared/models/user.models';

@Injectable({
  providedIn: 'root'
})
export class ReportingService {
  private readonly base_url: string;
  http = inject(HttpClient);
  baseUrl = inject(BaseUrlService);
  constructor(

  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
  }

  private buildParams(filter: QueryFilter): HttpParams {
    let params = new HttpParams();

    (filter['region']) && (params = params.append('supplier.address.commercialRegion', filter['region']));
    (filter['product']) && (params = params.append('cart.items.product.label', filter['product']));
    (filter['startDate']) && (params = params.append('startDate', moment(filter['startDate']).format("YYYY-MM-DD")));
    (filter['endDate']) && (params = params.append('endDate', moment(filter['endDate']).format("YYYY-MM-DD")));
    (filter['enable'] !== undefined) && (params = params.append('enable', filter['enable']));

    return params;
  }

  async geTotalQuantityOrder(filter: QueryFilter): Promise<OrderSupplier> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<OrderSupplier>(`${this.base_url}/reporting/total-quantity`, { params })
      );
    } catch (error) {
      console.error('Error fetching total quantity:', error);
      throw error;
    }
  }



  async getTotalProduct(filter: QueryFilter): Promise<Product[]> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<Product[]>(`${this.base_url}/reporting/product-quantity`, { params })
      );
    } catch (error) {
      console.error('Error fetching total products:', error);
      throw error;
    }
  }

  async getTopDonutAnimatorsByMamiCount(filter: QueryFilter): Promise<Product[]> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<Product[]>(`${this.base_url}/reporting/top-donut-animators-by-mami-count`, { params })
      );
    } catch (error) {
      console.error('Error fetching top donut animators by mami count:', error);
      throw error;
    }
  }

  async getTotalEarnPoint(filter: QueryFilter): Promise<OrderSupplier> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<OrderSupplier>(`${this.base_url}/reporting/points-evolution`, { params })
      );
    } catch (error) {
      console.error('Error fetching earn points:', error);
      throw error;
    }
  }

  async getEvolutionByProducts(filter: QueryFilter): Promise<OrderSupplier> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<OrderSupplier>(`${this.base_url}/reporting/total-quantity-evolution`, { params })
      );
    } catch (error) {
      console.error('Error fetching product evolution:', error);
      throw error;
    }
  }

  async getDistributorVolume(filter: QueryFilter): Promise<OrderSupplier[]> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<OrderSupplier[]>(`${this.base_url}/reporting/distributor-volume`, { params })
      );
    } catch (error) {
      console.error('Error fetching distributor volume:', error);
      throw error;
    }
  }

  async getTotalPoints(filter: QueryFilter): Promise<User[]> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<User[]>(`${this.base_url}/reporting/supplier-points`, { params })
      );
    } catch (error) {
      console.error('Error fetching total points:', error);
      throw error;
    }
  }

  async getSalesEvolutionByProductSuppliers(filter: QueryFilter): Promise<OrderSupplier> {
    try {
      const params = this.buildParams(filter);
      return await lastValueFrom(
        this.http.get<OrderSupplier>(`${this.base_url}/reporting/product-evolution-supplier`, { params })
      );
    } catch (error) {
      console.error('Error fetching sales evolution:', error);
      throw error;
    }
  }

  async archivePoints(data: { session: string }): Promise<Archive> {
    try {
      return await lastValueFrom(
        this.http.patch<Archive>(`${this.base_url}/order-retaill/archive`, data)
      );
    } catch (error) {
      console.error('Error archiving points:', error);
      throw error;
    }
  }
}