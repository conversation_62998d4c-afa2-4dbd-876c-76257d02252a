type QueryOption = {
  filter?: Object,
  projection?: Object,
  limit?: number,
  offset?: number
}

declare type QueryResult = {
  status?: number;
  message?: string;
  data?: any;
  error?: any
  count?: number;
}

type categoryQueryFilter = {
  get?: string,
  limit?: number
}

declare type Discount = {
  discountCash?: number;
  deliveryDiscount?: number;
  points?: number;
}

type BreadcrumbItem = {
  label: string;
  link: string;
}

declare type Address = {
  region?: string;
  commercialRegion?: string;
  city?: string;
  district?: string;
}

declare type Point = {
  validated: number;
  unvalidated: number;
  archived: number;
}

declare type Credentials = {
  email?: string;
  password?: string;
}

export type CodeOtp = {
  value: number;
}

export type CredentialOtpDto = {
  emailOrTel: string | number;
}

declare type Data = {
  count: number;
  data?: Document[];
}

declare type Authorization = {
  label?: string;
  actions?: string[];
  enable: boolean;
}

declare type Tonnage = {
  capacity: number;
  capacityPerYear: number;
  capacityLeft: number;
}
declare type ObjectType<T> = {
  [key: string]: T
}

declare type PaymentInfo<T> = {
  [key: 'status' | string]: T | string
}

declare type QueryFilter = ObjectType<any>


export interface ImageOrderRetail {
  dataUrls: string[];
  appRef: string;
}
