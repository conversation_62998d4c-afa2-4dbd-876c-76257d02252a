import { ClientBalance } from './../../../shared/models/balance.model';
import { LocationService } from './../../../shared/services/location.service';
import { CompanyService } from 'src/app/menu-management-account/companie-account/company.service';
import { ShippingAction } from 'src/app/shared/actions/shipping-authorization.action';
import { PriceAction } from 'src/app/shared/actions/price.authorization.action';
import { CompanyCategory } from 'src/app/shared/enums/Company-category.enum';
import { UserCategory } from 'src/app/shared/enums/user-category.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { Component, Inject, OnInit } from '@angular/core';
import { CompanyEmployee, FidelityStatus } from 'src/app/shared/models/user.models';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Company } from 'src/app/shared/models/company.models';
import { t } from 'src/app/shared/functions/global.function';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { UserService } from '../../services/user.service';
import { APP_BASE_HREF } from '@angular/common';
import { Router } from '@angular/router';
import * as XLSX from 'xlsx';
import { Balance } from 'src/app/shared/models/balance.model';
import * as moment from 'moment';
import { BalanceService } from 'src/app/shared/services/balance.service';

@Component({
  selector: 'mcw-companie-account-list',
  templateUrl: './companie-account-list.component.html',
  styles: [],
  providers: [ConfirmationService, MessageService],

})
export class CompanieAccountListComponent implements OnInit {

  companies: Company[];
  currentCompany: Company;

  userCategory: UserCategory;
  usersCompany: CompanyEmployee[];
  userCompany: CompanyEmployee = new CompanyEmployee(UserCategory.CompanyUser);
  filteredCompaniesNames: any;
  filteredSoldToId: any;
  filteredShipToId: any;
  showDialogDetail: boolean;
  showDialogExport: boolean;
  dataUser: any;

  showSideBar: boolean;
  isLoading: boolean;
  isEditMode: boolean = false;
  locations: any[];
  cities: any[] = [];

  modalUserCompany: boolean;
  status = Object.keys(CompanyCategory).filter(value => isNaN(Number(value)));

  offset = 0;
  limit = 50;
  total = 0;
  companyCategory = CompanyCategory;
  modalReset: boolean;
  confirmPassword: string = '';
  regions: any[];
  statusAccount = [
    { name: 'Actif', code: true, },
    { name: 'Inactif', code: false, },
  ];

  statusAccountEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ]
  isCompanyShipping: boolean;
  isCompany: boolean;
  language = this.baseHref?.replace(/\//g, '');
  shippingAction = ShippingAction;
  priceAction = PriceAction;
  showDialogImport: boolean;
  fileName: any;
  fileSize: any;
  clientBalances: any[];
  isUploading: boolean;
  indexToRemoves: number[];
  indexDuplicates: number[];

  commercialRegions = [
    { name: 'LSO', code: 'LSO' },
    { name: 'GNO', code: 'GNO' },
    { name: 'GNO 1', code: 'GNO 1' },
    { name: 'GNO 2', code: 'GNO 2' },
    { name: 'CS', code: 'CS' },
    { name: 'ONO', code: 'ONO' },
  ];


  constructor(
    private router: Router,
    private userService: UserService,
    private balanceSrv: BalanceService,
    public commonService: CommonService,
    private locationSrv: LocationService,
    public companyService: CompanyService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) {
    this.regions = commonService.getRegions()
  }

  async ngOnInit(): Promise<void> {
    this.statusAccount = (this.language === 'en') ? this.statusAccountEn : this.statusAccount;
    await this.getCompanies();
    await this.getLocation();
    await this.getElementsForFilters();
  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getCompanies()
  }

  async getCompanies() {
    this.isLoading = true;
    const query = {
      offset: this.offset,
      limit: this.limit,
      ...this.companyService.filterlistCompanyForm
    };
    const res = await this.companyService.getAllCompanies(query);
    if (res instanceof HttpErrorResponse) {
      return this.messageService.add({
        severity: 'error',
        summary: 'Une erreur est survenue',
        detail: res.error.message
      });
    }
    this.total = res.count;
    this.companies = res.data;
    this.isLoading = false;
  }

  async getElementsForFilters() {
    const keyForFilters = ['erpSoldToId', 'erpShipToId', 'name'];
    const companiesFiltersOptions = await this.commonService.getElementForFilterByKeys('companies', { keyForFilters })
    this.filteredSoldToId = companiesFiltersOptions?.dataerpSoldToId;
    this.filteredShipToId = companiesFiltersOptions?.dataerpShipToId;
    this.filteredCompaniesNames = companiesFiltersOptions?.dataname;
  }

  async getLocation(): Promise<void> {
    this.locations = (await this.locationSrv.getLocation({})).data ?? [];
    for (const location of this.locations) {
      this.cities = this.cities.concat(location?.cities);
    }
  }

  getShippingCompamy(company: Company) {
    this.router.navigateByUrl(`account/companie-account/company-address/${company?._id}`);
  }

  async deleteCompany(company: Company): Promise<void> {
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')} ${company.enable ? await t('disable') : await t('enable')}  ${await t('CONFIRM-DISABLEUSER')} ?`,
      header: `${company.enable ? await t('disable') : await t('enable')} ${'Account'} ${company.name.split(' ')[0]}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        const res = await this.companyService.changeStatut(company);
        await this.getCompanies();
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${company.enable ? await t('disable') : await t('enable')} ${await t('Done')}` : res.data,
          detail: '' + res?.message,
        });
      },

    });
  }

  async updateUsers(): Promise<void> {
    if (!this.userCompany.email || !this.userCompany.tel) {
      this.messageService.add({
        severity: 'error',
        summary: await t('CREATE_ERROR'),
        detail: await t('EMPTY_FILL'),
      });
      return;
    }

    if (this.userCompany.email && this.userCompany.email.indexOf('@') <= -1) {
      this.messageService.add({
        severity: 'error',
        summary: await t('INVALIDE_EMAIL'),
        detail: await t('CORRECT_EMAIL'),
      });
      return;
    }
    const response = await this.userService.updateUsers(this.userCompany);
    if (response.status === 200) {
      this.messageService.add({
        severity: 'success',
        summary: await t('CREATE_ACOUNT'),
        detail: '' + response.message,
      });
      this.modalUserCompany = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response.data,
        detail: '' + response.message,
      });
    }
  }

  goTo(path: string): void {
    this.router.navigate([path]);
  }

  showModalReset(user: CompanyEmployee): void {
    this.modalReset = true;
    this.userCompany = user;
  }

  async resetpassword() {
    if (this.userCompany.password !== this.confirmPassword) {
      this.messageService.add({
        severity: 'error',
        summary: `${(this.language === 'fr') ? 'Erreur de données' : 'Data  error'}`,
        detail: await t('DIFF_PASSWORD'),
      });
      return;
    }
    const res = await this.userService.changePassword(this.userCompany);
    if (res.status == 200) {
      this.messageService.add({
        severity: 'success',
        summary: await t('CREATE_ACOUNT'),
        detail: '' + res.message,
      });
      this.modalReset = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + res.data,
        detail: '' + res.message,
      });
    }
  }

  async reset(): Promise<void> {
    this.offset = 0;
    this.companyService.filterlistCompanyForm = {
      name: '',
      erpShipToId: '',
      commercialRegion: '',
      city: '',
      erpSoldToId: '',
      enable: true,
      region: ''
    };
    await this.getCompanies();
  }

  ShowDetails(user: CompanyEmployee): void {
    this.modalUserCompany = true;
    this.userCompany = user;
  }

  getColor(category: number): string {
    if (category === CompanyCategory.Baker) { return 'bg-primary-100' }
    if (category === CompanyCategory.WholeSaler) { return 'bg-secondary-300' }
    if (category === CompanyCategory.GMS) { return 'bg-tertiary-100' }
    if (category === CompanyCategory.Group) { return 'bg-info-100' }
    if (category === CompanyCategory.Industry) { return 'bg-warning-100' }
    if (category === CompanyCategory.EXPORT) { return 'bg-warning-200' }

    return '';
  }
  
    getColorLoyaltyProgram(status: FidelityStatus.PRIVILEGE): string {
    if (status === FidelityStatus.PRIVILEGE) { return 'bg-info-400' }
    if (status === FidelityStatus.DIAMOND) { return 'bg-primary-400' }
    if (status === FidelityStatus.PREMIUM) { return 'bg-warning-600' }

    return 'bg-tertiary-400';
  }

  openAddShipping() {
    this.isCompanyShipping = true;
  }

  async changeFidelityStatus(company: Company) {
    this.currentCompany = company;
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')}
      ${company.isLoyaltyProgDistributor ? await t('FIDELITY-REMOVE') : await t('FIDELITY-ADD')} ${company?.name}
      ${company?.isLoyaltyProgDistributor ? await t('OF-THE') : await t('THE')} ${await t('FIDELITY-PROGRAM')} ?`,
      header: `${await t('FIDELITY-PROGRAM')}`,
      icon: 'pi pi-info-circle',
      key: 'fidelity-program',
      accept: async () => {
        const res = await this.companyService.update({ _id: company?._id, isLoyaltyProgDistributor: !company?.isLoyaltyProgDistributor });
        await this.getCompanies();
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${company.isLoyaltyProgDistributor ? await t('REMOVE') : await t('ADDFIDELITY')} ${await t('Done')}` : res.data,
          detail: '' + res?.message,
        });
      },

    });
    this.currentCompany = null;
  }

  async exportToExcel() {
    this.isLoading = true;
    await this.getCompanies();
    this.dataUser = this.companies.map(elt => {
      const data = {};
      data['Raison social'] = elt?.name || 'N/A';
      data['Commercial region'] = elt?.address?.region;
      data['Régistre de commerce'] = elt?.rccm;
      data['Solto'] = elt?.erpSoldToId;
      data['Région'] = elt?.address?.region;
      data['Ville'] = elt?.address?.city;
      data['Telephone'] = elt?.tel;
      data['Catégorie'] = CompanyCategory[elt?.category]
      return data;
    });
    this.commonService.exportRetriveExcelFile(this.dataUser, 'Liste_company');
    this.isLoading = false;
  }

  async onFileSelected(event: any) {
    this.resetFileUpload();
    this.isUploading = true;
    const file = event.target.files[0];
    let balances = (await this.readExcelFile(file));
    balances = this.commonService.converTValueOfKeyOfObjectInArrayToString(balances);

    const { array, indexDuplicates } = this.commonService.removeDuplicateObjectCodeInArray(balances, 'Code Client');

    balances = array;
    this.indexDuplicates = indexDuplicates;

    this.indexToRemoves = [];

    for (const [index, bal] of balances.entries()) {
      const balance: ClientBalance = {
        othersAmount: bal?.othersAmount ?? 0,
        invoicedDelayed: bal?.invoicedDelayed ?? 0,
        invoiceInProgress: bal?.invoiceInProgress ?? 0,
        creditLimit: bal?.creditLimit ?? 0,
        openOrderAmount: bal?.openOrderAmount ?? 0,
        company: { erpSoldToId: `${bal?.clientCode}` },
        date: new Date(),  // Utiliser la date courante ici
      };

      this.clientBalances.push(balance);
    }

    this.handleFileSelect(event);
    this.isUploading = false;
  }

  async saveBalancesClient() {
    this.isUploading = true;

    if (!this.clientBalances?.length)
      this.messageService.add({
        severity: 'error',
        summary: await t('DATA_ERROR'),
        detail: `Veuillez renseigner au moins une information dans le fichier`,
      });

    const response = await this.balanceSrv.saveBalanceClient(this.clientBalances);
    if (response?.status < HttpStatusCode.BadRequest) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('UPLOAD_FILE_RESULT')}`,
        detail: '' + response.message,
      });
      this.resetFileUpload();
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response?.data,
        detail: '' + response.message,
      });
    }
    this.isUploading = false;
    this.showDialogImport = false;
  }

  readExcelFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();

      fileReader.onload = (e: any) => {
        const data = e.target.result;
        const workbook = data instanceof ArrayBuffer
          ? XLSX.read(new Uint8Array(data), { type: 'array' })
          : XLSX.read(data, { type: 'binary' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const datas = XLSX.utils.sheet_to_json(worksheet);
        resolve(datas);
      };

      fileReader.onerror = (error) => {
        reject(error);
      };

      fileReader.readAsArrayBuffer(file);
    });
  }

  handleFileSelect(event: any) {
    const file = event.target.files[0]; // Objet File sélectionné

    // Récupération du nom du fichier
    this.fileName = file?.name;

    this.fileSize = this.formatFileSize(file?.size);
  }

  formatFileSize(bytes: string) {
    const units = ['octets', 'Ko', 'Mo', 'Go', 'To'];
    let l = 0, n = parseInt(bytes, 10) || 0;

    while (n >= 1024 && ++l) {
      n = n / 1024;
    }

    return `${n.toFixed(l ? 1 : 0)} ${units[l]}`;
  }

  resetFileUpload() {
    this.fileSize = null;
    this.fileName = null;
    this.clientBalances = [];
    this.indexToRemoves = [];
  }

}
