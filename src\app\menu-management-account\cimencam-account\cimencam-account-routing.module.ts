import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./cimencam-account-list/cimencam-account-list.module').then((m) => m.CimencamAccountListModule),
  },
  {
    path: 'form',
    loadChildren: () =>
      import('./cimencam-account-form/cimencam-account-form.module').then((m) => m.CimencamAccountFormModule),
  },
  {
    path: 'migrate-particular-users/:animatorId?',
    loadChildren: () =>
      import('../migrate-particular-users/migrate-particular-users.module').then((m) => m.MigrateParticularUsersModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CimencamAccountRoutingModule { }
