import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CommonService } from '../../shared/services/common.service';
import { UserService } from '../services/user.service';

import { UserCategory } from '../../shared/enums/user-category.enum';
import { HttpErrorResponse } from '@angular/common/http';
import { User } from '../../shared/models/user.models';

@Component({
  selector: 'mcw-migrate-particular-users',
  templateUrl: './migrate-particular-users.component.html',
  styleUrls: ['./migrate-particular-users.component.scss'],
  providers: [ConfirmationService, MessageService],
  encapsulation: ViewEncapsulation.None,
})
export class MigrateParticularUsersComponent implements OnInit {
  animatorId?: string;
  particularUsers: User[] = [];
  animators: User[] = [];
  allAnimators: User[] = []; // Keep original list for filtering
  selectedParticularUsers: User[] = [];
  selectedAnimators: User[] = [];
  
  // Filters
  districts: any[] = [];
  neighborhoods: any[] = [];
  selectedDistrict: string = '';
  selectedNeighborhood: string = '';
  
  // Animator filters
  animatorNameFilter: string = '';
  animatorEmailFilter: string = '';
  animatorPhoneFilter: string = '';
  
  // Multiple selection
  selectAllParticular = false;
  selectAllAnimators = false;
  showSummaryModal = false;
  
  isLoading = false;

  // Variables de pagination distinctes
  offsetMamies = 0;
  limitMamies = 50;
  totalMamies = 0;
  offsetAnimators = 0;
  limitAnimators = 50;
  totalAnimators = 0;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    public commonService: CommonService,
    public userService: UserService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService
  ) {}

  async ngOnInit(): Promise<void> {
    this.animatorId = this.route.snapshot.paramMap.get('animatorId') || undefined;
    await this.loadMamies();
    await this.loadAnimators();
    await this.getElementsForFilters();
  }



  async getElementsForFilters(): Promise<void> {
    try {
      const keyForFilters = ['address.district', 'address.neighborhood'];
      const usersInfos = await this.commonService.getElementForFilterByKeys('users', { keyForFilters });
      
      this.districts = usersInfos['dataaddress.district'] || [];
      this.neighborhoods = usersInfos['dataaddress.neighborhood'] || [];
      
      // Filter empty items
      if (this.districts && Array.isArray(this.districts)) {
        this.districts = this.districts.filter(item => item && item.label && item.label.trim() !== '');
      }
      
      if (this.neighborhoods && Array.isArray(this.neighborhoods)) {
        this.neighborhoods = this.neighborhoods.filter(item => item && item.label && item.label.trim() !== '');
      }
    } catch (error) {
      console.error('Error loading filter elements:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Error loading filter options'
      });
    }
  }

  async onDistrictChange(): Promise<void> {
    // Reset neighborhood when district changes
    this.selectedNeighborhood = '';
    
    if (this.selectedDistrict && this.selectedDistrict.trim() !== '') {
      // Filter neighborhoods based on selected district
      try {
        const filterParams = {
          category: UserCategory.Particular,
          district: this.selectedDistrict,
          limit: 1000
        };
        
        const result = await this.userService.getUsers(filterParams);
        if (!(result instanceof HttpErrorResponse)) {
          // Extract unique neighborhoods from the filtered results
          const uniqueNeighborhoods = new Set<string>();
          result.data?.forEach(user => {
            const neighborhood = (user.address as any)?.neighborhood;
            if (neighborhood && neighborhood.trim() !== '') {
              uniqueNeighborhoods.add(neighborhood);
            }
          });
          
          // Convert to the format expected by the dropdown
          this.neighborhoods = Array.from(uniqueNeighborhoods).map(neighborhood => ({
            label: neighborhood,
            value: neighborhood
          }));
        }
      } catch (error) {
        console.error('Error filtering neighborhoods:', error);
      }
    } else {
      // If no district selected, load all neighborhoods
      await this.getElementsForFilters();
    }
  }

  onNeighborhoodChange(): void {
    // This will be handled by the filter button
  }

  async resetFilters(): Promise<void> {
    this.selectedDistrict = '';
    this.selectedNeighborhood = '';
    await this.loadMamies();
    await this.getElementsForFilters();
  }

  async filterParticularUsers(): Promise<void> {
    this.isLoading = true;
    
    try {
      const filterParams: any = {
        category: UserCategory.Particular,
        offset: this.offsetMamies,
        limit: this.limitMamies,
        enable: true
      };

      // Add district filter if selected
      if (this.selectedDistrict && this.selectedDistrict.trim() !== '') {
        filterParams.district = this.selectedDistrict;
      }

      // Add neighborhood filter if selected
      if (this.selectedNeighborhood && this.selectedNeighborhood.trim() !== '') {
        filterParams.neighborhood = this.selectedNeighborhood;
      }

      console.log('Filter params:', filterParams);

      const result = await this.userService.getUsers(filterParams);

      if (result instanceof HttpErrorResponse) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: result.error.message || 'Error filtering data'
        });
      } else {
        this.particularUsers = result.data || [];
        this.totalMamies = result.count || 0;
        // Reset selections and initialize selected property
        this.selectedParticularUsers = [];
        this.particularUsers.forEach(user => (user as any).selected = false);
        this.selectAllParticular = false;
        
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: `Found ${this.particularUsers.length} particular users matching your filters`
        });
      }
    } catch (error) {
      console.error('Error filtering users:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Error filtering data'
      });
    } finally {
      this.isLoading = false;
    }
  }

  filterAnimators(): void {
    // Filter animators based on the filter inputs
    const filteredAnimators = this.allAnimators.filter(animator => {
      const nameMatch = !this.animatorNameFilter || 
        (animator.firstName && animator.firstName.toLowerCase().includes(this.animatorNameFilter.toLowerCase())) ||
        (animator.lastName && animator.lastName.toLowerCase().includes(this.animatorNameFilter.toLowerCase()));
      
      const emailMatch = !this.animatorEmailFilter || 
        (animator.email && animator.email.toLowerCase().includes(this.animatorEmailFilter.toLowerCase()));
      
      const phoneMatch = !this.animatorPhoneFilter || 
        (animator.tel && animator.tel.toString().includes(this.animatorPhoneFilter));
      
      return nameMatch && emailMatch && phoneMatch;
    });
    
    // Update the displayed animators
    this.animators = filteredAnimators;
  }

  resetAnimatorFilters(): void {
    this.animatorNameFilter = '';
    this.animatorEmailFilter = '';
    this.animatorPhoneFilter = '';
    // Reset to show all animators
    this.animators = [...this.allAnimators];
  }

  async loadAnimators(): Promise<void> {
    this.isLoading = true;
    try {
      const result = await this.userService.getUsers({
        category: UserCategory.DonutAnimator,
        offset: this.offsetAnimators,
        limit: this.limitAnimators,
        enable: true
      });
      if (!(result instanceof HttpErrorResponse)) {
        this.animators = result.data || [];
        this.totalAnimators = result.count || 0;
        // Synchronise la sélection visuelle
        this.animators.forEach(animator => (animator as any).selected = !!this.selectedAnimators.find(a => a._id === animator._id));
        this.updateSelectAllAnimatorsState();
      }
    } finally {
      this.isLoading = false;
    }
  }

  onParticularUserSelect(particularUser: User): void {
    console.log('onParticularUserSelect called for:', particularUser.firstName, 'selected:', (particularUser as any).selected);
    
    if ((particularUser as any).selected) {
      // Check if already selected
      const alreadySelected = this.selectedParticularUsers.find(p => p._id === particularUser._id);
      if (!alreadySelected) {
        this.selectedParticularUsers.push(particularUser);
        console.log('Added user to selectedParticularUsers. Total:', this.selectedParticularUsers.length);
      }
    } else {
      // Remove from selection
      this.selectedParticularUsers = this.selectedParticularUsers.filter(p => p._id !== particularUser._id);
      console.log('Removed user from selectedParticularUsers. Total:', this.selectedParticularUsers.length);
    }
  }

  onAnimatorSelect(animator: User): void {
    if ((animator as any).selected) {
      // Check if already selected
      const alreadySelected = this.selectedAnimators.find(a => a._id === animator._id);
      if (!alreadySelected) {
        this.selectedAnimators.push(animator);
      }
    } else {
      // Remove from selection
      this.selectedAnimators = this.selectedAnimators.filter(a => a._id !== animator._id);
    }
  }

  onSelectAllParticularUsers(event: any): void {
    if (event) {
      // Ajoute tous les users de la page courante à la sélection globale (sans doublons)
      this.particularUsers.forEach(user => {
        (user as any).selected = true;
        if (!this.selectedParticularUsers.find(u => u._id === user._id)) {
          this.selectedParticularUsers.push(user);
        }
      });
    } else {
      // Retire tous les users de la page courante de la sélection globale
      this.particularUsers.forEach(user => {
        (user as any).selected = false;
      });
      // Filtre la sélection globale pour ne garder que ceux qui ne sont pas sur la page courante
      const idsOnPage = this.particularUsers.map(u => u._id);
      this.selectedParticularUsers = this.selectedParticularUsers.filter(u => !idsOnPage.includes(u._id));
    }
    this.updateSelectAllMamiesState();
  }

  onSelectAllAnimators(event: any): void {
    if (event) {
      this.animators.forEach(animator => {
        (animator as any).selected = true;
        if (!this.selectedAnimators.find(a => a._id === animator._id)) {
          this.selectedAnimators.push(animator);
        }
      });
    } else {
      this.animators.forEach(animator => {
        (animator as any).selected = false;
      });
      const idsOnPage = this.animators.map(a => a._id);
      this.selectedAnimators = this.selectedAnimators.filter(a => !idsOnPage.includes(a._id));
    }
    this.updateSelectAllAnimatorsState();
  }

  getUserSelected(user: User): boolean {
    return (user as any).selected || false;
  }

  setUserSelected(user: User, selected: boolean): void {
    (user as any).selected = selected;
    if (selected) {
      if (!this.selectedParticularUsers.find(u => u._id === user._id)) {
        this.selectedParticularUsers.push(user);
      }
    } else {
      this.selectedParticularUsers = this.selectedParticularUsers.filter(u => u._id !== user._id);
    }
    this.updateSelectAllMamiesState();
    
    console.log('User selected:', user.firstName, selected);
    console.log('Total selected particular users:', this.selectedParticularUsers.length);
  }

  getAnimatorSelected(animator: User): boolean {
    return (animator as any).selected || false;
  }

  setAnimatorSelected(animator: User, selected: boolean): void {
    (animator as any).selected = selected;
    if (selected) {
      if (!this.selectedAnimators.find(a => a._id === animator._id)) {
        this.selectedAnimators.push(animator);
      }
    } else {
      this.selectedAnimators = this.selectedAnimators.filter(a => a._id !== animator._id);
    }
    this.updateSelectAllAnimatorsState();
    
    console.log('Animator selected:', animator.firstName, selected);
    console.log('Total selected animators:', this.selectedAnimators.length);
  }

  updateSelectAllStates(): void {
    // Update "Select All" for particular users
    const allParticularSelected = this.particularUsers.length > 0 && 
      this.particularUsers.every(p => (p as any).selected);
    this.selectAllParticular = allParticularSelected;

    // Update "Select All" for animators
    const allAnimatorsSelected = this.animators.length > 0 && 
      this.animators.every(a => (a as any).selected);
    this.selectAllAnimators = allAnimatorsSelected;
    
    console.log('Update Select All States:');
    console.log('- All Particular Selected:', allParticularSelected);
    console.log('- All Animators Selected:', allAnimatorsSelected);
    console.log('- Particular Users Count:', this.particularUsers.length);
    console.log('- Animators Count:', this.animators.length);
  }

  isUserSelected(user: User): boolean {
    return this.selectedParticularUsers.some(p => p._id === user._id);
  }

  isAnimatorSelected(animator: User): boolean {
    return this.selectedAnimators.some(a => a._id === animator._id);
  }

  removeParticularUser(user: User): void {
    this.selectedParticularUsers = this.selectedParticularUsers.filter(p => p._id !== user._id);
    // Update the selected property in the main list
    const userInList = this.particularUsers.find(p => p._id === user._id);
    if (userInList) {
      (userInList as any).selected = false;
    }
    this.updateSelectAllStates();
  }

  removeAnimator(animator: User): void {
    this.selectedAnimators = this.selectedAnimators.filter(a => a._id !== animator._id);
    // Update the selected property in the main list
    const animatorInList = this.animators.find(a => a._id === animator._id);
    if (animatorInList) {
      (animatorInList as any).selected = false;
    }
    this.updateSelectAllStates();
  }

  async assignParticularUsersToAnimators(): Promise<void> {
    if (this.selectedParticularUsers.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Please select at least one particular user'
      });
      return;
    }

    if (this.selectedAnimators.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Please select at least one animator'
      });
      return;
    }

    this.confirmationService.confirm({
      message: `Do you really want to assign ${this.selectedParticularUsers.length} particular user(s) to ${this.selectedAnimators.length} animator(s)?`,
      header: 'Migration Confirmation',
      icon: 'pi pi-info-circle',
      accept: async () => {
        await this.performMigration();
      }
    });
  }

  async performMigration(): Promise<void> {
    this.isLoading = true;
    try {
      if (!this.selectedParticularUsers.length || !this.selectedAnimators.length) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Sélection incomplète',
          detail: 'Veuillez sélectionner au moins un particulier et un animateur.'
        });
        this.isLoading = false;
        return;
      }
      // On prend le premier animateur sélectionné comme cible
      const targetAnimator = {
        _id: this.selectedAnimators[0]._id,
        firstName: this.selectedAnimators[0].firstName,
        category: this.selectedAnimators[0].category
      };
      const userIds = this.selectedParticularUsers.map(p => p._id);
      const result = await this.userService.migrateParticuliersToAnimator(userIds, targetAnimator);
      if (result instanceof HttpErrorResponse || result?.statusCode === 500) {
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: result?.message || result?.error?.message || 'Erreur lors de la migration.'
        });
      } else {
        this.messageService.add({
          severity: 'success',
          summary: 'Succès',
          detail: 'Migration effectuée avec succès.'
        });
        this.selectedParticularUsers = [];
        this.selectedAnimators = [];
        this.particularUsers.forEach(p => (p as any).selected = false);
        this.animators.forEach(a => (a as any).selected = false);
        await this.loadMamies();
      }
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Erreur',
        detail: 'Erreur lors de la migration.'
      });
    } finally {
      this.isLoading = false;
    }
  }

  getNeighborhood(user: User): string {
    return (user.address as any)?.neighborhood || 'N/A';
  }

  back(): void {
    this.router.navigate(['/account/pasta-account']);
  }

  // --- MAMIES ---
  async loadMamies(): Promise<void> {
    this.isLoading = true;
    try {
      const result = await this.userService.getUsers({
        category: UserCategory.Particular,
        offset: this.offsetMamies,
        limit: this.limitMamies,
        enable: true
      });
      if (!(result instanceof HttpErrorResponse)) {
        this.particularUsers = result.data || [];
        this.totalMamies = result.count || 0;
        // Synchronise la sélection visuelle
        this.particularUsers.forEach(user => (user as any).selected = !!this.selectedParticularUsers.find(u => u._id === user._id));
        this.updateSelectAllMamiesState();
      }
    } finally {
      this.isLoading = false;
    }
  }

  onMamiesPageChange(event: any): void {
    this.offsetMamies = event.first;
    this.limitMamies = event.rows;
    this.loadMamies();
  }

  updateSelectAllMamiesState(): void {
    this.selectAllParticular = this.particularUsers.length > 0 && this.particularUsers.every(u => (u as any).selected);
  }

  onAnimatorsPageChange(event: any): void {
    this.offsetAnimators = event.first;
    this.limitAnimators = event.rows;
    this.loadAnimators();
  }

  updateSelectAllAnimatorsState(): void {
    this.selectAllAnimators = this.animators.length > 0 && this.animators.every(a => (a as any).selected);
  }
} 