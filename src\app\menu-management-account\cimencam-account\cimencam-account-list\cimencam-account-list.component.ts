import { UserCategory } from 'src/app/shared/enums/user-category.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { t } from 'src/app/shared/functions/global.function';
import { HttpErrorResponse } from '@angular/common/http';
import { User } from 'src/app/shared/models/user.models';
import { UserService } from '../../services/user.service';
import { APP_BASE_HREF } from '@angular/common';

@Component({
  selector: 'mcw-cimencam-account-list',
  templateUrl: './cimencam-account-list.component.html',
  styles: [],
  providers: [ConfirmationService, MessageService],
})
export class CimencamAccountListComponent implements OnInit {

  userType: string;
  title: string = '';
  users: User[];
  user: User = { password: '' };

  filteredUsers: any;
  usersEmails: any;
  showDialogDetail: boolean;
  showDialogExport: boolean;
  dataUser: any;
  isLoading: boolean = true;
  isLoadingInModal: boolean;
  showSideBar: boolean;

  confirmPassword: string = '';
  statusAccount = [
    { name: 'Actif', code: true, },
    { name: 'Inactif', code: false, },
  ];

  statusAccountEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ]

  offset = 0;
  limit = 50;
  total = 0;
  modalReset: boolean;
  language = this.baseHref?.replace(/\//g, '');

  constructor(
    public commonService: CommonService,
    public userService: UserService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    @Inject(APP_BASE_HREF) private baseHref: string,
  ) { }

  async ngOnInit(): Promise<void> {
    this.commonService.isLoading = true;
    this.statusAccount = (this.language === 'en') ? this.statusAccountEn : this.statusAccount;
    this.userType = this.commonService.setStatusLabel(UserCategory.COMMERCIAL);
    this.getTitle(this.userType);
    await this.handleChangeTabUser();
    await this.getElementsForFilters();
    this.commonService.isLoading = false;
  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getUsers()
  }

  async getUsers(): Promise<void> {
    this.isLoading = true;
    let query: any = {
      limit: this.limit,
      offset: this.offset,
      category: UserCategory[this.userType],
      ...this.userService.filterListUser
    };
    const result = await this.userService.getUsers(query);
    if (result instanceof HttpErrorResponse) {
      return this.messageService.add({ severity: 'error', summary: 'Erreur', detail: result.error.message });
    }
    this.total = result.count;
    this.users = result?.data;
    this.isLoading = false;
  }

  async getElementsForFilters() {
    const keyForFilters = ['email'];
    const usersEmails = await this.commonService.getElementForFilterByKeys('users', { keyForFilters })
    this.usersEmails = usersEmails?.dataemail?.map(elt => { return elt.label });

  }

  searchUsers(event: any) {
    this.filteredUsers = this.usersEmails.filter((item) =>
      item.toLowerCase().includes(event.query.toLowerCase())
    ).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(event.query?.toLowerCase()) - b.toString().toLowerCase().indexOf(event.query?.toLowerCase())
    });
  }

  async deleteUser(user: User) {
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')} ${user.enable ? await t('disable') : await t('enable')} ${await t('CONFIRM-DISABLEUSER')} ?`,
      header: `${user.enable ? await t('Disable') : await t('Enable')} ${'Of'} ${user?.firstName || user?.lastName}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        this.isLoading = true;
        const res = await this.userService.deleteUser(user);
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${user.enable ? await t('Disable') : await t('Enable')} ${await t('Done')}` : res.data,
          detail: '' + res?.message,
        });
        this.isLoadingInModal = false;
        await this.getUsers();
      },
    });
  }

  async handleChangeTabUser(): Promise<void> {
    this.isLoading = true;
    if (this.userService.indexTab === 0) { this.userType = this.commonService.setStatusLabel(UserCategory.COMMERCIAL); }
    if (this.userService.indexTab === 1) { this.userType = this.commonService.setStatusLabel(UserCategory.ADMINISTRATOR); }
    if (this.userService.indexTab === 2) { this.userType = this.commonService.setStatusLabel(UserCategory.DonutAnimator); }
    this.getTitle(this.userType);
    await this.getUsers();
    this.isLoading = false;
  }

  async reset(): Promise<void> {
    this.isLoading = true;
    this.offset = 0;
    this.userService.filterListUser = { email: '', tel: '', enable: true };

    await this.getUsers();
    this.isLoading = false;
  }

  showModalReset(user: User) {
    this.modalReset = true;
    this.user = user;
  }

  async resetpassword(): Promise<boolean> {
    this.isLoadingInModal = true;
    if (this.user.password !== this.confirmPassword) {
      this.messageService.add({ severity: 'error', summary: `${await t('DATA_ERROR')}`, detail: `${await t('DIFF_PASSWORD')}` });
      return this.isLoadingInModal = false;
    }
    const res = await this.userService.changePassword(this.user);
    if (res.status == 200) {
      this.messageService.add({
        severity: 'success',
        summary: `${(this.language === 'fr') ? 'Réinitialisation effectuée' : 'Reset done'}`,
        detail: '' + res.message,
      });
      this.modalReset = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + res.data,
        detail: '' + res.message,
      });
    }
    return this.isLoadingInModal = false;
  }

  async getTitle(user: string) {
    if (user === 'COMMERCIAL') { this.title = `${await t('COMMERCIALS')}` }
    if (user === 'ADMINISTRATOR') { this.title = `${await t('ADMINISTRATORS')}` }
    if (user === 'DonutAnimator') { this.title = `${await t('DONUT')}` }
  }

  async exportToExcel() {
    this.isLoading = true;
    await this.getUsers();
    this.dataUser = this.users.map(elt => {
      const data = {};
      data['NOM'] = elt?.lastName || 'N/A';
      data['EMAIL'] = elt?.email || 'N/A';
      data['CNI'] = elt?.cni;
      data['Matricule'] = elt?.matricuel || 'N/A';
      // data['Service'] = elt?.service  as unknown as any|| 'N/A' ;
      data['TELEPHONE'] = elt?.tel || 'N/A';
      return data;
    });
    this.commonService.exportRetriveExcelFile(this.dataUser, 'Liste des utiilisateurs');
    this.isLoading = false;
  }

}
