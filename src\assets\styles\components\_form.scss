@use '../utils/mixins' as *;

.form {

  // SCSS sécurisé - ne touche QUE à la nouvelle modale d'export
  .two-columns-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;

    // Styles UNIQUEMENT pour les éléments à l'intérieur de .two-columns-container
    .column-left,
    .column-right {
      display: flex;
      flex-direction: column;

      .input-group {
        margin-bottom: 15px;

        label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
          color: #333;
        }

        // Styles PrimeNG UNIQUEMENT dans cette modale
        ::ng-deep {

          .p-calendar,
          .p-dropdown,
          .p-inputtext,
          .p-inputnumber {
            width: 100%;
          }
        }
      }
    }

    // Responsive UNIQUEMENT pour cette classe
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 10px;

      .column-left,
      .column-right {
        .input-group {
          margin-bottom: 12px;
        }
      }
    }
  }

  // Styles UNIQUEMENT pour le footer de cette modale spécifique
  .footer-export {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;

    .p-button {
      min-width: 120px;
    }

    @media (max-width: 576px) {
      flex-direction: column;

      .p-button {
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .input-group {
    margin-bottom: 24px;

    .p-password {
      display: block !important;
      width: 100%;
    }

    label {
      font-family: var(--montserrat-semibold);

      .required {
        color: var(--clr-secondary-400);
      }
    }

    input,
    .p-dropdown,
    .p-autocomplete .p-multiselect {
      background-color: var(--clr-tertiary-100);

      &:hover {
        border-color: var(--clr-primary-200);
      }

      &:focus {
        border-color: var(--clr-primary-100);
        box-shadow: none;
      }

      margin-top: 10px;
      min-height: 40px;
      border-radius: var(--border-raduis-input);
      width: 100% !important;
    }

    .p-autocomplete,
    .p-dropdown .p-multiselect {
      width: 100%;

      .p-autocomplete-panel {
        width: inherit;
        left: 0.7188px;
      }

      .p-dropdown-panel {
        width: inherit;
        left: 0.7188px;
      }
    }

  }


}

.body-export {

  @include horizontal-space-between;
  gap: 2em;

  .right-section {
    width: 47%;


  }

  .left-section {
    width: 48%;
  }
}