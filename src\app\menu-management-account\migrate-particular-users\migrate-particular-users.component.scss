.migration-page {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .migration-page-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 2rem;

        // Header
        .header-migration {
            margin-bottom: 1.5rem;

            .title-container {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin: 0;
                    color: var(--clr-primary-400);

                    i {
                        font-size: 1.5rem;
                    }
                }
            }
        }

        // Filters
        .filters-section {
            margin-bottom: 1.5rem;

            .filters-container {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
                align-items: end;

                .filter-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    min-width: 200px;

                    label {
                        font-weight: 600;
                        color: var(--clr-text-600);
                    }

                    .filter-buttons {
                        display: flex;
                        gap: 0.5rem;

                        button {
                            flex: 1;
                        }
                    }
                }
            }
        }

        // Main content
        .main-content {
            flex: 1;
            margin-bottom: 1.5rem;

            .content-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
                height: 80%;

                .left-column,
                .right-column {
                    display: flex;
                    flex-direction: column;
                    background: var(--clr-white);
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    overflow: hidden;

                    .column-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 1rem;
                        background: var(--clr-primary-50);
                        border-bottom: 1px solid var(--clr-border-200);

                        h3 {
                            margin: 0;
                            color: var(--clr-primary-600);
                            font-size: 1.1rem;
                        }

                        .count-badge {
                            background: var(--clr-primary-100);
                            color: var(--clr-primary-700);
                            padding: 0.25rem 0.75rem;
                            border-radius: 12px;
                            font-size: 0.875rem;
                            font-weight: 600;
                        }
                    }

                    .table-container {
                        flex: 1;
                        position: relative;

                        ::ng-deep .p-table {
                            .p-checkbox {
                                margin: 0;
                            }

                            th {
                                background: var(--clr-gray-50);
                                font-weight: 600;
                                color: var(--clr-text-700);
                            }

                            td {
                                padding: 0.75rem;
                                border-bottom: 1px solid var(--clr-border-100);
                            }

                            .selected-row {
                                background-color: var(--clr-primary-50);
                                border-left: 3px solid var(--clr-primary-400);

                                td {
                                    font-weight: 500;
                                }
                            }

                            // Enhanced checkbox styling
                            ::ng-deep .p-checkbox {
                                .p-checkbox-box {
                                    border: 2px solid var(--clr-border-300);
                                    border-radius: 4px;
                                    transition: all 0.2s ease;

                                    &.p-highlight {
                                        background-color: var(--clr-primary-500);
                                        border-color: var(--clr-primary-500);

                                        .p-checkbox-icon {
                                            color: white;
                                        }
                                    }

                                    &:hover {
                                        border-color: var(--clr-primary-400);
                                    }
                                }

                                .p-checkbox-icon {
                                    font-size: 0.875rem;
                                }
                            }
                        }
                    }

                    .table-container {
                        flex: 1;
                        position: relative;

                        ::ng-deep .p-table {
                            .p-checkbox {
                                margin: 0;
                            }

                            th {
                                background: var(--clr-gray-50);
                                font-weight: 600;
                                color: var(--clr-text-700);
                            }

                            td {
                                padding: 0.75rem;
                                border-bottom: 1px solid var(--clr-border-100);
                            }

                            .selected-row {
                                background-color: var(--clr-primary-50);
                                border-left: 3px solid var(--clr-primary-400);

                                td {
                                    font-weight: 500;
                                }
                            }
                        }
                    }

                    .animator-filters {
                        padding: 1rem;
                        background: var(--clr-gray-50);
                        border-bottom: 1px solid var(--clr-border-200);

                        .filter-row {
                            display: flex;
                            gap: 1rem;
                            align-items: end;
                            flex-wrap: wrap;

                            .filter-input {
                                display: flex;
                                flex-direction: column;
                                gap: 0.5rem;
                                flex: 1;
                                min-width: 150px;

                                label {
                                    font-weight: 600;
                                    color: var(--clr-text-600);
                                    font-size: 0.875rem;
                                }

                                input {
                                    padding: 0.5rem;
                                    border: 1px solid var(--clr-border-300);
                                    border-radius: 4px;
                                    font-size: 0.875rem;

                                    &:focus {
                                        outline: none;
                                        border-color: var(--clr-primary-400);
                                        box-shadow: 0 0 0 2px var(--clr-primary-100);
                                    }
                                }
                            }

                            .filter-actions {
                                display: flex;
                                align-items: end;

                                button {
                                    height: 38px;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Summary section
        .summary-section {
            margin-bottom: 1.5rem;

            .summary-container {
                background: var(--clr-white);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                overflow: hidden;

                .summary-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 1rem 1.5rem;
                    background: var(--clr-success-50);
                    border-bottom: 1px solid var(--clr-border-200);

                    h3 {
                        margin: 0;
                        color: var(--clr-success-600);
                        font-size: 1.1rem;
                    }

                    .summary-count {
                        background: var(--clr-success-100);
                        color: var(--clr-success-700);
                        padding: 0.25rem 0.75rem;
                        border-radius: 12px;
                        font-size: 0.875rem;
                        font-weight: 600;
                    }
                }

                .summary-content {
                    padding: 1.5rem;

                    .summary-group {
                        margin-bottom: 1.5rem;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        h4 {
                            margin: 0 0 1rem 0;
                            color: var(--clr-text-700);
                            font-size: 1rem;
                            font-weight: 600;
                        }

                        .selected-items {
                            display: flex;
                            flex-direction: column;
                            gap: 0.75rem;

                            .selected-item {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                padding: 0.75rem 1rem;
                                background: var(--clr-gray-50);
                                border-radius: 6px;
                                border: 1px solid var(--clr-border-200);

                                .item-info {
                                    display: flex;
                                    flex-direction: column;
                                    gap: 0.25rem;

                                    .item-name {
                                        font-weight: 600;
                                        color: var(--clr-text-700);
                                    }

                                    .item-details {
                                        font-size: 0.875rem;
                                        color: var(--clr-text-500);
                                    }
                                }

                                button {
                                    flex-shrink: 0;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Validation section
        .validation-section {
            .validation-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem 1.5rem;
                background: var(--clr-white);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                .summary-info {
                    p {
                        margin: 0;
                        color: var(--clr-text-600);
                        font-size: 0.95rem;

                        strong {
                            color: var(--clr-primary-600);
                        }
                    }
                }

                .action-buttons {
                    display: flex;
                    gap: 1rem;
                }
            }
        }
    }
}

// Responsive
@media (max-width: 1024px) {
    .migration-page {
        .migration-page-container {
            .main-content {
                .content-grid {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
            }

            .summary-section {
                .summary-container {
                    .summary-header {
                        flex-direction: column;
                        gap: 0.5rem;
                        align-items: stretch;
                    }
                }
            }

            .validation-section {
                .validation-container {
                    flex-direction: column;
                    gap: 1rem;
                    align-items: stretch;

                    .action-buttons {
                        justify-content: center;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .migration-page {
        .migration-page-container {
            padding: 0.5rem;

            .filters-section {
                .filters-container {
                    flex-direction: column;

                    .filter-group {
                        min-width: auto;
                    }
                }
            }

            .main-content {
                .content-grid {

                    .left-column,
                    .right-column {
                        .animator-filters {
                            .filter-row {
                                flex-direction: column;

                                .filter-input {
                                    min-width: auto;
                                }

                                .filter-actions {
                                    align-self: stretch;

                                    button {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .summary-section {
                .summary-container {
                    .summary-content {
                        padding: 1rem;

                        .summary-group {
                            .selected-items {
                                .selected-item {
                                    flex-direction: column;
                                    gap: 0.5rem;
                                    align-items: stretch;

                                    button {
                                        align-self: flex-end;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .validation-section {
                .validation-container {
                    .action-buttons {
                        flex-direction: column;
                    }
                }
            }
        }
    }
}

// Summary Modal Styles
.summary-modal-container {
    padding: 1rem;

    .summary-title-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid var(--surface-border);

        h3 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        .summary-count {
            background: var(--green-100);
            color: var(--green-800);
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
    }

    .summary-columns {
        display: flex;
        flex-direction: row;
        gap: 2rem;

        @media (max-width: 768px) {
            flex-direction: column;
        }

        .summary-column {
            flex: 1;
            background-color: var(--surface-card);
            padding: 1rem;
            border: 1px solid var(--surface-border);
            border-radius: 8px;

            h4 {
                margin-bottom: 1rem;
                font-size: 1.1rem;
                color: var(--text-color);
                border-bottom: 1px solid var(--surface-border);
                padding-bottom: 0.5rem;
            }

            .selected-list {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;

                .selected-card {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background: var(--gray-50);
                    border: 1px solid var(--surface-border);
                    padding: 0.75rem 1rem;
                    border-radius: 8px;
                    transition: background 0.2s ease;

                    &:hover {
                        background: var(--gray-100);
                    }

                    .card-info {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 0.25rem;

                        .card-name {
                            font-weight: 600;
                            color: var(--text-color);
                        }

                        .card-details {
                            font-size: 0.875rem;
                            color: var(--text-color-secondary);
                        }
                    }

                    button {
                        margin-left: 1rem;
                        flex-shrink: 0;
                    }
                }
            }
        }
    }

    .footer-center-container {
        display: flex;
        justify-content: center;
        padding-top: 1rem;
    }
}

// Pagination compacte dans les th
:host ::ng-deep .p-paginator {
    min-width: 120px;
    max-width: 220px;
    height: 28px;
    font-size: 12px;
    margin: 0;
    padding: 0 2px;

    .p-paginator-pages .p-paginator-page,
    .p-paginator-next,
    .p-paginator-prev,
    .p-paginator-first,
    .p-paginator-last {
        min-width: 22px;
        height: 22px;
        font-size: 12px;
        margin: 0 1px;
        padding: 0 2px;
        border-radius: 3px;
    }

    .p-dropdown,
    .p-inputtext {
        font-size: 12px;
        height: 22px;
        min-width: 40px;
        padding: 0 2px;
    }

    .p-paginator-current {
        font-size: 11px;
        padding: 0 2px;
    }
}