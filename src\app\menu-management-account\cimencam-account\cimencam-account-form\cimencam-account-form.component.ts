import {
  EmployeeType,
  UserCategory,
} from 'src/app/shared/enums/user-category.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { Component, Inject, OnInit } from '@angular/core';
import { EmployeeCimencam, Logo, LogoType, User } from 'src/app/shared/models/user.models';
import { Company } from 'src/app/shared/models/company.models';
import { Address, Authorization } from 'src/app/shared/types';
import { t } from 'src/app/shared/functions/global.function';
import { APP_BASE_HREF, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { MessageService } from 'primeng/api';
import { HttpStatusCode } from '@angular/common/http';
import { CompanyService } from '../../companie-account/company.service';
import { NgxImageCompressService } from 'ngx-image-compress';
import { LogoService } from 'src/app/shared/services/logo.service';

@Component({
  selector: 'mcw-cimencam-account-form',
  templateUrl: './cimencam-account-form.component.html',
  styles: [],
})
export class CimencamAccountFormComponent implements OnInit {
  user: EmployeeCimencam = new EmployeeCimencam(UserCategory.EmployeeEntity);
  address: Address;
  company: Company;
  title: string = '';
  commercialRegion: any;
  companies: Company[];

  isEditMode: boolean = true;
  regions: any[];

  authorizations: Authorization[] = [];
  userCategory: UserCategory;

  userType: string;
  isLoading: boolean;
  directions: any[];
  services: any[];
  posts: any[];
  maxSizeImage = 7302567;
  maxFileSize = 1000000;



  statusEmployee: { name: string; value: boolean }[] = [
    { name: 'Rétraité', value: true },
    { name: 'Actif', value: false },
  ];

  TypeEmployee: { name: string; value: EmployeeType }[] = [
    { name: 'DRH', value: EmployeeType.DRH },
    { name: 'Coordo RH', value: EmployeeType.CORDO_RH },
  ];
  language = this.baseHref?.replace(/\//g, '');

  companyUserCategory: { name: ''; code: 0 };
  cities: any[] = [];
  imageBlobLogo: any;
  logo: Logo;
  imageBlobSignature: any;
  signature: Logo;
  othersClients: User[];
  clientsAssociates: User[] | Company[];

  constructor(
    private router: Router,
    private location: Location,
    private logoSrv: LogoService,
    private route: ActivatedRoute,
    private userService: UserService,
    public commonService: CommonService,
    private messageService: MessageService,
    private companiesService: CompanyService,
    private imageCompress: NgxImageCompressService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) {
    this.userType = this.route.snapshot.params['userType'];
    this.getTitle(this.userType);
    this.address = commonService.initAddress();
    this.regions = commonService.getRegions();
  }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    this.address = this.user.address;
    this.user = this.route.snapshot.params['id']
      ? await this.findUser(this.route.snapshot.params['id'])
      : new EmployeeCimencam(
        UserCategory[`${this.route.snapshot.params['userType']}`]
      );

    // if(this.isEditMode){
    //   this.cities = this.commonService.getCities(this.user.address.region);
    // }

    if (this.router.url.indexOf('show') > -1) {
      this.isEditMode = false;
    }
    await this.getAuthorization();
    this.companies = (await this.companiesService.getAllCompanies({ projection: 'name,category,enable' }))?.data;
    this.othersClients = (await this.userService.getUsers({
      category: JSON.stringify({ $in: [UserCategory.Particular] }),
      projection: 'firstName,lastName,category,email,tel,enable',
    }) as { data: User[], count: any })?.data;
    this.othersClients = this.othersClients.map(other => ({ ...other, name: other.email }));
    console.log(this.othersClients);

    this.clientsAssociates = this.companies.concat(this.othersClients as any);
    await this.getSignature();
    this.isLoading = false;
  }

  async getElementsForFilters() {
    const keyForFilters = ['name', 'reference'];
    const promoters = await this.commonService.getElementForFilterByKeys('companies', { keyForFilters })
  }

  regionChange(event: any): string[] {
    const commercialRegion = Object.entries(
      this.commonService.commercialRegions
    ).find(([key, value]) => value.includes(event.value))?.[0];

    if (commercialRegion) {
      this.user.address.commercialRegion = commercialRegion;
    }

    this.cities = this.commonService.getCities(event.value);
    return this.cities;
  }

  async findUser(idUser: string): Promise<EmployeeCimencam> {
    return (this.user = (await this.userService.getUser(
      idUser
    )) as EmployeeCimencam);
  }

  async getAuthorization(): Promise<void> {
    const res = await this.userService.getAuthorization();
    this.authorizations = res;
  }

  async getSignature() {
    const data = (await this.logoSrv.getLogoCompany({ 'erpSoldToId': this.company?.erpSoldToId }))?.data
    this.signature = data.find(el => el?.logoType === LogoType.SIGNATURE);
  }

  manageAction(e: any, action: string) {
    e.checked
      ? this.user?.authorizations?.push(action)
      : (this.user.authorizations = this.user?.authorizations?.filter(
        (item) => item !== action
      ));
  }

  addAllAuthorizations(e: any) {
    if (e.checked) {
      let allAuthorizations = [];
      this.authorizations.forEach(
        (elt) => (allAuthorizations = allAuthorizations.concat(elt.actions))
      );
      this.user.authorizations = allAuthorizations;
    } else {
      this.user.authorizations = [];
    }
  }

  async createAccount(): Promise<boolean> {
    this.isLoading = true;
    if ('store' in this.user) {
      this.user.store = {
        _id: this.user.store._id,
        label: this.user.store.label,
      };
    }
    const object = { ...this.user };
    delete object.address;
    delete object.nui;
    delete object.lastName;
    delete object.direction;
    delete object.service;
    delete object.position;
    delete object.isRetired;
    const validationFields = this.commonService.verifyAllFieldsForm(object);

    if (validationFields) {
      this.messageService.add({
        severity: 'error',
        summary: await t('DATA_ERROR'),
        detail: validationFields as string,
      });
      return (this.isLoading = false);
    }
    let response = await this.userService.createUser(this.user);
    if (response.status === 201) {
      this.messageService.add({
        severity: 'success',
        sticky: true,
        key: 'confirm',
        summary: `${await t('CREATE_ACOUNT')}`,
        detail: '' + response?.message,
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response?.data,
        detail: '' + response?.message,
      });
    }
    return (this.isLoading = false);
  }

  async updateAccount(): Promise<boolean> {
    this.isLoading = true;
    if (!this.user?.email || !this.user?.tel) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CREATE_ERROR')}`,
        detail: `${await t('EMPTY_FILL')}`,
      });
      return (this.isLoading = false);
    }

    if (this.user.email && this.user.email.indexOf('@') <= -1) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('INVALIDE_EMAIL')}`,
        detail: `${await t('CORRECT_EMAIL')}`,
      });
      return (this.isLoading = false);
    }
    this.address = this.user.address;
    this.user.tel = Number(this.user.tel);
    const response = await this.userService.updateUsers(this.user);

    if (response.status < HttpStatusCode.BadRequest) {
      this.messageService.add({
        severity: 'success',
        sticky: true,
        key: 'confirm',
        summary: response?.data,
        detail: '' + response?.message,
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response.data,
        detail: '' + response.message,
      });
    }
    return (this.isLoading = false);
  }

  clearToast(): void {
    this.messageService.clear();
  }

  back(): void {
    this.location.back();
  }

  async verifyFillTel(): Promise<boolean> {
    if (
      !/\d{9}|\+\d{1} \(\d{3}\) \d{3}-\d{4}/gi.test(`${this.company?.tel}`) ||
      `${this.company?.tel}`.length !== 9
    ) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('DATA_ERROR')}`,
        detail: `${await t('CORRECT_SIZE')}`,
      });
      return false;
    }
    return true;
  }

  async getTitle(user: string) {
    if (user === 'COMMERCIAL') {
      this.title = `${await t('COMMERCIALS')}`;
    }
    if (user === 'ADMINISTRATOR') {
      this.title = `${await t('ADMINISTRATORS')}`;
    }
    if (user === 'DonutAnimator') { this.title = `${await t('DONUT')}` }
  }

  async uploadLogoCompany(value: string): Promise<void> {
    const logo: Logo = {
      user: {
        _id: this.user._id,
        firstName: this.user?.firstName,
        lastName: this.user?.lastName,
        category: this.user.category,
      },
      value: value,
      logoType: LogoType.SIGNATURE
    }
    const response = await this.logoSrv.saveLogo(logo);

    if (response.status < HttpStatusCode.BadGateway) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('LOGO_ACCOUNT_SAVE')}`,
        detail: '' + response?.message,
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response.data,
        detail: '' + response.message,
      });
    }
  }

  async showImageSelected(event: any, keyValue: string) {
    const imageBlob = event?.files[0]?.objectURL?.changingThisBreaksApplicationSecurity;

    const reader = new FileReader();
    const blob = await fetch(imageBlob).then(r => r.blob());

    reader.readAsDataURL(blob);
    reader.onloadend = async () => {
      const image = await this.imageCompress.compressFile(reader.result as string, 0);

      if (this.imageCompress.byteCount(image) > this.maxSizeImage) {
        return this.messageService.add({
          severity: 'warn',
          summary: '',
          detail: `${await t('IMAGE_EXCEED')}`,
        });
      }

      this[keyValue] = image;
      console.log(this[keyValue]);

    };

  }
}
