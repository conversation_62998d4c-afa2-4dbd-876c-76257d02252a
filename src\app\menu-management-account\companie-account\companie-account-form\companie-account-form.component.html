<div class="form-user-page page-container container">
  <div class="form-user-page-container">
    <div class="body">
      <div class="title-container">
        <i class="pi pi-arrow-left" (click)="back()"></i>
        <h2 class="title" i18n="@@cpnyForm">
          {{ company?._id? (isEditMode
          ? ('CHANGE_CompanyTITLE'| translate | async ) +" " +company.name
          : ('Detail_CompanyTITLE'| translate | async ) + " " +company.name)
          : ('Add_CompanyTITLE'| translate | async) }}
        </h2>
      </div>
      <p-card class="card-group">
        <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>

        <div class="card-header">
          <div class="input-select">
            <label for="type" *ngIf="isEditMode" i18n="@@cpnyFormAccount">Sélectionnez le type de compte </label>
            <p-dropdown name="type" styleClass="dropdown-toggle" [options]="category" optionLabel="name"
              [readonly]="!isEditMode" optionValue="code" [(ngModel)]="company.category" [readonly]="!isEditMode"
              i18n-placeholder="@@cpnyFormType"
              placeholder="{{commonService.setStatusCompanyLabel(company?.category) || 'Sélectionner un type'}}">
            </p-dropdown>
          </div>
          <div class="logo">
            <div class="block-logo left">
              <div class="label">Logo</div>
              <div class="img-logo">
                <!-- <div class="img"[ngStyle]="getImgStyle(company?.logo ?? imageBlob)"></div> -->
                <img [src]="imageBlobLogo ?? logo?.value" alt="" />
                <p-fileUpload mode="basic" name="demo[]" accept="image/*" [maxFileSize]="maxFileSize"
                  (onSelect)="showImageSelected($event, 'imageBlobLogo')"
                  (onBeforeUpload)="uploadLogoCompany(imageBlobLogo, 1)">
                </p-fileUpload>
              </div>
            </div>

            <div class="block-logo right">
              <div class="label">Signature</div>
              <div class="img-logo">
                <img [src]="imageBlobSignature ?? signature?.value" alt="" />
                <p-fileUpload mode="basic" name="demo[]" accept="image/*" [maxFileSize]="maxFileSize"
                  (onSelect)="showImageSelected($event, 'imageBlobSignature')"
                  (onBeforeUpload)="uploadLogoCompany(imageBlobSignature, 2)">
                </p-fileUpload>
              </div>
            </div>
          </div>

          <!-- <div class="signature">
            <div class="label">Signature</div>
            <div class="block-logo">
              <img [src]="company?.logo ?? imageBlob" alt="" />
              <p-fileUpload mode="basic" accept="image/*" [maxFileSize]="maxFileSize"
                (onSelect)="showImageSelected($event)" (onBeforeUpload)="uploadLogoCompany(this.imageBlob, 2)">
              </p-fileUpload>
            </div>
          </div> -->
        </div>

        <p-tabView class="p-tabview-container" *ngIf="company?.category">
          <p-tabPanel i18n-header="@@cpnyInfoForm" header="Informations Compagnie" leftIcon="pi pi-bookmark">
            <form class="card-body form display-grid">
              <div class="first-group">
                <div class="input-group">
                  <label for="name" i18n="@@cpnyFormReason">Raison sociale<span class="required">*</span> </label>
                  <input name="name" type="text" [readonly]="!isEditMode" [(ngModel)]="company.name" pInputText
                    placeholder="{{company.name}}" />
                </div>
                <div class="input-group">
                  <label i18n="@@cpnyFormStatus" for="precompteRate">Status fiscal </label>
                  <p-dropdown name="precompteRate" [(ngModel)]="company.precompteRate" [options]="precompteRate"
                    [readonly]="!isEditMode" optionValue="value" optionLabel="label"
                    i18n-placeholder="@@cpnyFormselectStatus"
                    placeholder="{{company?.precompteRate|| ('PRECOMPTE'| translate | async)}}"></p-dropdown>

                </div>

                <div class="input-group">
                  <label i18n="@@cpnyFormNumber" for="num">Numéro de contribuable</label>
                  <input name="num" type="text" [(ngModel)]="company.nui" pInputText [readonly]="!isEditMode"
                    placeholder="{{company?.nui}}" />
                </div>

                <div class="input-group">
                  <label for="register" i18n="@@cpnyFormRegister">Régistre de commerce</label>
                  <input name="register" type="text" [(ngModel)]="company.rccm" pInputText [readonly]="!isEditMode"
                    placeholder="{{company?.rccm}}" />
                </div>
                <div class="input-group">
                  <label for="tel" i18n="@@cpnyFormTel">Téléphone <span class="required">*</span></label>
                  <input name="tel" type="tel" [(ngModel)]="company.tel" [readonly]="!isEditMode" pInputText
                    placeholder="{{company?.tel}}" />
                </div>
              </div>

              <div class="second-group">

                <div class="input-group">
                  <label for="solto">SolTo ID (Identifiant du client dans X3)<span class="required">*</span></label>
                  <input name="solto" type="text" [(ngModel)]="company.erpSoldToId" pInputText [readonly]="!isEditMode"
                    placeholder="{{company?.erpSoldToId}}" />
                </div>

                <div class="input-group">
                  <label for="shipto">ShipTo ID (Adresse de livraison par défaut)</label>
                  <input name="shipto" type="text" [(ngModel)]="company.erpShipToId" pInputText [readonly]="!isEditMode"
                    placeholder="{{company?.erpShipToId}}" />
                </div>

                <div class="input-group">
                  <label for="shipto" i18n="@@cpnyFormCommercial">Commercial Associé</label>
                  <p-dropdown name="precompteRate" [(ngModel)]="company.associatedCommercial" [options]="commercials"
                    [readonly]="!isEditMode" optionLabel="email" filterBy="email" [filter]="true"
                    placeholder="{{company?.associatedCommercial?.email || ('COMPANY_COMMERCIAL'| translate | async) }}"></p-dropdown>
                </div>

                <!-- company-detail.component.html -->
                <div class="input-group">
                  <label for="option" class="form-label">Options</label>
                  <p-multiSelect #optionSelect [styleClass]="'w-full'" name="option" [(ngModel)]="selectedOptionIds"
                    [options]="options" optionLabel="name" optionValue="_id" [showClear]="true" [disabled]="!isEditMode"
                    placeholder="{{('SELECT_OPTION' | translate) | async}}" (onChange)="onOptionChange($event)"
                    [style]="{'width':'100%','margin-top':'10px','min-height': '40px'}" [required]="false">
                    <ng-template pTemplate="selectedItems">
                      <div class="selected-items">
                        {{ getSelectedOptionsNames() ?? ('NO_OPTION_SELECTED' | translate) }}
                      </div>
                    </ng-template>
                  </p-multiSelect>
                </div>

                <div class="input-group" *ngIf="showGlobalShippingAddress">
                  <label for="globalShippingAddress" class="form-label"> Adresse associée à l'option</label>
                  <p-multiSelect #shippingSelect [styleClass]="'w-full'" name="globalShippingAddress"
                    [(ngModel)]="selectedGlobalShippingAddress" [options]="globalShippingAddresses" optionLabel="label"
                    optionValue="_id" [showClear]="true" [disabled]="!isEditMode"
                    [placeholder]="'SELECT_GLOBAL_SHIPPING_ADDRESS' | translate | async"
                    [style]="{'width':'100%','margin-top':'10px','min-height': '40px'}"
                    (onChange)="onGlobalShippingAddressChange($event)" [required]="false">
                    <ng-template pTemplate="selectedItems">
                      <div class="selected-items">
                        {{ getSelectedShippingAddressesNames() || ('NO_ADDRESS_SELECTED' | translate) }}
                      </div>
                    </ng-template>
                  </p-multiSelect>
                </div>


                <div class="input-group" *ngIf="company?.address?.commercialRegion">
                  <label label="commercial-region" i18n="@@formRegion">Région Commercial </label>
                  <input name="commercial-region" [readonly]="!isEditMode" type="text"
                    [(ngModel)]="company.address.commercialRegion" pInputText>
                </div>

                <div class="input-group">
                  <label for="region" i18n="@@cpnyFormRegion">Région </label>
                  <p-dropdown name="region" [(ngModel)]="company.address.region" (onChange)="regionChange($event)"
                    [options]="regions" [readonly]="!isEditMode" i18n-placeholder="@@cpnyFormSelectRegion"
                    placeholder="{{company?.address?.region || ('manageStore_Region'| translate | async)}}">
                  </p-dropdown>
                </div>

                <div class="input-group">
                  <label for="city" i18n="@@cpnyFormCity">Ville </label>
                  <p-dropdown name="city" [(ngModel)]="company.address.city" [options]="cities"
                    i18n-emptyMessage="@@formEmptytext7" sélectionner="Veuillez choisir la région"
                    placeholder="{{company?.address?.city || ('manageStore_city'| translate | async)}} "
                    [disabled]="!company.address.region"></p-dropdown>

                </div>

                <div class="input-group">
                  <label for="city" i18n=@@manageShippingsdistrict>Quartier </label>
                  <input name="shipto" type="text" [(ngModel)]="company.address.district" pInputText
                    [readonly]="!isEditMode" placeholder="{{company?.address?.district}}" />
                </div>
              </div>
            </form>
            <div class="footer" *ngIf="isEditMode">
              <div class="footer-container">
                <button class="btn btn-tertiary btn-icon-inline" (click)="back()" i18n="@@dialogOut">
                  <i class="pi pi-times"></i>Annuler
                </button>
                <button class="btn btn-primary btn-icon-inline" [disabled]="isLoading"
                  (click)="this.company._id ? updateCompany(): createCompany()" i18n="@@dialogSave">
                  <i [class]="isLoading? 'pi pi-spin pi-spinner' :'pi pi-check'"></i>Enregistrer
                </button>
              </div>
            </div>
          </p-tabPanel>

          <p-tabPanel i18n-header="@@cpnyFormtabset2" header="Compte(s) utilisateur(s)" leftIcon="pi pi-users">
            <div class="header-list" *ngIf="isEditMode || !company._id">
              <p-dropdown [options]="existingUsers" [(ngModel)]="selectedExistingUser"
                (onChange)="onExistingUserSelected()" optionLabel="email" filterBy="email" [filter]="true"
                [style]="{'width':'98%','min-height': '40px', 'margin-bottom':'15px'}"
                placeholder="Sélectionnez des utilisateurs existants">
                <ng-template let-user pTemplate="item">
                  <div>{{user.email}} - {{user.firstName}} {{user.lastName}}</div>
                </ng-template>
              </p-dropdown>
              <div class="btn btn-outline-success btn-icon" (click)="openAddUserModal()">
                <i class="pi pi-user-plus"></i>
              </div>
            </div>
            <p-table [value]="usersCompany" responsiveLayout="scroll">
              <ng-template pTemplate="header">
                <tr>
                  <th i18n="@@form-Label">Nom</th>
                  <th>Email</th>
                  <th>Tel</th>
                  <th>Action</th>
          
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td>{{user?.lastName | truncateString: 15}}</td>
                  <td>{{user?.email }}</td>
                  <td>{{user?.tel}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)"></button>
                    <p-overlayPanel #op [style]="{width: '100px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-edit" i18n-ptooltip="@@listUpdate" pTooltip="Modification"
                            tooltipPosition="top" (click)="ShowEditUser(user, i)">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div *ngIf="!isCreationMode" class="btn btn-icon btn-icon-delete" (click)="deleteUserCompany(user)"
                            i18n-ptooltip="@@listDisable" pTooltip="Désactivation" tooltipPosition="top">
                            <i class="pi pi-eye"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </p-tabPanel>

          <p-tabPanel i18n-header="@@cpnyFormtabset3" header="Adresse(s) utilisateur(s)" leftIcon="pi pi-map-marker"
            *ngIf="company?._id">
            <div class="header-list" *ngIf="isEditMode">
              <div class="btn btn-outline-success btn-icon" (click)="goTo(company)">
                <i class="pi pi-user-plus"></i>
              </div>
              <!-- <div class="btn btn-outline-success btn-icon" (click)="openAddShipping(company?._id)">
                <i class="pi pi-user-plus"></i>
              </div> -->
            </div>
            <p-table [value]="companyShippings" responsiveLayout="scroll">
              <ng-template pTemplate="header">
                <tr>
                  <th i18n="@@cpnyFormDesc">Description(s)</th>
                  <th i18n="@@cpnyFormStore">Usine(s)</th>
                  <th>ShipTo</th>
                  <!-- <th>Action</th> -->
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-companyShipping>
                <tr>
                  <td>{{companyShipping?.endRef | truncateString:20}}</td>
                  <td>{{companyShipping?.startRef?.label | truncateString:25}}</td>
                  <td>{{companyShipping?.erpShipToId}}</td>
                  <!-- <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-edit" pTooltip="Modification" tooltipPosition="top"
                            (click)="ShowEditUser(companyShipping)">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-delete" (click)="deleteCompanyShipping(companyShipping)"
                            pTooltip="Désactivation" tooltipPosition="top">
                            <i class="pi pi-eye"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td> -->
                </tr>
              </ng-template>
            </p-table>
          </p-tabPanel>

          <p-tabPanel i18n-header="@@cpnyFormtabset4" header="Solde du compte" leftIcon="pi pi-dollar"
            *ngIf="company?._id && !isEditMode">

            <h2 class="balance-date">
              Solde du compte le {{ balance?.date | date:'dd-MM-yyyy':'':'fr-FR' }} - {{ balance?.date |
              date:'HH:mm':'':'fr-FR' }}
            </h2>
            <h1 class="balance-amount">{{ availableBalance | number:'':'fr-FR'}} XAF</h1>
            <div class="points">Points: {{company?.points || 'N/A'}}</div><br>
            <div class="balance-group">
              <div class="form-group">
                <div class="field-info">
                  <label i18n="@@limitCredit">Plafond de crédit:</label>
                  <div>{{ balance?.creditLimit != null ? (balance.creditLimit | currency:'':'XAF':'':'fr') : 'Non renseignée' }}</div>
                </div>
              </div>
              <div class="form-group">
                <div class="field-info">
                  <label i18n="@@paiementEn">Total en cours:</label>
                  <div> {{(balance?.openOrderAmount | currency:'':'XAF':'':'fr') ?? 'Non renseigné'}}</div>
                </div>
              </div>
              <div class="form-group">
                <div class="field-info">
                  <label i18n="@@getClient">Commandes ouvertes:</label>
                  <div> {{(balance?.invoiceInProgress | currency:'':'XAF':'':'fr') ?? 'Non renseigné'}}</div>
                </div>
              </div>
              <div class="form-group">
                <div class="field-info">
                  <label i18n="@@orderBill">Autres notes de débit</label>
                  <div> {{(balance?.othersAmount | currency:'':'XAF':'':'fr') ?? 'Non renseignée'}}</div>
                </div>
              </div>
              <div class="form-group">
                <div class="field-info">
                  <label i18n="@@orderBill">Factures Échues</label>
                  <div> {{(balance?.invoicedDelayed | currency:'':'XAF':'':'fr') ?? 'Non renseignée'}}</div>
                </div>
              </div>
              <!-- <div class="form-group">
                <div class="field-info">
                  <label i18n="@@orderNotBill">Commandes non facturées</label>
                  <div> {{balance?.OpenorderAmount | currency:'':'XAF':'':'fr' || 'Non renseignée'}}</div>
                </div>
              </div> -->
            </div>
          </p-tabPanel>
        </p-tabView>
      </p-card>
    </div>
  </div>
</div>

<p-confirmDialog i18n-acceptLabel="@@listDisable4" acceptLabel="Désactivation" acceptIcon="" [style]="{width: '30vw'}"
  i18n-rejectLabel="@@listReject4" rejectLabel="Annuler" rejectButtonStyleClass="bg-tertiary-400 border-tertiary-400"
  acceptButtonStyleClass="bg-secondary-400 border-secondary-400" defaultFocus="none"></p-confirmDialog>
<p-toast position="top-right"></p-toast>


<p-dialog i18n-header="@@titleCmpny"
  header="{{isEditUser? ('UPDATE_USERTITLE' |translate | async) +user?.lastName.split(' ')[0] : ('ADD_USERTITLE' |translate | async)}}"
  [modal]="true" [(visible)]="modalUserCompany" [breakpoints]="{'960px': '75vw'}" [style]="{width: '35vw'}">
  <div class="basic-modal modal-container-content">
    <div class="input-group">
      <label for="tel" i18n="@@listName6">Nom <span class="required clr-secondary-500">*</span></label>
      <input type="text" [(ngModel)]="user.lastName" pInputText i18n-placeholder="@@listUserName7"
        placeholder="Nom utilisateur*" />
    </div>
    <div class="input-group">

      <label for="tel" i18n="@@listfirstName3">Prénom <span class="required clr-secondary-500">*</span></label>
      <input type="text" [(ngModel)]="user.firstName" pInputText i18n-placeholder="@@listUserLastName8"
        placeholder="Prénom utilisateur" />
    </div>
    <div class="input-group">
      <label for="tel">Email <span class="required clr-secondary-500">*</span></label>
      <input type="Email" [(ngModel)]="user.email" pInputText placeholder="email" required />

    </div>
    <div class="input-group">
      <label for="tel" i18n="@@listTel">Téléphone <span class="required clr-secondary-500">*</span></label>
      <input type="tel" [(ngModel)]="user.tel" pInputText i18n-placeholder="@@listTel."
        placeholder="Numéro de télephone" />
    </div>
    <div class="input-group" *ngIf="!user?._id">
      <label for="tel" i18n="@@list_PSWD">Mot de passe <span class="required clr-secondary-500">*</span></label>
      <p-password [toggleMask]="true" [(ngModel)]="user.password" placeholder="Mot de passe"></p-password>
    </div>

  </div>
  <ng-template pTemplate="footer">
    <div class="footer-center-container">
      <p-button icon="pi pi-times" (click)="modalUserCompany=false" i18n-label="@@listOut5" label="Annuler"
        styleClass="bg-tertiary-400 border-tertiary-400">
      </p-button>
      <p-button [icon]="isLoadingInModal? 'pi pi-spin pi-spinner' :'pi pi-check'"
        (click)="isEditUser? updateUsers() : addUserCompany()" i18n-label="@@listRegistered" label="Enregistrer"
        styleClass="bg-primary-400 border-primary-400"></p-button>
    </div>
  </ng-template>
</p-dialog>

<div class="add-dialog shippingDialog">
  <p-dialog position="center" [(visible)]="isCompanyShipping" [modal]="true">
    <div class="body">
      <div class="title">
        <div class="title-text" i18n="@@AddRoad">
          Ajout trajet
        </div>
        <i class="pi pi-times positionIcon " (click)="close()"></i>
      </div>
      <p-card class="card-group">
        <div class="insert-info">
          <div class="add-container">
            <div class="product-form">
              <label for="otherTrainingType" i18n="@@listName1">Nom</label>
              <input pInputText fullWidth size="medium" type="text" class="form-input" [(ngModel)]="shipping.label"
                i18n-placeholder="@@customerName" placeholder="{{shipping?.label || 'Nom du client'}}">
            </div>
            <div class="product-form">
              <label for="otherTrainingType" i18n="@@shippingStarted">Départ</label>
              <p-dropdown styleClass="form-input" [options]="stores" [(ngModel)]="shipping.startRef" optionLabel="label"
                [showClear]="true" i18n-placeholder="@@shipping-Start"
                placeholder="{{shipping?.startRef?.label || 'Point de depart'}}" [filter]="true" class="form-input"
                filterValue="label">
              </p-dropdown>
            </div>
            <div class="product-form">
              <label for="otherTrainingType" i18n="@@shippingEnd3">Arrivée</label>
              <input pInputText fullWidth size="medium" type="text" class="form-input" [(ngModel)]="shipping.endRef"
                i18n-placeholder="@@shipping-End" placeholder="{{shipping?.endRef|| 'Entrer une destination'}}">
            </div>

            <div class="product-form">
              <label for="otherTrainingType" i18n="@@shippingAmount">Montant</label>
              <input pInputText fullWidth size="medium" type="number" class="form-input" [(ngModel)]="shipping.amount"
                [readonly]="isEdit" placeholder="Entrer un montant">
            </div>

            <div class="product-form">
              <label for="otherTrainingType">ShipTo</label>
              <input pInputText fullWidth size="medium" type="number" class="form-input"
                [(ngModel)]="shipping.erpShipToId" [readonly]="isEdit" placeholder="Entrer un shipTo">
            </div>

            <div class="product-form">
              <label for="otherTrainingType" i18n="@@shippingDescShipTo">Description ShipTo</label>
              <input pInputText fullWidth size="medium" type="text" class="form-input"
                [(ngModel)]="shipping.erpShipToDesc" [readonly]="isEdit" placeholder="Entrer un shipTo description">
            </div>
          </div>
          <div class="product-button">
            <button nbButton class="btn btn-tertiary iconsBtn button" status="basic" [disabled]="isLoading"
              (click)="close()" i18n="@@listOut">
              <i class="pi pi-times"></i>
              Annuler
            </button>

            <button nbButton outline status="success" class="btn btn-primary iconsBtn button" [disabled]="isLoading"
              (click)='addShipping()' i18n="@@list-modification">
              <i class="pi" [ngClass]="isLoading ? 'pi-spin pi-spinner' : 'pi-check'">
              </i>
              {{shipping?._id? 'Modifier' : 'Enregistrer'}}
            </button>
          </div>
        </div>
      </p-card>
    </div>
  </p-dialog>
</div>

<p-toast position="bottom-center" key="confirm" (onClose)="clearToast()" [baseZIndex]="5000">
  <ng-template let-message pTemplate="message">
    <div class="confirm-toast" style="flex: 1">
      <div class="text-center">
        <i class="pi pi-check-circle" style="font-size: 3rem"></i>
        <h4>{{message.summary}}</h4>
        <p>{{message.detail}}</p>
      </div>
      <div class="footer-toast">
        <button class="btn btn-primary btn-icon-inline" (click)="back()">
          <i class="pi pi-delete-left"></i>Retour
        </button>
      </div>
    </div>
  </ng-template>
</p-toast>