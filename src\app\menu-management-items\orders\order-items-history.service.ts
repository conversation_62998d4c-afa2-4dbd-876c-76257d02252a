import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { lastValueFrom } from 'rxjs';
import { t } from 'src/app/shared/functions/global.function';
import { OrderStatus } from 'src/app/shared/models/order';
import { OrderItem } from 'src/app/shared/models/order-item.model';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { QueryResult } from 'src/app/shared/types';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OrderItemsHistoryService {

  url: string;
  order: OrderItem;
  modalDetail: boolean;

  constructor(
    private http: HttpClient,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService
  ) {
    this.url =
      this.baseUrlService.getOrigin() + environment.basePath + '/order-items';
  }

  async getOrderItems(param: any): Promise<HttpErrorResponse | { data: OrderItem[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      const { status, appReference, offset, limit, userCategory, paymentMode, customer, product, date, enable = true, } = param;
      if (offset) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (status) { params = params.append('status', status); }
      if (appReference) { params = params.append('appReference', `${appReference}`); }

      if (date.start && date.end) {
        params = params.append('startDate', moment(date.start).format('YYYY-MM-DD'));
        params = params.append('endDate', moment(date.end).format('YYYY-MM-DD'));
      }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<HttpErrorResponse |
      { data: OrderItem[], count: number }>(`${this.url}`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async ValidateOrder(order: OrderItem): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch(`${this.url}/validate/${order._id}`, { appReference: order?.appReference, status: OrderStatus.VALIDATED },));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }
  async CancelOrder(reason: string, actionType: string, orderItemId: string): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch(`${this.url}/handle-order/${orderItemId}`, { actionType: actionType, reason: reason }));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    } finally { this.commonSrv.isLoading = false; }
  }


}
