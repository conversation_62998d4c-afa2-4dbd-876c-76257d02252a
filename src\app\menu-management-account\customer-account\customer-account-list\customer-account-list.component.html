<div class="account-list-page container page-container">
  <div class="account-list-page-container">
    <div class="header-list">
      <div class="title-container">
        <h2 class="title" i18n="@@customerTitle">Liste des clients
          <span *ngIf="![employeeType.CORDO_RH, employeeType.DRH].includes(this.commonService?.user?.employeeType)"
            [ngStyle]="{'color': 'var(--clr-primary-400)',
                                    'font-family': 'var(--montserrat-bold)'}">{{ title }}</span>
          <span [ngStyle]="{'color': 'var(--clr-primary-400)',
          'font-family': 'var(--montserrat-bold)'}"
            *ngIf="[employeeType.CORDO_RH, employeeType.DRH].includes(this.commonService?.user?.employeeType)">{{drhTitle}}</span>
        </h2>
        <div class="section-button">
          <button type="button" pButton i18n-label="@@listreset" label="Réinitialiser" icon="pi pi-replay"
            class="p-button-text p-button-warning margin-rigth" (click)="reset()">
          </button>
          <button type="button" pButton class="p-button-secondary margin-rigth" i18n-label="@@ListFiltre" label="Filtre"
            icon="pi pi-filter" (click)="showSideBar = true">
          </button>
          <button pButton class="p-button-danger" i18n-label="@@ListCreateAccount" label=" Créer un compte"
            icon="pi pi-plus" *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
            (click)="op.toggle($event)">
          </button>

          <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
            icon="pi pi-file-pdf" (click)="showDialogExport = true">
          </button>

          <p-overlayPanel class="overlayPanel" #op [style]="{width: '195px'}" [dismissable]="true"
            [hideTransitionOptions]="'1ms'">
            <ng-template pTemplate>
              <div class="align-column">
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()" i18n="@@customerTitle-1"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  routerLink="/account/customer/form/Particular">
                  Particulier
                </div>
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()" i18n="@@customerTitle-2"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  routerLink="/account/customer/form/EmployeeCimencam">
                  Employé
                </div>
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()" routerLink="/account/customer/form/RETAILER"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  i18n="@@customerTitle-3">
                  Revendeur
                </div>
              </div>
            </ng-template>
          </p-overlayPanel>
        </div>
      </div>
    </div>

    <div class="align-container">
      <div class="align-paginator">
        <p-paginator (onPageChange)="paginate($event)" [rows]="limit" [totalRecords]="total"
          [showJumpToPageDropdown]="true" [showPageLinks]="false">
        </p-paginator>
        <div class="title-h4">Total: {{total}}</div>
      </div>
      <p-tabView (onChange)=" !isDrhOrRh ? handleChangeTabUser(): handleChangeTabUserForDrhORCoordoRh()"
        class="bg-tertiary-100" [(activeIndex)]="userService.indexTabClient">
        <p-tabPanel i18n-header="@@customerTitle-1" header="Particuliers" *ngIf="!isDrhOrRh">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="max-width:50px; min-width: 50px">N°</th>
                  <!-- <th style="max-width:180px; min-width:180px" i18n="@@listName0">Nom</th> -->
                  <th style="max-width:180px; min-width:180px" i18n="@@listName0">Nom et Prénom</th>
                  <th style="max-width:300px; min-width:300px">Email</th>
                  <th style="max-width:120px; min-width:120px" i18n="@@list_CNI">CNI</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@form_City">Ville</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@form_City">District</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@form_City">Quartier</th>
                  <th style="max-width:160px; min-width:160px" i18n="@@list_Tel">Téléphone</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; min-width:50px">{{i + offset + 1}}</td>
                  <!-- <td style="max-width:180px; min-width:180px" pTooltip="{{user.lastName}}" tooltipPosition="top">
                    {{user.lastName || 'N/A' | truncateString:15}}
                  </td> -->
                  <td style="max-width:180px; min-width:180px" pTooltip="{{user.firstName}}" tooltipPosition="top">
                    {{user.firstName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="max-width:300px; min-width:300px" pTooltip="{{user.email}}" tooltipPosition="top">
                    {{user.email | truncateString: 30}}</td>
                  <td style="max-width:120px; min-width:120px">{{user.cni || 'N/A' | truncateString:
                    10}}</td>
                  <td style="max-width:150px; min-width:150px" pTooltip="{{user?.address?.city}}" tooltipPosition="top">
                    {{user?.address?.city || 'N/A' | truncateString : 18}}</td>
                  <td style="max-width:150px; min-width:150px" pTooltip="{{user?.address?.district}}"
                    tooltipPosition="top">
                    {{user?.address?.district || 'N/A' | truncateString : 18}}</td>
                  <td style="max-width:150px; min-width:150px" pTooltip="{{user?.address?.neighborhood}}"
                    tooltipPosition="top">
                    {{user?.address?.neighborhood || 'N/A' | truncateString : 18}}</td>
                  <td style="max-width:160px; min-width:160px">{{user?.tel || 'N/A' | truncateString:
                    15}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@listUpdate"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" tooltipPosition="top"
                            [routerLink]="'form/' + userType + '/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listPWD"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CHANGE_PASSWORD) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div (click)="deleteUser(user)" class="btn btn-icon"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            [pTooltip]="user?.enable? ('Disable'|translate|async) : ('Enable'|translate|async) "
                            tooltipPosition="top">
                            <i [class]=" user?.enable? 'pi pi-eye-slash' : 'pi pi-eye' "></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listCode" pTooltip="Code Afriland"
                            tooltipPosition="top" (click)="showModalcodeAfriland(user)">
                            <i class="pi pi-key"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0 && !isLoading">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@EmptyUserList">
                  Aucun utilisateur trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>

        <p-tabPanel i18n-header="@@customerTitle-3" header="Revendeurs" *ngIf="!isDrhOrRh">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="max-width:50px; min-width: 50px">N°</th>
                  <th style="max-width:180px; min-width:180px" i18n="@@listName9">Nom</th>
                  <th style="max-width:300px; min-width:300px">Email</th>
                  <th style="max-width:120px; min-width:120px" i18n="@@listCNI9">CNI</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@formCity9">Ville</th>
                  <th style="max-width:160px; min-width:160px" i18n="@@listTel9">Téléphone</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; min-width: 50px">{{i + offset + 1}}</td>
                  <td style="min-width:180px; max-width:180px" pTooltip="{{user?.lastName}}" tooltipPosition="top">
                    {{user?.lastName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:300px; max-width:300px" pTooltip="{{user?.email}}" tooltipPosition="top">
                    {{user?.email | truncateString: 30}}</td>
                  <td style="min-width:120px; max-width:120px">{{user?.cni || 'N/A' |
                    truncateString:10}}</td>
                  <td style="min-width:150px; max-width:150px" pTooltip="{{user?.address?.city}}" tooltipPosition="top">
                    {{user?.address?.city || 'N/A' | truncateString : 18}}</td>
                  <td style="min-width:160px; min-width:160px">{{user?.tel || 'N/A' | truncateString :
                    15}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@listUpdate"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" tooltipPosition="top"
                            [routerLink]="'form/' + userType + '/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listPWD"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div (click)="deleteUser(user)" class="btn btn-icon"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            [pTooltip]="user?.enable? ('Disable'|translate|async) : ('Enable'|translate|async) "
                            tooltipPosition="top">
                            <i [class]=" user?.enable? 'pi pi-eye-slash' : 'pi pi-eye' "></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@EmptyUserList">
                  Aucun utilisateur trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>

        <!-- <p-tabPanel i18n-header="@@customerTitle-2" class="bg-tertiary-100" header="Employés">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="max-width:50px; min-width: 50px">N°</th>
                  <th style="max-width:180px; min-width:180px" i18n="@@listName8">Nom</th>
                  <th style="max-width:300px; min-width:294px">Email</th>
                  <th style="max-width:120px; min-width:120px" i18n="@@listCNI8">CNI</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@formCity8">Ville</th>
                  <th style="max-width:160px; min-width:130px" i18n="@@listTel8">Téléphone</th>
                  <th style="max-width:150x; min-width:148px" i18n="@@listEmployee">Type d'employé</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; min-width: 50px">{{i + offset + 1}}</td>
                  <td style="min-width:180px; max-width:180px" pTooltip="{{user?.lastName}}" tooltipPosition="top">
                    {{user?.lastName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:294px; max-width:200px" pTooltip="{{user?.email }}" tooltipPosition="top">
                    {{user?.email | truncateString: 30}}</td>
                  <td style="min-width:120px; max-width:120px">{{user.cni || 'N/A' |
                    truncateString:10}}</td>
                  <td style="min-width:150px; max-width:150px" pTooltip="{{user?.address?.city}}" tooltipPosition="top">
                    {{user?.address?.city ||
                    'N/A' | truncateString : 18}}</td>
                  <td style="max-width:160px; min-width:130px">{{user?.tel || 'N/A' |
                    truncateString:15}}</td>
                  <td style="max-width:150px; min-width:148px" class="row-clr">
                    <button class="elt" [ngClass]="getColor(user?.employeeType)">
                      {{employeeType[user?.employeeType]}}</button>
                  </td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@listUpdate"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" tooltipPosition="top"
                            [routerLink]="'form/' + userType + '/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listPWD"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div (click)="deleteUser(user)" class="btn btn-icon"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            [pTooltip]="user?.enable? ('Disable'|translate|async) : ('Enable'|translate|async) "
                            tooltipPosition="top">
                            <i [class]=" user?.enable? 'pi pi-eye-slash' : 'pi pi-eye' "></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listCode" pTooltip="Code Afriland"
                            tooltipPosition="top" (click)="showModalcodeAfriland(user)">
                            <i class="pi pi-key"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@EmptyUserList">
                  Aucun utilisateur trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>

        <p-tabPanel header="Employés à valider" i18n-header="@@customerTitle-5">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh" *ngIf="users?.length > 0">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="max-width:50px; min-width: 50px">N°</th>
                  <th style="max-width:180px; min-width:180px" i18n="@@listName8">Nom</th>
                  <th style="max-width:294px; min-width:300px">Email</th>
                  <th style="max-width:120px; min-width:120px" i18n="@@listCNI8">CNI</th>
                  <th style="max-width:150px; min-width:150px" i18n="@@formCity8">Ville</th>
                  <th style="max-width:160px; min-width:130px" i18n="@@listTel8">Téléphone</th>
                  <th style="max-width:150px; min-width:148px"> Type d'employé</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; min-width: 50px">{{i + offset + 1}}</td>
                  <td style="min-width:180px; max-width:180px" pTooltip="{{user?.lastName}}" tooltipPosition="top">
                    {{user?.lastName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:294px; max-width:300px" pTooltip="{{user?.email }}" tooltipPosition="top">
                    {{user?.email | truncateString: 30}}</td>
                  <td style="min-width:120px; max-width:120px">{{user.cni || 'N/A' |
                    truncateString:10}}</td>
                  <td style="min-width:150px; max-width:150px" pTooltip="{{user?.address?.city}}" tooltipPosition="top">
                    {{user?.address?.city ||
                    'N/A' | truncateString : 18}}</td>
                  <td style="max-width:160px; min-width:130px">{{user?.tel || 'N/A' |
                    truncateString:15}}</td>
                  <td style="max-width:150px; min-width:148px" class="row-clr">
                    <button class="elt" [ngClass]="getColor(user?.employeeType)">
                      {{employeeType[user?.employeeType]}}</button>
                  </td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE)"
                            i18n-pTooltip="@@listValid" pTooltip="Valider le compte" tooltipPosition="top"
                            (click)="validateAccount(user)">
                            <i class="pi pi-user-edit"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div class="not-found" *ngIf="users?.length <= 0">
              <img src="assets/icons/data-not-fount.svg" alt="">
              <h6 i18n="@@EmptyUserList">
                Aucun utilisateur trouvé
              </h6>
            </div>
          </div>
        </p-tabPanel> -->

      </p-tabView>
    </div>

  </div>
</div>

<p-sidebar [(visible)]="showSideBar" [style]="{width:'25em'}" position="right">
  <section class="filter-sidebar">
    <section class="header-filter">
      <span></span>
      <button type="button" pButton i18n-label="@@FilterReset" label="Réinitialiser" icon="pi pi-replay"
        class="p-button-text p-button-warning" (click)="reset()"></button>
    </section>
    <section class="body-filter">
      <form class="form">
        <div class="input-group">
          <label for="startDate" i18n="@@startDate">Date de début</label>
          <p-calendar [(ngModel)]="userService.filterForm.startDate" name="startDate" inputId="startDate"
            dateFormat="dd/mm/yy" i18n-placeholder="@@startDatePlaceholder" placeholder="Sélectionner une date">
          </p-calendar>
        </div>

        <div class="input-group">
          <label for="endDate" i18n="@@endDate">Date de fin</label>
          <p-calendar [(ngModel)]="userService.filterForm.endDate" name="endDate" inputId="endDate"
            dateFormat="dd/mm/yy" i18n-placeholder="@@endDatePlaceholder" placeholder="Sélectionner une date">
          </p-calendar>
        </div>

        <div class="input-group">
          <label i18n="@@region">Région</label>
          <p-dropdown [(ngModel)]="userService.filterForm.region" name="region" [options]="regions"
            (onChange)="regionChange($event)" [showClear]="true" i18n-placeholder="@@regionPlaceholder"
            placeholder="Sélectionner une région">
          </p-dropdown>
        </div>
        <div class="input-group">
          <label i18n="@@commercialRegion">Région commerciale</label>
          <p-dropdown [(ngModel)]="userService.filterForm.commercialRegion" name="commercialRegion"
            [options]="commercialRegions" [showClear]="true" i18n-placeholder="@@commercialRegionPlaceholder"
            placeholder="Sélectionner une région commerciale">
          </p-dropdown>
        </div>
        <div class="input-group">
          <label i18n="@@city">Ville</label>
          <p-dropdown [(ngModel)]="userService.filterForm.city" name="city" [options]="cities"
            (onChange)="cityChange($event)" [showClear]="true" i18n-placeholder="@@cityPlaceholder"
            placeholder="Sélectionner une ville">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label i18n="@@district">Quartier</label>
          <p-autoComplete [(ngModel)]="userService.filterForm.district" [suggestions]="filteredDistricts"
            (completeMethod)="searchDistricts($event)" name="district" styleClass="w-full"
            i18n-placeholder="@@districtPlaceholder" placeholder="Saisir le quartier">
          </p-autoComplete>
        </div>

        <div class="input-group">
          <label for="status" i18n="@@stat">Statut </label>
          <p-dropdown name="type" [options]="statusAccount" optionValue="code" optionLabel="name"
            [(ngModel)]="userService.filterForm.enable" i18n-placeholder="@@statText"
            placeholder="{{'Sélectionner un statut'}}">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="email">Email </label>
          <p-dropdown type="email" [options]="usersEmails" [(ngModel)]="userService.filterForm.email" [showClear]="true"
            optionValue="label" optionLabel="label" [filter]="true" filterBy="label" i18n-placeholder="@@EmailText"
            placeholder="Email utilisateur"></p-dropdown>
        </div>



        <div class="input-group">
          <label for="firstName">Nom et Prénom </label>
          <p-dropdown name="firstName" [options]="usersFirstNames" optionValue="label" optionLabel="label"
            [filter]="true" [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.firstName"
            placeholder="Prénom utilisateur">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="neighborhood">Quartier </label>
          <p-dropdown name="neighborhood" [options]="usersNeighborhoods" optionValue="label" [filter]="true"
            [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.neighborhood"
            placeholder="Quartier utilisateur">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="district">District </label>
          <p-dropdown name="district" [options]="districts" optionValue="label" [filter]="true" [showClear]="true"
            filterBy="label" [(ngModel)]="userService.filterForm.district" placeholder="District utilisateur"
            [style]="{width: '10vw'}">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label for="tel" i18n="@@Tel">Téléphone </label>
          <p-dropdown name="type" [options]="usersTels" optionValue="label" optionLabel="label" [filter]="true"
            [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.tel"
            placeholder="Télephone utilisateur">
          </p-dropdown>
          <!-- <input name="tel" type="text" [(ngModel)]="userService.filterForm.tel" pInputText i18n-placeholder="@@TelText"
            placeholder="Télephone utilisateur" /> -->
        </div>

        <div class="input-group" *ngIf="userType === 'EmployeeCimencam'">
          <label for="status">Type d'employé </label>
          <p-dropdown name="type" [options]="TypeEmployee" optionValue="value" optionLabel="name"
            [(ngModel)]="userService.filterForm.employeeType" placeholder="Sélectionner un type">
          </p-dropdown>
        </div>
      </form>
    </section>
    <section class="footer-filter">
      <button class="btn btn-tertiary btn-icon-inline" (click)=" showSideBar = false" i18n="@@listOut12">
        <i class="pi pi-times"></i>Annuler
      </button>

      <button class="btn btn-primary btn-icon-inline" (click)=" offset=0; getUsers(); showSideBar = false"
        i18n="@@filterBtn">
        <i class="pi pi-search"></i>Filtrer
      </button>
    </section>
  </section>
</p-sidebar>

<p-confirmDialog i18n-acceptLabel="@@listDisable14"
  acceptLabel="{{!userService.filterForm?.enable? 'Activer' : 'Désactiver'}}" [style]="{width: '30vw'}"
  i18n-rejectLabel="@@listReject6" rejectLabel="Annuler"
  rejectButtonStyleClass="p-button-text bg-tertiary-400 clr-default-400"
  [acceptButtonStyleClass]="userService.filterForm?.enable? 'bg-secondary-400 border-secondary-400' : ''"
  defaultFocus="none">
</p-confirmDialog>

<p-dialog i18n-closeArialLabel="@@listOut13" closeAriaLabel="Annuler" i18n-header="@@listheadear-container14"
  header="{{('PASSWORD' |translate | async) + ' ' + user?.lastName || user?.email }}" [(visible)]="modalReset"
  [modal]="true" [style]="{width: '30vw'}" [draggable]="false" [resizable]="false">

  <div class="input-group-float">
    <label i18n="@@newPwd14">Nouveau mot de passe </label>
    <p-password [toggleMask]="true" [(ngModel)]="user.password"></p-password>

  </div>
  <div class="input-group-float">
    <label i18n="@@listconfirmNewPwd14">Confirmation mot de passe</label>
    <p-password [toggleMask]="true" [(ngModel)]="confirmPassword"></p-password>
  </div>

  <ng-template pTemplate="footer">
    <div class="footer-center-container">
      <p-button icon="pi pi-times" (click)="modalReset=false" i18n-label="@@listOut14" label="Annuler"
        styleClass="bg-tertiary-400 border-tertiary-400">
      </p-button>
      <p-button [icon]="isLoadingInModal? 'pi pi-spin pi-spinner' : 'pi pi-check'" (click)="resetpassword()"
        i18n-label="@@listReset14" label="Réinitialiser" styleClass="bg-primary-400 border-primary-400 btn-icon-inline"
        [disabled]="isLoading">
      </p-button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog i18n-header="@@accountListheader" header="CODE AFRILAND" [(visible)]="modalCodeAfriland" [modal]="true"
  [style]="{width: '35%'}" [draggable]="false" [resizable]="false">
  <form class="form display-grid">
    <div class="first-group">
      <div class="input-group">
        <label for="email" i18n="@@listGenerateCodetext">Voulez-vous générer un code Afriland pour ce client? </label>
      </div>
    </div>
  </form>
  <ng-template pTemplate="footer">
    <div class="footer-center-container">
      <p-button icon="pi pi-times" i18n-label="@@listOut14" label="Annuler" (click)="modalCodeAfriland=false"
        styleClass="bg-tertiary-400 border-tertiary-400">
      </p-button>
      <p-button [icon]="isLoadingInModal? 'pi pi-spin pi-spinner' : 'pi pi-check'" i18n-label="@@listGenerateCode"
        (click)="sendOtp(user)" label="Generer code" styleClass="bg-primary-400 border-primary-400 btn-icon-inline"
        [disabled]="isLoading">
      </p-button>
    </div>
  </ng-template>
</p-dialog>


<p-dialog header="EXPORTE LA LISTE " [(visible)]="showDialogExport" [modal]="true" [style]="{width: '50%'}"
  [draggable]="true" [resizable]="true">

  <section class="body-filter">
    <form class="form">
      <!-- Conteneur en deux colonnes -->
      <div class="two-columns-container">

        <!-- Colonne de gauche -->
        <div class="column-left">
        </div>
        <div class="input-group">
          <label i18n="@@district">Quartier</label>
          <p-autoComplete [(ngModel)]="userService.filterForm.district" [suggestions]="filteredDistricts"
            (completeMethod)="searchDistricts($event)" name="districtExport" styleClass="w-full"
            i18n-placeholder="@@districtPlaceholder" placeholder="Saisir le quartier">
          </p-autoComplete>
        </div>

        <div class="input-group">
          <label for="startDate" i18n="@@startDate">Date de début</label>
          <p-calendar [(ngModel)]="userService.filterForm.startDate" name="startDateExport" dateFormat="dd/mm/yy"
            inputId="startDateExport" i18n-placeholder="@@startDatePlaceholder" placeholder="Sélectionner une date">
          </p-calendar>
        </div>

        <div class="input-group">
          <label for="endDate" i18n="@@endDate">Date de fin</label>
          <p-calendar [(ngModel)]="userService.filterForm.endDate" name="endDateExport" dateFormat="dd/mm/yy"
            inputId="endDateExport" i18n-placeholder="@@endDatePlaceholder" placeholder="Sélectionner une date">
          </p-calendar>
        </div>

        <div class="input-group">
          <label i18n="@@region">Région</label>
          <p-dropdown [(ngModel)]="userService.filterForm.region" name="region" [options]="regions"
            (onChange)="regionChange($event)" [showClear]="true" i18n-placeholder="@@regionPlaceholder"
            placeholder="Sélectionner une région">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label i18n="@@city">Ville</label>
          <p-dropdown [(ngModel)]="userService.filterForm.city" name="city" [options]="cities"
            (onChange)="cityChange($event)" [showClear]="true" i18n-placeholder="@@cityPlaceholder"
            placeholder="Sélectionner une ville">
          </p-dropdown>
        </div>

        <!-- Colonne de droite -->
        <div class="column-right">


          <div class="input-group">
            <label for="status" i18n="@@stat">Statut</label>
            <p-dropdown name="statusExport" [options]="statusAccount" optionValue="code" optionLabel="name"
              [(ngModel)]="userService.filterForm.enable" i18n-placeholder="@@statText"
              placeholder="{{'Sélectionner un statut'}}">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="email">Email</label>
            <p-dropdown name="emailExport" type="email" [options]="usersEmails"
              [(ngModel)]="userService.filterForm.email" [showClear]="true" optionValue="label" optionLabel="label"
              [filter]="true" filterBy="label" i18n-placeholder="@@EmailText"
              placeholder="Email utilisateur"></p-dropdown>
          </div>

          <div class="input-group">
            <label for="tel" i18n="@@Tel">Téléphone </label>
            <p-dropdown name="type" [options]="usersTels" optionValue="label" optionLabel="label" [filter]="true"
              [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.tel"
              placeholder="Télephone utilisateur">
            </p-dropdown>

          </div>

          <div class="input-group">
            <label for="firstName">Nom et Prénom </label>
            <p-dropdown name="firstName" [options]="usersFirstNames" optionValue="label" optionLabel="label"
              [filter]="true" [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.firstName"
              placeholder="Nom et prénom utilisateur">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="district">District </label>
            <p-dropdown name="district" [options]="usersDistricts" optionValue="label" optionLabel="label"
              [filter]="true" [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.district"
              placeholder="District utilisateur">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="neighborhood">Quartier </label>
            <p-dropdown name="neighborhood" [options]="usersNeighborhoods" optionValue="label" optionLabel="label"
              [filter]="true" [showClear]="true" filterBy="label" [(ngModel)]="userService.filterForm.neighborhood"
              placeholder="Quartier utilisateur">
            </p-dropdown>
          </div>

          <div class="input-group" *ngIf="userType === 'EmployeeCimencam'">
            <label for="status">Type d'employé</label>
            <p-dropdown name="type" [options]="TypeEmployee" optionValue="value" optionLabel="name"
              [(ngModel)]="userService.filterForm.employeeType" placeholder="Sélectionner un type">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label i18n="@@commercialRegion">Région commerciale</label>
            <p-dropdown [(ngModel)]="userService.filterForm.commercialRegion" name="commercialRegion"
              [options]="commercialRegions" [showClear]="true" i18n-placeholder="@@commercialRegionPlaceholder"
              placeholder="Sélectionner une région commerciale">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="loadNumber">Total d'éléments à exporter, Maximum: {{total}}</label>
            <p-inputNumber [(ngModel)]="limit" name="loadNumber" [size]="100"></p-inputNumber>
          </div>
        </div>

      </div>
    </form>

    <section class="footer-export">
      <button pButton pRipple type="button" label="{{'close' | translate | async}}" icon="pi pi-times"
        class="p-button-outlined p-button-secondary" (click)="showDialogExport = false">
      </button>
      <button pButton pRipple type="button" label="{{'EXPORT' | translate |async}}" icon="pi pi-search"
        [loading]="isLoading" class="p-button-success" (click)=" exportToExcel() ; showDialogExport = false ">
      </button>
    </section>
  </section>


</p-dialog>


<p-toast></p-toast>