import { messageType } from './../../shared/models/notification-type.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { Advantage } from 'src/app/shared/models/advantages.model';
import { Items, MarketplaceStatus } from 'src/app/shared/models/items.model';
import { Product } from 'src/app/shared/models/product.model';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { QueryResult } from 'src/app/shared/types';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MarketPlaceService {
  base_url: string;
  base_url_advantages: string;

  constructor(
    private baseUrl: BaseUrlService,
    private http: HttpClient,
    private commonSrv: CommonService
  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
    this.base_url += '/items';
    this.base_url_advantages = `${this.baseUrl.getOrigin()}${environment.basePath}/advantages`;
  }

  async getItems(param?: any): Promise<{ count: number, data: Items[] }> {
    this.commonSrv.isLoading = true;
    try {
      let params = new HttpParams();
      if (param) {
        const { offset, limit, sku, name, labelCateg, enable = true, created_at, projection } = param;
        if (offset !== undefined || null) { params = params.append('offset', offset); }
        if (limit) { params = params.append('limit', limit); }
        if (sku) { params = params.append('sku', sku); }
        if (name) { params = params.append('name', name); }
        if (labelCateg) { params = params.append('category.label', labelCateg); }
        if (projection) { params = params.append('projection', projection); }
        if (created_at) { params = params.append('created_at', created_at); }
        params = params.append('enable', enable);
      }

      return await lastValueFrom(this.http.get<{ data: Items[], count: number }>(this.base_url, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false }
  }

  async createProduct(product: Items): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.post<QueryResult>(this.base_url, product,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de création du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }
  }

  async updateProduct(productId: string, product: Items): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.base_url}/${productId}`, product,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de création du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }
  }

  async delete(product: Items) {
    this.commonSrv.isLoading = true;
    try {
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/${product._id}`, { enable: !product?.enable },
          { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de désactivation du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }

  }
  async updateMarketplaceStatus(marketplaceStatus: MarketplaceStatus): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return lastValueFrom(this.http.patch<QueryResult>(`${this.base_url}/toggle-marketplace/market`, marketplaceStatus,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de désactivation du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }

  }

  async assignItemToFidelityStatus(itemId: string, fidelityStatus: string | number): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.put(`${this.base_url_advantages}/assign-item/${itemId}/status/${fidelityStatus}`, {},
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError('Erreur lors de l’assignation du niveau de fidélité', error);
    } finally {
      this.commonSrv.isLoading = false;
    }
  }

  async getAdvantages(): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.get<{ data: Advantage[]; count: number }>(`${this.base_url_advantages}`));
    } catch (error) {

      return await this.commonSrv.getError('Erreur lors de la récupération des avantages', error);
    }
  }

  async removeRewardItemFromAdvantage(advantageId: string, itemId: string): Promise<any> {
    try {
      return await lastValueFrom(this.http.put(`${this.base_url_advantages}/${advantageId}/reward-items/${itemId}`, {}));
    } catch (error) {
      return await this.commonSrv.getError('Erreur lors du retrait du produit de l\'avantage', error);
    }
  }
}

