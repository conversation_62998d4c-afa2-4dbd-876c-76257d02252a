<div class="migration-page container page-container">
    <div class="migration-page-container">
        <!-- Header -->
        <div class="header-migration">
            <div class="title-container">
                <h2 class="title">
                    <i class="pi pi-users"></i>
                    Migration des mamies vers les animateurs
                </h2>
                <div class="section-button">
                    <button type="button" pButton label="Back" icon="pi pi-arrow-left"
                        class="p-button-text p-button-secondary" (click)="back()">
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filters-container">
                <div class="filter-group">
                    <label for="district">District :</label>
                    <p-dropdown name="district" [options]="districts" optionValue="label" optionLabel="label"
                        [(ngModel)]="selectedDistrict" [showClear]="true" [filter]="true" filterBy="label"
                        placeholder="Sélectionner un district" (onChange)="onDistrictChange()">
                    </p-dropdown>
                </div>

                <div class="filter-group">
                    <label for="neighborhood">Quartier :</label>
                    <p-dropdown name="neighborhood" [options]="neighborhoods" optionValue="label" optionLabel="label"
                        [(ngModel)]="selectedNeighborhood" [showClear]="true" [filter]="true" filterBy="label"
                        placeholder="Sélectionner un quartier" (onChange)="onNeighborhoodChange()">
                    </p-dropdown>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <div class="filter-buttons">
                        <button type="button" pButton label="Filtrer" icon="pi pi-search" class="p-button-primary"
                            [loading]="isLoading" (click)="filterParticularUsers()">
                        </button>
                        <button type="button" pButton label="Réinitialiser" icon="pi pi-refresh"
                            class="p-button-outlined p-button-secondary" [loading]="isLoading" (click)="resetFilters()">
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="main-content">
            <div class="content-grid">
                <!-- Left column - Particular Users -->
                <div class="left-column">
                    <div class="column-header">
                        <h3>Mamies</h3>
                        <span class="count-badge">{{ selectedParticularUsers.length }} sélectionnées</span>
                    </div>

                    <div class="table-container">
                        <p-progressBar *ngIf="isLoading" mode="indeterminate"></p-progressBar>
                        <p-table [value]="particularUsers" [scrollable]="true" scrollHeight="60vh" [loading]="isLoading"
                            [showLoader]="true">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th colspan="7" style="padding:0;text-align:right;background:transparent;">
                                        <p-paginator [rows]="limitMamies" [totalRecords]="totalMamies"
                                            [first]="offsetMamies" (onPageChange)="onMamiesPageChange($event)"
                                            [rowsPerPageOptions]="[10, 25, 50, 100]"
                                            style="display:inline-block;vertical-align:middle;min-width:200px;">
                                        </p-paginator>
                                    </th>
                                </tr>
                                <tr>
                                    <th style="width: 50px">
                                        <p-checkbox [ngModel]="selectAllParticular"
                                            (ngModelChange)="onSelectAllParticularUsers($event)">
                                        </p-checkbox>
                                    </th>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Téléphone</th>
                                    <th>District</th>
                                    <th>Quartier</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-particularUser>
                                <tr [ngClass]="{'selected-row': isUserSelected(particularUser)}">
                                    <td>
                                        <p-checkbox [ngModel]="getUserSelected(particularUser)"
                                            (ngModelChange)="setUserSelected(particularUser, $event)" [binary]="true">
                                        </p-checkbox>
                                    </td>
                                    <td>{{ particularUser.firstName }} {{ particularUser.lastName }}</td>
                                    <td>{{ particularUser.email }}</td>
                                    <td>{{ particularUser.tel }}</td>
                                    <td>{{ particularUser.address?.district || 'N/A' }}</td>
                                    <td>{{ getNeighborhood(particularUser) }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>

                <!-- Right column - Animators -->
                <div class="right-column">
                    <div class="column-header">
                        <h3>Animateurs</h3>
                        <span class="count-badge">{{ selectedAnimators.length }} sélectionnés</span>
                    </div>

                    <!-- Animator filters -->
                    <div class="animator-filters">
                        <div class="filter-row">
                            <div class="filter-input">
                                <label for="animatorName">Nom :</label>
                                <input type="text" id="animatorName" pInputText [(ngModel)]="animatorNameFilter"
                                    placeholder="Rechercher par nom..." (input)="filterAnimators()">
                            </div>
                            <div class="filter-input">
                                <label for="animatorEmail">Email :</label>
                                <input type="text" id="animatorEmail" pInputText [(ngModel)]="animatorEmailFilter"
                                    placeholder="Rechercher par email..." (input)="filterAnimators()">
                            </div>
                            <div class="filter-input">
                                <label for="animatorPhone">Téléphone :</label>
                                <input type="text" id="animatorPhone" pInputText [(ngModel)]="animatorPhoneFilter"
                                    placeholder="Rechercher par téléphone..." (input)="filterAnimators()">
                            </div>
                            <div class="filter-actions">
                                <button type="button" pButton label="Réinitialiser" icon="pi pi-refresh"
                                    class="p-button-outlined p-button-secondary p-button-sm"
                                    (click)="resetAnimatorFilters()">
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <p-table [value]="animators" [scrollable]="true" scrollHeight="60vh">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th colspan="5" style="padding:0;text-align:right;background:transparent;">
                                        <p-paginator [rows]="limitAnimators" [totalRecords]="totalAnimators"
                                            [first]="offsetAnimators" (onPageChange)="onAnimatorsPageChange($event)"
                                            [rowsPerPageOptions]="[10, 25, 50, 100]"
                                            style="display:inline-block;vertical-align:middle;min-width:200px;">
                                        </p-paginator>
                                    </th>
                                </tr>
                                <tr>
                                    <th style="width: 50px">
                                        <p-checkbox [ngModel]="selectAllAnimators"
                                            (ngModelChange)="onSelectAllAnimators($event)">
                                        </p-checkbox>
                                    </th>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Téléphone</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-animator>
                                <tr [ngClass]="{'selected-row': isAnimatorSelected(animator)}">
                                    <td>
                                        <p-checkbox [ngModel]="getAnimatorSelected(animator)"
                                            (ngModelChange)="setAnimatorSelected(animator, $event)" [binary]="true">
                                        </p-checkbox>
                                    </td>
                                    <td>{{ animator.firstName }} {{ animator.lastName }}</td>
                                    <td>{{ animator.email }}</td>
                                    <td>{{ animator.tel }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Validation button -->
        <div class="validation-section">
            <div class="validation-container">
                <div class="summary-info">
                    <p>
                        <strong>{{ selectedParticularUsers.length }}</strong> mamie(s) sélectionnée(s)
                        vers <strong>{{ selectedAnimators.length }}</strong> animateur(s)
                    </p>
                </div>

                <div class="action-buttons">
                    <button type="button" pButton label="Annuler" icon="pi pi-times"
                        class="p-button-outlined p-button-secondary" (click)="back()">
                    </button>

                    <button type="button" pButton label="Details" icon="pi pi-info-circle"
                        class="p-button-outlined p-button-info"
                        [disabled]="selectedParticularUsers.length === 0 && selectedAnimators.length === 0"
                        (click)="showSummaryModal = true">
                    </button>

                    <button type="button" pButton label="Valider l'attribution" icon="pi pi-check"
                        class="p-button-success" [loading]="isLoading"
                        [disabled]="selectedParticularUsers.length === 0 || selectedAnimators.length === 0"
                        (click)="assignParticularUsersToAnimators()">
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Modal -->
<p-dialog header="Récapitulatif de la migration" [(visible)]="showSummaryModal" [modal]="true" [style]="{width: '70vw'}"
    [draggable]="false" [resizable]="false" [contentStyle]="{'overflow': 'auto'}">

    <div class="summary-modal-container">
        <div class="summary-title-bar">
            <h3>Résumé de la migration</h3>
            <span class="summary-count">{{ selectedParticularUsers.length }} mamies → {{ selectedAnimators.length }}
                animateurs</span>
        </div>

        <div class="summary-columns">
            <!-- LEFT SIDE - Mamies -->
            <div class="summary-column">
                <h4>Mamies sélectionnées ({{ selectedParticularUsers.length }})</h4>
                <div class="selected-list">
                    <div class="selected-card" *ngFor="let user of selectedParticularUsers">
                        <div class="card-info">
                            <span class="card-name">{{ user.firstName }} {{ user.lastName }}</span>
                            <span class="card-details">{{ user.email }} | {{ user.address?.district || 'N/A' }}</span>
                        </div>
                        <button pButton type="button" icon="pi pi-times"
                            class="p-button-rounded p-button-danger p-button-text" (click)="removeParticularUser(user)">
                        </button>
                    </div>
                </div>
            </div>

            <!-- RIGHT SIDE - Animateurs -->
            <div class="summary-column">
                <h4>Animateurs sélectionnés ({{ selectedAnimators.length }})</h4>
                <div class="selected-list">
                    <div class="selected-card" *ngFor="let animator of selectedAnimators">
                        <div class="card-info">
                            <span class="card-name">{{ animator.firstName }} {{ animator.lastName }}</span>
                            <span class="card-details">{{ animator.email }} | {{ animator.tel }}</span>
                        </div>
                        <button pButton type="button" icon="pi pi-times"
                            class="p-button-rounded p-button-danger p-button-text" (click)="removeAnimator(animator)">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="footer-center-container">
            <button type="button" pButton label="Fermer" icon="pi pi-times" class="p-button-outlined p-button-secondary"
                (click)="showSummaryModal = false">
            </button>
        </div>
    </ng-template>
</p-dialog>


<!-- Dialogs and messages -->
<p-confirmDialog></p-confirmDialog>
<p-toast></p-toast>