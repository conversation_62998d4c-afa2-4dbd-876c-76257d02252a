<div class="orders-reporting-page page-container container">
  <div class="orders-reporting-page-container">
    <div class="header-reporting">
      <div class="title-container">
        <h2 class="title">Reporting des commandes</h2>

        <div class="section-button">
          <button type="button" pButton label="Réinitialiser" icon="pi pi-replay" class="p-button-text p-button-warning"
            (click)="reset()">
          </button>

          <button type="button" (click)="sideBarDisplay= true" pButton class="p-button-secondary" i18n-label=""
            label="Filtre" icon="pi pi-filter">
          </button>
          <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
            (click)="showDialogExport = true" icon="pi pi-file-pdf">
          </button>
        </div>
      </div>
    </div>

    <div class="orders-card-detail">
      <div class="container-main-bloc">
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <ul>
            <li>
              <p>Ventes</p>
              <!-- <div>  <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"></button></div> -->
            </li>
            <li *ngIf="user?.authorizations.includes(reportingAction.VIEW_REPORTING_VIEW)">Total des ventes (XAF) <br> <br>
              <span class="spacing-value">{{AmountOrder | currency:'':'':"3.0"}}</span>
            </li>
            <li>Nombre de commandes <br> <br>
              <span class="spacing-value">{{orderNumber}}</span>
            </li>
            <li>Commandes non validées<br> <br>
              <span class="spacing-value">{{orderNotValid}}</span>
            </li>
            <li>Délai moyen validation<br> <br>
              <span>
                {{ formatAverageTime().hours }}h
                {{ formatAverageTime().minutes }}min
              </span>
            </li>
          </ul>
        </div>
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <ul>
            <li>
              <P>Comptes utilisateurs </P>
              <!-- <button type="button" pButton class="p-button-primary"  label="{{'EXPORT' | translate |async}}"></button> -->

            </li>
            <li>Total utilisateurs <br> <br>
              <span class="spacing-value">{{userRecapData?.totalUsers}}</span>
            </li>
            <li *ngFor="let data of userRecapData?.dataUserCategories">Total {{data?._id | categoryUserName}}<br> <br>
              <span class="spacing-value">{{data?.totalUser}}</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="container-main-bloc">
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <ul>
            <li> Classement par volume</li>
            <li *ngFor="let data of rankingsData">{{data?.label | truncateString: 30}} <br> <br>
              <span class="spacing-value">{{ (data?.totalValue | number) }} T</span>
            </li>
          </ul>
        </div>

        <div class="table">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <ul>
            <li> Volumes produit vendu</li>
            <li *ngFor="let data of cementVolumesData">{{data?.label | truncateString: 30}} <br> <br>
              <span class="spacing-value"> {{data?.totalValue | currency:'':'':"1.0"}} T</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
    <div class="orders-reporting-body">
      <div class="order-reporting-chart">
        <div class="reporting-order-bar reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par utilisateur</h3>


              <div class="right-block">
                <div class="pdroSelect">
                  <p-dropdown [options]="enploy" [(ngModel)]="selectedenploy" placeholder="Compagnie" class="pdroSelect"
                    optionLabel="name" [showClear]="true">

                  </p-dropdown>
                </div>



                <p-inputSwitch pButton [label]="typeChart? 'HISTOGRAMME' : 'LINEAIRE'" icon="pi-chart"
                  class="p-button-text p-button-icons" (onChange)="valueChartType = typeChart ? 'line' : 'bar';"
                  [(ngModel)]="typeChart">
                </p-inputSwitch>

                <!-- <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
                  (click)="showDialogExport = true" icon="pi pi-file-pdf">
                </button> -->
                <!-- 
                <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
                  icon="pi pi-file-pdf">
                </button> -->
              </div>
            </ng-template>
            <p-chart [type]="'bar'" [data]="userData" *ngIf="typeChart === false" width="100%" height="40vh"> </p-chart>
            <p-chart type="line" [data]="userData" *ngIf="typeChart === true" width="100%" height="40vh"> </p-chart>
          </p-card>
        </div>
        <div class="reporting-order-doughnut reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3 class="p-card-header">Ventes totales des companies</h3>
            </ng-template>
            <p-chart type="doughnut" [data]="userDataDoughnut" width="100%" height="40vh"></p-chart>
          </p-card>
        </div>
      </div>

      <div class="order-reporting-chart">


        <div class="reporting-order-doughnut reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <div class="p-card-header">Ventes totales par produits</div>
            </ng-template>
            <p-chart type="doughnut" [data]="productDataDoughnut" width="100%" height="40vh"></p-chart>
          </p-card>
        </div>

        <div class="reporting-order-bar reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par produit</h3>


              <div class="right-block">
                <p-inputSwitch pButton [label]="typeChart2? 'HISTOGRAMME' : 'LINEAIRE'" icon="pi-chart"
                  class="p-button-text p-button-icons" (onChange)="valueChartType1 = typeChart1 ? 'bar' : 'line'"
                  [(ngModel)]="typeChart2">
                </p-inputSwitch>

                <!-- <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
                  (click)="showDialogExport = true" icon="pi pi-file-pdf">
                </button> -->

              </div>


            </ng-template>

            <p-chart type="bar" [data]="productData" *ngIf="typeChart2 === false" width="100%" height="40vh"> </p-chart>
            <p-chart type="line" [data]="productData" *ngIf="typeChart2 === true" width="100%" height="40vh"> </p-chart>
          </p-card>
        </div>

      </div>

      <div class="order-reporting-chart">
        <div class="reporting-order-bar reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par region </h3>



              <p-inputSwitch pButton [label]="typeChart1? 'LINEAIRE' : 'HISTOGRAMME'" icon="pi-chart"
                class="p-button-text p-button-icons" (onChange)="valueChartType2 = typeChart2 ? 'line' : 'bar'"
                [(ngModel)]="typeChart1">
              </p-inputSwitch>

              <!-- <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
              (click)="showDialogExport = true" icon="pi pi-file-pdf">
            </button> -->
            </ng-template>
            <p-chart type="line" [data]="regionData" [options]="options" *ngIf="typeChart1 === true" width="100%"
              height="40vh"> </p-chart>
            <p-chart type="bar" [data]="regionData" [options]="options" *ngIf="typeChart1 === false" width="100%"
              height="40vh"> </p-chart>
          </p-card>
        </div>

        <div class="reporting-order-doughnut reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <div class="p-card-header">Ventes totales par régions</div>
            </ng-template>
            <p-chart type="doughnut" [data]="regionDataDoughnut" width="100%" height="40vh"></p-chart>
          </p-card>
        </div>
      </div>

      <div class="order-reporting-chart">
        <div class="reporting-order-doughnut reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <div class="p-card-header">Ventes totales par paiement</div>
            </ng-template>
            <p-chart type="doughnut" [data]="paymentDataDoughnut" width="100%" height="40vh"></p-chart>
          </p-card>
        </div>
        <div class="reporting-order-bar reporting-chart">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par paiement</h3>


              <div class="right-block">

                <p-inputSwitch pButton [label]="typeChart3? 'HISTOGRAMME' : 'LINEAIRE'" icon="pi-chart-ba"
                  class="p-button-text p-button-icons" (onChange)="valueChartType3 = typeChart3 ? 'bar' : 'line'"
                  [(ngModel)]="typeChart3">
                </p-inputSwitch>


                <!-- <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
                  (click)="showDialogExport = true" icon="pi pi-file-pdf">
                </button> -->

              </div>


            </ng-template>
            <p-chart type="line" [data]="paimentData" *ngIf="typeChart3 === false" width="100%" height="40vh"></p-chart>
            <p-chart type="bar" [data]="paimentData" *ngIf="typeChart3 === true" width="100%" height="40vh"></p-chart>
          </p-card>
        </div>

      </div>

      <!-- <div class="order-reporting-region-paiment">
        <div class="reporting-region">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par region </h3>



              <p-inputSwitch pButton [label]="typeChart1? 'LINEAIRE' : 'HISTOGRAMME'" icon="pi-chart"
                class="p-button-text p-button-icons" (onChange)="valueChartType2 = typeChart2 ? 'line' : 'bar'"
                [(ngModel)]="typeChart1">
              </p-inputSwitch>

              <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
              (click)="showDialogExport = true" icon="pi pi-file-pdf">
            </button> 

            </ng-template>
            <p-chart type="line" [data]="regionData" [options]="options" *ngIf="typeChart1 === true" width="100%"
              height="40vh"> </p-chart>
            <p-chart type="bar" [data]="regionData" [options]="options" *ngIf="typeChart1 === false" width="100%"
              height="40vh"> </p-chart>
          </p-card>
        </div>
        <div class="reporting-paiement">
          <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
          <p-card>
            <ng-template pTemplate="header">
              <h3>Evolution des ventes par paiement</h3>


              <div class="right-block">

                <p-inputSwitch pButton [label]="typeChart3? 'HISTOGRAMME' : 'LINEAIRE'" icon="pi-chart-ba"
                  class="p-button-text p-button-icons" (onChange)="valueChartType3 = typeChart3 ? 'bar' : 'line'"
                  [(ngModel)]="typeChart3">
                </p-inputSwitch>


                <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
                  (click)="showDialogExport = true" icon="pi pi-file-pdf">
                </button>

              </div>


            </ng-template>
            <p-chart type="line" [data]="paimentData" *ngIf="typeChart3 === false" width="100%" height="40vh">
            </p-chart>
            <p-chart type="bar" [data]="paimentData" *ngIf="typeChart3 === true" width="100%" height="40vh"> </p-chart>
          </p-card>
        </div>
      </div> -->
    </div>
  </div>
  <p-sidebar [(visible)]="sideBarDisplay" position="right">
    <section class="filter-sidebar">
      <section class="header-filter">
        <button type="button" pButton i18n-label="@@historyOrders-Reset" label="Réinitialiser" icon="pi pi-replay"
          class="p-button-text p-button-warning"></button>
      </section>
      <section class="body-filter">
        <form class="form">
          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-List_Date">Date de début</label>
            <p-calendar [(ngModel)]="filterForm.startDate" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>
          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-ListEndDate">Date de fin</label>
            <p-calendar [(ngModel)]="filterForm.endDate" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>
          <div class="input-group">
            <label for="status">Statut commande </label>
            <p-dropdown [options]="orderStatus" optionLabel="name" optionValue="code" [(ngModel)]="filterForm.status"
              [showClear]="true" placeholder="Sélectionner un statut">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@produit">Produit </label>
            <p-dropdown name="type" [options]="products" optionLabel="label" optionValue="_id" [showClear]="true"
              [filter]="true" filterBy="label" [(ngModel)]="filterForm.product" i18n-placeholder="@@productselect"
              placeholder="Sélectionner un produit">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="user" i18n="@@manageStore-Ref">Sélectionner un store </label>
            <p-dropdown name="userCategory" [options]="stores" optionLabel="label" optionValue="storeRef"
              filterBy="label" [filter]="true" [showClear]="true" [(ngModel)]="filterForm.storeRef"
              placeholder="Sélectionner un client">
            </p-dropdown>
          </div>

          <div class="input-group">
            <label for="status" i18n="@@region">Region</label>
            <p-dropdown name="type" [options]="region" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.region" i18n-placeholder="@@regionselect" placeholder="Sélectionner une region">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@typ_user">Type d'utilisateur </label>
            <p-dropdown name="type" [options]="userCategories" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.user" i18n-placeholder="@@typ_user_select" placeholder="type d'utilisateur">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@paiement">Paiement</label>
            <p-dropdown name="type" [options]="paymentModes" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.payment" i18n-placeholder="@@select_paiement"
              placeholder="Sélectionner le paiment">
            </p-dropdown>
          </div>
        </form>
      </section>
      <section class="footer-filter">
        <button pButton pRipple type="button" i18n-label="@@historyOrders-ListOut" label="Annuler" icon="pi pi-times"
          class="p-button-outlined p-button-secondary" (click)=" sideBarDisplay = false">
        </button>
        <button pButton pRipple type="button" i18n-label="@@historyOrders-ListFilter" label="Filtrer"
          icon="pi pi-search" class="p-button-success" (click)="refresh(); sideBarDisplay= false">
        </button>
      </section>
    </section>
  </p-sidebar>



  <p-dialog header="EXPORTER" [(visible)]="showDialogExport" [modal]="true" [draggable]="true" [resizable]="true">

    <section class="body-filter">
      <form class="form body-export">
        <div class="left-section">
          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-List_Date">Date de début</label>
            <p-calendar [(ngModel)]="filterForm.startDate" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>
          <div class="input-group">
            <label for="calendar" i18n="@@historyOrders-ListEndDate">Date de fin</label>
            <p-calendar [(ngModel)]="filterForm.endDate" dateFormat="dd/mm/yy" InputId="clendar"
              i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
          </div>
          <div class="input-group">
            <label for="status">Statut commande </label>
            <p-dropdown [options]="orderStatus" optionLabel="name" optionValue="code" [(ngModel)]="filterForm.status"
              [showClear]="true" placeholder="Sélectionner un statut">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@produit">Produit </label>
            <p-dropdown name="type" [options]="products" optionLabel="label" optionValue="_id" [showClear]="true"
              [filter]="true" filterBy="label" [(ngModel)]="filterForm.product" i18n-placeholder="@@productselect"
              placeholder="Sélectionner un produit">
            </p-dropdown>
          </div>

          <div class="input-group">
            <p-inputSwitch pButton label="Inclure la repartitions des Ventes" icon="pi-chart"
              class="p-button-text p-button-icons" [(ngModel)]="sellAprove">
            </p-inputSwitch>
          </div>

          <div class="input-group">
            <p-inputSwitch pButton label="Inclure la repartitions des Comptes utilisateurs" icon="pi-chart"
              class="p-button-text p-button-icons" [(ngModel)]="userAcountAproved">
            </p-inputSwitch>
          </div>
        </div>

        <div class="right-section">
          <div class="input-group">
            <label for="user" i18n="@@manageStore-Ref">Sélectionner un store </label>
            <p-dropdown name="userCategory" [options]="stores" optionLabel="label" optionValue="storeRef"
              filterBy="label" [filter]="true" [showClear]="true" [(ngModel)]="filterForm.storeRef"
              placeholder="Sélectionner un client">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@region">Region</label>
            <p-dropdown name="type" [options]="region" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.region" i18n-placeholder="@@regionselect" placeholder="Sélectionner une region">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@typ_user">Type d'utilisateur </label>
            <p-dropdown name="type" [options]="userCategories" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.user" i18n-placeholder="@@typ_user_select" placeholder="type d'utilisateur">
            </p-dropdown>
          </div>
          <div class="input-group">
            <label for="status" i18n="@@paiement">Paiement</label>
            <p-dropdown name="type" [options]="paymentModes" optionLabel="name" optionValue="code" [showClear]="true"
              [(ngModel)]="filterForm.payment" i18n-placeholder="@@select_paiement"
              placeholder="Sélectionner le paiment">
            </p-dropdown>
          </div>
          <div class="input-group">
            <p-inputSwitch pButton label="Inclure la repartitions du classement par valeurs" icon="pi-chart"
              class="p-button-text p-button-icons" [(ngModel)]="companyValueAprove">
            </p-inputSwitch>
          </div>
          <div class="input-group">
            <p-inputSwitch pButton label="Inclure la repartions du volume de farine vendu" icon="pi-chart"
              class="p-button-text p-button-icons" [(ngModel)]="volumCimentAprove">
            </p-inputSwitch>
          </div>
        </div>


      </form>


      <section class="footer-export">
        <button pButton pRipple type="button" label="{{'close' | translate | async}}" icon="pi pi-times"
          class="p-button-outlined p-button-secondary" (click)="showDialogExport = false">
        </button>
        <button pButton pRipple type="button" label="{{'EXPORT' | translate |async}}" icon="pi pi-search"
          [loading]="isLoading" class="p-button-success" (click)="getExportRecap() ; showDialogExport = false ">
        </button>
      </section>
    </section>


  </p-dialog>
</div>


<p-toast></p-toast>