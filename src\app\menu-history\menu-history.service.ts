import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CancellationData, Order, OrderStatus } from '../shared/models/order';
import { BaseUrlService } from '../shared/services/base-url.service';
import { CommonService } from '../shared/services/common.service';
import * as moment from 'moment';
import { QueryResult } from '../shared/types';
import { UserCategory } from '../shared/enums/user-category.enum';
import { t } from '../shared/functions/global.function';
import { Price } from '../shared/models/price';

@Injectable({
  providedIn: 'root'
})
export class MenuHistoryService {

  url: string;
  order: Order;
  modalDetail: boolean;

  constructor(
    private http: HttpClient,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService
  ) {
    this.url =
      this.baseUrlService.getOrigin() + environment.basePath + '/orders';
  }

  async getOrders(param: any): Promise<HttpErrorResponse | { data: Order[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      const { status, appReference, offset, limit, userCategory, paymentMode, customer, product, date, enable = true, } = param;
      if (offset) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (status) { params = params.append('status', status); }
      if (appReference) { params = params.append('appReference', `${appReference}`); }
      if (paymentMode) { params = params.append('payment.mode.id', paymentMode); }
      if (userCategory) { params = params.append('user.category', userCategory); }
      if (customer) { params = params.append('user.email', customer); }
      if (product) { params = params.append('cart.items.product.label', product); }
      if ((this.commonSrv?.user?.category === UserCategory.COMMERCIAL) && userCommercialReg) { params = params.append('user.address.commercialRegion', userCommercialReg) }

      if (date.start && date.end) {
        params = params.append('startDate', moment(date.start).format('YYYY-MM-DD'));
        params = params.append('endDate', moment(date.end).format('YYYY-MM-DD'));
      }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<HttpErrorResponse |
      { data: Order[], count: number }>(`${this.url}`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async getOrdersByCommercials(param: any): Promise<HttpErrorResponse | { data: Order[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { status, appReference, offset, limit, userCategory, renderType, paymentMode, customer, product, enable = true, region, date, erpReference, erpSoldToId } = param;
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      if (offset !== undefined || null) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (status) { params = params.append('status', status); }
      if (appReference) { params = params.append('appReference', `${appReference}`); }
      if (erpReference) { params = params.append('erpReference', `${erpReference}`); }
      if (renderType) { params = params.append('cart.renderType', renderType); }
      if (paymentMode) { params = params.append('payment.mode.id', paymentMode); }
      if (userCategory || userCategory === UserCategory.Particular) { params = params.append('user.category', userCategory); }
      if (customer) { params = params.append('company.name', customer); }
      if (region) { params = params.append('company.address.commercialRegion', region); }
      if (erpSoldToId) { params = params.append('company.erpSoldToId', `${erpSoldToId}`); }
      if (product) { params = params.append('cart.items.product.label', product); }
      if (date.start && date.end) {
        params = params.append('startDate', moment(date.start).format('YYYY-MM-DD'));
        params = params.append('endDate', moment(date.end).format('YYYY-MM-DD'));
      }
      if ((this.commonSrv?.user?.category === UserCategory.COMMERCIAL) && userCommercialReg) { params = params.append('user.address.commercialRegion', userCommercialReg) }
      params = params.append('sort', 'created_at');
      params = params.append('way', '-1');
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<HttpErrorResponse |
      { data: Order[], count: number }>(`${this.url}/commercials`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async getRecapInfosForOrderList(param: any): Promise<HttpErrorResponse | { data: { totalAmount: number, totalTonnes: number, numberOfOrders: number } }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { status, appReference, userCategory, renderType, paymentMode, customer, email, product, enable = true, date, region } = param;
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      if (status) { params = params.append('status', status); }
      if (appReference) { params = params.append('appReference', `${appReference}`); }
      if (renderType) { params = params.append('cart.renderType', renderType); }
      if (paymentMode) { params = params.append('payment.mode.id', paymentMode); }
      if (userCategory || userCategory === UserCategory.Particular) { params = params.append('user.category', userCategory); }
      if (email) { params = params.append('user.email', email); }
      if (region) { params = params.append('company.address.commercialRegion', region); }
      if (customer) { params = params.append('company.name', customer); }
      if (product) { params = params.append('cart.items.product.label', product); }
      if (date.start && date.end) {
        params = params.append('startDate', moment(date.start).format('YYYY-MM-DD'));
        params = params.append('endDate', moment(date.end).format('YYYY-MM-DD'));
      }
      params = params.append('enable', enable);
      if ((this.commonSrv?.user?.category === UserCategory.COMMERCIAL) && userCommercialReg) { params = params.append('user.address.commercialRegion', userCommercialReg) }
      return await lastValueFrom(this.http.get<HttpErrorResponse |
      { data: { totalAmount: number, totalTonnes: number, numberOfOrders: number } }>(`${this.url}/recap-order-list`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async saveErpReference(_id: string, erpReference: string) {
    try {
      return await lastValueFrom(this.http.patch(`${this.url}/${_id}/save-num-order`, { erpReference }));
    } catch (error) {
      return error;
    }
  }

  async CommercialValidatedOrder(order: Order): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch(`${this.url}/${order._id}/validate`, { erpReference: order?.erpReference, status: OrderStatus.VALIDATED },));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async CommercialValidatedOrderLowBalance(order: Order): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch(`${this.url}/${order._id}/validate`, { erpReference: order?.erpReference, status: OrderStatus.VALIDATED },));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async RhValidatedOrder(order: Order): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.url}/${order._id}/validate`, {}));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async RhRejectOrder(order: Order): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.url}/${order._id}/reject`, {}));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async commercialHandleOrderAction(order: Order, actionType: 'reject' | 'cancel'): Promise<QueryResult> {
    try {
      let endpoint;
      if (actionType === 'reject') {
        endpoint = `${this.url}/${order._id}/reject-order`;
      } else if (actionType === 'cancel') {
        endpoint = `${this.url}/${order._id}/cancel-order`;
      }

      return await lastValueFrom(
        this.http.patch<QueryResult>(endpoint, {
          reason: actionType === 'reject' ? order?.rejectReason : order?.cancelReason,
          type: actionType
        })
      );
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }



  async updateOrderJdeNumber(_id: string, order: Partial<Order>): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.url}/${_id}`, order));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

  async generatePurchaseOrder(id: string) {
    try {
      return await lastValueFrom(this.http.get<QueryResult>(`${this.url}/${id}/generate-purchase`));
    } catch (error) {
      return error;
    }
  }

  async rejectDemandToCancelOrder(_id: string, data: Partial<CancellationData>): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.url}/${_id}/reject-cancel-demand`, data));
    } catch (error) {
      return this.commonSrv.getError(await t('ACTION_FAILED'), error);
    }
  }

}
