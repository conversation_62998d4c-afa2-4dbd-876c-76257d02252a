import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuManagementItemsRoutingModule } from './menu-management-items-routing.module';
import { ManagementItemsComponent } from './management-items/management-items.component';
import { OrdersComponent } from './orders/orders.component';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PaginatorModule } from 'primeng/paginator';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { SharedModule } from 'src/app/shared/shared.module';
import { MenubarModule } from 'primeng/menubar';
import { CardModule } from 'primeng/card';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { AccordionModule } from 'primeng/accordion';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TreeSelectModule } from 'primeng/treeselect';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessagesModule } from 'primeng/messages';
import { AutoCompleteModule } from 'primeng/autocomplete';


@NgModule({
  declarations: [
    ManagementItemsComponent,
  ],
  imports: [
    CardModule,
    FormsModule,
    ToastModule,
    TableModule,
    SharedModule,
    CommonModule,
    DialogModule,
    TabViewModule,
    SidebarModule,
    TooltipModule,
    MenubarModule,
    MessagesModule,
    CalendarModule,
    InputTextModule,
    AccordionModule,
    PaginatorModule,
    TreeSelectModule,
    AutoCompleteModule,
    OverlayPanelModule,
    ConfirmDialogModule,
    InputTextareaModule,
    ProgressSpinnerModule,
    MenuManagementItemsRoutingModule
  ]
})
export class MenuManagementItemsModule { }
