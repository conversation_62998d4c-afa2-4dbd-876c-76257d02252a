{"manageStore-addtitle": "Add point of sale", "manageItemID-addtitle": "Add product ID", "manageStore-changetitle": "Modification of", "ADD_UNITTITLE": "Add unit", "ADD_USERTITLE": "Add a new user", "UPDATE_USERTITLE": "Update a new user", "CHANGE_UNITTITLE": "Change unit", "ADD_PACKTITLE": "Add packaging", "CHANGE_PACKTITLE": "Change packaging", "BTNPRODUCT": "Change", "BTNPRODUCTSave": "Save", "manageStore_city": "Select city", "manageStore_Region": "Select region", "enable": "Enable", "disable": "Disable", "Enable": "Enable", "Disable": "Disable", "THE": "in the", "OF-THE": "of the", "ADDFIDELITY": "add to Loyalty Program", "MOVEFIDELITY": "Remove to the Fidelity Program", "SOCIAL": "Select a Business Name", "PHONE": "Select a phone number ", "REMOVE": "loyalty Program Designations", "FIDELITY-ADD": "Assign distributor", "FIDELITY-REMOVE": "Remove distributor", "FIDELITY-PROGRAM": "Fidelity program", "ProductTitle": "Add product", "ProductTitleChange": "Modification of", "Img": "Import image", "CORRECT_FILL": "Please fill in an email and a password", "specifications": "Insert specifications", "advantages": "Insert Advantages", "Cautions": "Insert cautions", "status": "Status", "CONNECTION_DONE": "Connection done", "TAX": "Total before tax", "TVA": "VAT", "WELCOME": "Welcome", "TTC": "Total including VAT", "PAID": "Paid", "CREATED": "Created", "REJECTED": "Rejected", "FAILD": "Faild", "PREVALID": "Prevalided", "VALIDATED": "Validated", "CREDIT_REJECTED": "Returned", "CREDIT_IN_VALIDATION": "Awaiting", "CANCELLED": "Annuler", "CREDIT_IN_VALIDATION_DRH": "Awaiting DRH", "DISTRIBUT": "WholeSaler", "EMPTY": "No information", "ORDER_RECAP": "Summary of the order", "DISABLED_ADDRESS": "Disabling the address", "THIS_ADDRESS": "this address", "ACTION_FAILED": "An error occurred", "VALIDATION": "Order validation", "REJECTION": "Reject Order", "CANCELE": "Cancel order", "VALID_ACTION": "Validation done", "REJECT_ACTION": "Rejetion done", "REJECT_ACTION_SUCCES": "The order has been successfully rejected", "CANCEL_ACTION": "Cancel done", "CANCEL_ACTION_SUCCES": "The order has been successfully cancelled", "VERIFY_DONE": "Verification done", "CONFIRM_VALIDORDER": "Are you sure you want to validate this order?", "REJECTED-ORDER": "Order rejection ", "CONFIRM_REJECTED": "Are you sure you want to reject this order?", "CONFIRM_CANCEL": "Are you sure you want to cancel this order?", "DELETE": "Delete", "selectStatus": "Select status", "CONFIRM": "Confirm", "PackagingtType": "Packaging type", "EndPoint": "End point", "Sale-region": "Sale region", "StartRef": "Reference", "Modification": "Change user information's", "AddUser": "Add user", "ALLCOMPANY": "Companies", "COMPANY": "Company", "COMMERCIALS": "commercials", "ADMINISTRATORS": "administrators", "DONUT": "animators", "OF": "of", "Detail": "Detail", "Change": "Change", "UNAUTHORIZE": "You do not have the authorization", "FIND_ERROR": "One occurred", "CHANGE_CompanyTITLE": "Change company", "Detail_CompanyTITLE": "Company detail ", "Add_CompanyTITLE": "Add company", "PRECOMPTE": "Select a tax status", "selectRegion": "Select region", "selectCity": "Select city", "selectEmployeeType": "Select employee type", "selectStore": "Select store", "selectDirection": "Select direction", "selectService": "Select service", "selectname": "Select company name", "DetailUser": "User detail", "CLOSE": "Close", "CANCEL": "Cancel", "Filtrer": "Filter", "removal": "Removal type", "PriceOffer": "Change price offer ", "PriceOfferAdd": "Add price offer ", "Store": "Select store", "packaging": "Select packaging", "Product": "Select product", "Amount": "Insert amount", "CONFIRM-DISABLE": "Do you want", "CONFIRM-DISABLEUSER": "this user", "CREATE_ERROR": "Account creation error", "EMPTY_FILL": "Please insert email, phone number", "INVALIDE_EMAIL": "Invalid email", "CORRECT_EMAIL": "Please enter a valid email", "SELECT_ADDRESS": "Select address", "CREATE_ACOUNT": "Account created", "LOGO_ACCOUNT_SAVE": "Logo saved with success", "Done": "done", "BAG": "bag (25Kg)", "PLANIFICATIONTEXT": "Are you sure you want to disable this schedule?", "PRODUCT": "This Product", "PLANIFICATIONDONE": "Schedule deleted", "PRODUCTS": "products", "UNITS": "units", "PACKAGING": "this packaging", "CATEGORY": "this category", "OF_PRODUCT": "of Product", "OF_CATEGORY": "of category", "OF_UNIT": "of unit", "This_Store": "this store", "This_itemID": "this product ID", "UPDATE_ADDRESS": "Address updated", "OF_Store": "of store", "OF_ProductID": "of product ID", "CORRECT_SIZE": "Enter the correct telephone number format, e.g. 6xxxxxxxx", "PASSWORD": "Reset password of", "IMG_IMPORT": "Please import an image", "DISABLE_CONFIRM": "Confirm disable", "CONFIRM_PROCEED": "Confirm to proceed", "ARE_YOU_SURE": "Are you sure ?", "QSTDISABLE_CONFIRM": "Do you want to Disable this element?", "CREATE_UNIT": "Unit created", "INVALID_DATE": "Invalid date", "CREATE_PRODUCT": "Product created", "CREATE_Store": "Store created", "UPDATE_Store": "Store updated", "CREATE_PACKAGING": "packaging success created", "OF_PACKAGING": "of packaging", "VALID_ADDRESS": "Please enter a valid email", "FILL": "Please fill in all fields", "UNIT": "This unit", "SELECT_UNIT": "Select unit type", "INSERT_LABEL": "Insert un Label", "size_info": "Please ensure the correct format of the telephone number, eg: 6xxxxxxxx", "BLANK_SPACE": "Please fill in all fields", "Empty_FILL": "Missing data", "VALIDATED_ACCOUNT": "You are about to validate the account of", "CONFIRM_VALIDATION": "Account validation", "OTP_CONNECTION": "OTP Authentification", "MAIL_OTP": "Your OTP code are generated", "CONNECTION": "Connection error", "CONFIRM_DISABLECMPNY": "this company", "QUESTION_MODAL": " Do you want to disable this price offer", "HEADER_MODALUser": "Account disable", "QUESTION_MODALUSER": " Do you want to disable this user", "HEADER_MODAL": "Disable the price offer", "Account": "of the account", "DATA_RESET": "Reset done", "PARTICULAR": "particular", "ACTIVATE_PRICE": "Do you want to enable this price offer", "ACTIVATE_PRICEOFFER": "Enable price offer", "EMPLOYEE": "PASTA employee", "RESELLER": "Reseller", "CREATE_ADDRESS": "Address created", "CREATE_ADDRES": "Address created", "GETDATA_ERROR": "Get data error", "DISABLE_DONE": "Disabled done", "USER_INSERT": "Insert user company", "DATA_ERROR": "Data error", "FILL_BLANKSPACE": "Please enter the company name and her phone number", "CREATE_PRICE": "Creation done", "DIFF_PASSWORD": "Passwords are different", "UPDATE_PRICE": "Update done", "StartPoint": "Start point", "REJECT_ORDER_MESSAGE": "You are about to reject this order", "ERROR_INTERVAL_DATE": "Incorrect time interval !!", "OPERATION_DONNE": "Operation done", "INFO_CLIENT": "Customer information", "INFO_DELIVERY": "Delivery Information", "RENDER": "RENDER", "SELECT_RENDER": "Select a render type", "TYPE_RENDER": "Type of delivery", "DELIVERY_POINT": "Delivery point", "RETRIEVE_POINT": "Reirtrieve point", "ORDER_POINT": "order point", "SHIPPING_COST": "Shipping cost", "DISTRIBUTOR": "WholeSaler", "COMMERCIALREGION": "Commercial region", "POINT_NOT_VALIDATE": "Point not validated", "VALIDATE_POINT": "Point validated", "CHECK_QUANTITY_FIELDS": "Please enter all quantities", "CHECK_QUANTITY": "The quantity(s) entered must not be greater than the current quantity", "CHECK_QUANTITY_ZERO": "The quantity entered must not be zero", "CHECK_IMAGE_FOR_VALIDATE_RETAIL": "Please add at least one image as proof beforehand", "ERROR_MessageReject": "Please enter a rejection reason", "GENERATE_CODE": "Afriland code generation in progress...", "IMAGE_EXCEED": "You have exceeded the size of the images to add, please add smaller images...", "NOTAPPROVED": "NOTAPPROVED", "APPROV": "APPROVED", "SEIZURE": "SEIZURE", "AUTPUT": "AUTPUT", "INVOICED": "INVOICED", "REJECT": "REJECTED", "Company": "Company", "date": "Creation date", "Destination": "Destination", "Quantity": "Quantity", "Line": "Line", "List-of-removal-authorizations": "List of removal authorizations", "Loading-status:": "Loading status", "Order-number:": "Order number", "Product:": "Product", "Order-quantity:": "Order quantity", "Delivery-quantity:": "Delivery quantity", "Collection-request-date:": "Collection request date", "Carrier:": "Carrier", "Driver:": "Driver", "License-plate:": "License plate", "Pickup/Render:": "Pickup/render type", "Vehicle-registration:": "Vehicle registration", "Close": "Close", "Reset": "Reset", "AES-repartition": "AES repartition", "AES-pickup-status": "AES pickup status", "AES-status-Rendu": "AES status in render", "Evolution-of-pic": "Evolution of authorizations", "Breakdown-of-pro": "Breakdown of deliveries by product", "Authorization-reporting": "Authorization reporting", "Product-repartion": "Product repartition", "evolution-of-bl": "Evolution of delivery notes", "Breakdown-by-driver": "Breakdown by driver", "TITLE_MODAL_RESEND": "Creating the command in JDE with reference ", "CONFIRM_RESEND_ORDER_TO_JDE": "You are about to resend this command in JDE.", "GET_PROMO_CODE": "Error retrieving promo code. The promo code entered is invalid.", "FIELD_INPUT": "please fill in the required fields", "CODE_PROMO_CREER": "Promo Code created successfully", "GET_PROMO_CODE_FAILED": "Promo code failed to create", "START_DATE_BIGGER": "  The start date must not exceed the end date", "BAD_PROMO_CODE_FORMAT": "Please enter the correct promo code value format", "ENTER_NUMBER_IN_RANGE": "Enter a valid number between 0 and 100", "List-of-your-complaints": "List of your complaints", "List-of-notifications": "List of Notifications", "send-notif-success": "Notification send successfully", "ENTER-MESSAGE": "Please enter a message", "MESSAGE-SAVE": "Votre message a été enregistré.", "claim-detail": "Complaint detail", "reset": "Reset", "category": "Category", "subcategory": "Sub-category", "comment": "Comment", "download": "Download related attachment", "treat": "<PERSON><PERSON><PERSON>", "close": "Close", "EN_DATE": "End date", "START_DATE": "Start date", "SELECT_DATE": "Select date", "SELECT_STATUS": "Select status", "reference-claim": "Claim reference", "FILTER": "Filter", "EMPTY_CLAIM": "No claims found", "REFILL_ACCOUNT": "Account Refills", "PAYMENT_REQUEST_JDE": "Make a payment to JDE for the Refill", "CHECK_STATUS_REQUEST": "Check payment status to JDE for Refill", "ABOUT_TO_MAKE_PAYMENT_REQUEST": "you are about to make a payment to JDE", "ABOUT_TO_MAKE_STATUS_PAYMENT_REQUEST": "you are about to check the payment status for this refill at JDE", "VERIFY_PAYMENT_STATUS_TO_OPERATOR": "Check payment status to Operator for Refill", "ABOUT_TO_MAKE_STATUS_PAYMENT_REQUEST_OPERATOR": "you are about to check the payment status for this refill to the Operator", "CANCEL_JDE_NUMBER": "Removing the jde number from this order", "ABOUT_TO_CANCEL_JDE_NUMBER": "you are about to remove the jde number from this order", "JDE_NBER_DELETED": "Jde number deleted", "JDE_NUMBERS_CANCELED": "Jde number deleted successfully", "COMPANY_COMMERCIAL": "Select a commercial", "SELECT_OPTION": "Select option", "SELECT_PRODUCT": "Select product", "WAITING": "Awaitng", "WAITING_RH": "Awaitng RH", "WAITING_DRH": "Awaiting DRH", "EXPORT": "Export", "UPLOAD_FILE": "UPLOAD FILE FOR UPDATE ACCOUNT", "UPLOAD_FILE_BALANCE": "Select a file containing customer balances", "UPLOAD_FILE_RESULT": "Balance of customers as been updated successfully", "SEND": "Send", "UPDATE_OPTIONTITLE": "Update Option", "ADD_OPTIONTITLE": "Add Option", "NO_OPTION_SELECTED": "No options selected", "SELECT_GLOBAL_SHIPPING_ADDRESS": "select shpping address", "NO_ADDRESS_SELECTED": "NO address selected", "QUESTION_MODAL_QR_CODE": "Do you want to deactivate this QR code", "HEADER_MODAL_QR_CODE": "Disabling the Qr code", "ACTIVE": "Enable", "SCANNED": "Scanner", "USED": "Used", "INACTIVE": "Inactive", "ACTIVE_DONE": "Activate done", "DOWNLOAD": "Download", "manageQrcode-addtitle": "Generate QR codes", "UPDATE_DATE_EXPIRED_SUCCESS": "The expiry date has been successfully updated", "DATE_REQUIRED": "Start and end dates are mandatory.", "DATE_START_END": "Start date must be less than end date.", "DATE_INVALID": "Please check imported dates", "DATE_CHECK": "Check dates", "CLOSE_SUCCESS": "Closure successful", "MARKETPLACE_CLOSED": "The marketplace has been successfully opened", "UPDATE_DATE_EXPIRED": "Update expiration date", "CONFIRM_CLOSE_MARKETPLACE": "Are you sure you want to disable point redemption for products?", "CLOSE_MARKETPLACE": "Enabling point redemption for products", "VALIDATE": "Validate", "REWARD_REQUEST_NOT_FOUND": "Reward request not found", "REWARD_REQUEST_ALREADY_VALIDATED": "Reward request already validated", "REWARD_REQUEST_VALIDATED": "Reward request validated"}