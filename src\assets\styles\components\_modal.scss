@use "../utils/mixins" as *;

.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: rgba(31, 30, 30, 0.3);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);

  &-container {
    box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
    background-color: #fefefe;
    border: 1px solid #888;
    width: 30%;
    border-radius: 10px;

    &-content {
      padding: 3em;

      .title {
        font-size: var(--fz-h3);
        font-weight: var(--montserrat-semibold);
        margin-bottom: 1em;
      }

      textarea{
        margin: 0.5rem 0;
        width: 100%;
      }

      .input-group {
        margin-bottom: 0.5rem;

        .p-password {
          display: block !important;
          width: 100%;
        }

        input,
        .p-dropdown {
          background-color: var(--clr-tertiary-100);
        }

        .p-dropdown-label {
          padding: 0.7em;
        }

        input {
          margin: 0.5rem 0;
          width: 100%;
        }
      }
    }

    .footer-modal {
      padding: 1em;
      @include vertical-horizontal-center;
      gap: 10%;
      background-color: var(--clr-tertiary-100);
      border-radius: 0 0 20px 20px;

      .btn {
        padding: 0.5rem 2rem;

        @media screen and(max-width: 1320px) {
          padding: 0.5rem 1rem;
        }
      }
    }

    .close {
      position: absolute;
      right: 15px;
      top: 5px;
      color: #000;
      font-size: 35px;
      font-weight: bold;
    }

    .close:hover,
    .close:focus {
      color: var(--clr-secondary-400);
      cursor: pointer;
    }



    @media only screen and (max-width: 380px) {
      font-size: 8px !important;

      .h2-title {
        font-size: 24px !important;
      }
    }

    @media only screen and (max-width: 920px) {
      width: 48%;

      .header-modal-auth {
        .social-element {
          padding: 4px !important;

          img {
            max-width: 20px !important;
            max-height: 20px !important;
          }
        }

        .small-title {
          font-size: 10px !important;
          line-height: 10px;
          margin-bottom: 2px !important;
        }

        .small-text {
          padding: 0 6% 10px 6% !important;
          font-size: 13px !important;
        }

        .text {
          font-size: 8px !important;
        }
      }
    }

    @media only screen and (max-width: 520px) {
      width: 85%;

      .avatar {
        max-width: 22%;
      }

      h2,
      .container {
        padding: 10px 6%;
      }

    }
  }

  .animate {
    -webkit-animation: animatezoom 0.6s;
    animation: animatezoom 0.6s;
  }

  @-webkit-keyframes animatezoom {
    from {
      -webkit-transform: scale(0);
    }

    to {
      -webkit-transform: scale(1);
    }
  }

  @keyframes animatezoom {
    from {
      transform: scale(0);
    }

    to {
      transform: scale(1);
    }
  }

  @media screen and (max-width: 420px) {
    .modal-container {
      width: 70%;
    }
  }

  /* Change styles for span and cancel button on extra small screens */
  @media screen and (max-width: 300px) {
    span.psw {
      display: block;
      float: none;
    }

    .cancelbtn {
      width: 100%;
    }
  }
}

.basic-modal {
  padding: 0 3em;

  label {
    font-family: var(--montserrat-semibold)
  }
}

.footer-center-container {
  @include vertical-horizontal-around;
  gap: 2em;
}

.p-dialog {
  font-family: var(--montserrat-Regular) !important;

  .upload-file {
    /* Conteneur principal */
    @include vertical-horizontal-center;
    flex-direction: column;
    padding: 2rem;

    /* Zone de glisser-déposer */
    .file-upload-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 2rem;
      background-color: aliceblue;
      border-radius: 0.5rem;
      border: 2px dashed var(--clr-tertiary-400);
      margin-bottom: 1rem;
      line-height: 1.5rem;
      text-align: center;
      max-width: 80%;

      .file-upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
      }

      .file-upload-text {
        h3 {
          font-size: 1.25rem;
          margin-bottom: 0.5rem;
        }

        p {
          color: #6c757d;
          margin-bottom: 1rem;
        }
      }

      .file-upload-btn {
        .btn {
          display: inline-flex;
          align-items: center;
          padding: 0.5rem 1rem;

          i {
            margin-right: 0.5rem;
          }
        }
      }
    }

    /* Zone d'aperçu */
    .file-preview-container {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;

      .file-preview-box {
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);

        .file-preview-icon {
          font-size: 2rem;
          color: #28a745;
          margin-right: 1rem;
        }

        .file-preview-text {
          text-align: center;

          h4 {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
          }

          p {
            color: #6c757d;
          }
        }

        .file-error {
          color: var(--clr-danger-400);
          margin-top: 1rem;
          @include vertical-horizontal-center
        }
      }
    }

    /* Boutons d'action */
    .file-upload-actions {
      display: flex;
      justify-content: center;

      .btn {
        display: inline-flex;
        align-items: center;
        margin: 0 0.5rem;

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }


  .p-dialog-footer {
    @include vertical-horizontal-center;
    gap: 2em;

    button {
      font-family: var(--montserrat-Regular) !important;

    }
  }
}