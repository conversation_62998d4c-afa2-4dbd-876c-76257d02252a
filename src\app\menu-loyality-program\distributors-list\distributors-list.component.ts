import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { CompanyService } from 'src/app/menu-management-account/companie-account/company.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { HttpErrorResponse } from '@angular/common/http';
import { t } from 'src/app/shared/functions/global.function';
import { CompanyCategory } from 'src/app/shared/enums/Company-category.enum';
import { Company } from 'src/app/shared/models/company.models';
import { CommonService } from 'src/app/shared/services/common.service';
import { WholeSaleService } from 'src/app/shared/services/whole-sale.service';



@Component({
  selector: 'mcw-distributors-list',
  templateUrl: './distributors-list.component.html',
  styles: [
  ],
  providers: [ConfirmationService, MessageService],

})
export class DistributorsListComponent implements OnInit {


  isLoading: boolean;
  showSideBar: boolean;
  offset = 0;
  limit = 50;
  total: number;
  distributors: Company[] = [];
  showDialogDetail: boolean;
  showDialogExport: boolean;
  dataOrder: any;
  currentCompany: Company
  filterForm = {
    tel: 0,
    region: '',
    name: '',
    erpSoldToId: 0,
    category: CompanyCategory.Baker,
    isLoyaltyProgDistributor: true,
    enable: true,
  };
  dataForFilter: Company[]
  erpSoldToIdsForFilter: any;

  constructor(public companySrv: CompanyService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private companyService: CompanyService,
    private commonSrv: CommonService,
    private wholeSaleService: WholeSaleService,
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getDistributors();
    await this.getDataForFilter();
    await this.getElementsForFilters();
  }

  async getDataForFilter() {
    this.dataForFilter = (await this.companySrv.getAllCompanies({ projection: 'name,tel', ...this.filterForm }))?.data;
  }

  async reset(): Promise<void> {
    this.filterForm = {
      tel: 0,
      region: '',
      name: '',
      erpSoldToId: 0,
      category: CompanyCategory.Baker,
      isLoyaltyProgDistributor: true,
      enable: true,
    };
    this.offset = 0;
    this.limit = 50;
    await this.getDistributors();
  }


  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getDistributors();
  }

  async getDistributors(): Promise<boolean> {
    this.isLoading = true;

    const query = {
      limit: this.limit,
      offset: this.offset,
      ...this.filterForm
    };


    const wholeSale = await this.wholeSaleService.getWholeSale({
      limit: this.limit, offset: this.offset, enable: true
    });
    const result = await this.companySrv.getAllCompanies(query);

    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message,
      });
      return (this.isLoading = false);
    }
    if (wholeSale instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: wholeSale.error.message,
      });
      return (this.isLoading = false);
    }

    this.total = result?.count + wholeSale?.count;
    this.distributors = [...result?.data, ...wholeSale?.data];
    return (this.isLoading = false);
  }

  async getElementsForFilters() {
    const keyForFilters = ['erpSoldToId'];
    const companiesInfos = await this.commonSrv.getElementForFilterByKeys('companies', { keyForFilters })
    this.erpSoldToIdsForFilter = companiesInfos?.dataerpSoldToId;
  }

  async changeFidelityStatus(company: Company) {
    this.currentCompany = company;
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')}
      ${company.isLoyaltyProgDistributor ? await t('FIDELITY-REMOVE') : await t('FIDELITY-ADD')} ${company?.name}
      ${company?.isLoyaltyProgDistributor ? await t('OF-THE') : await t('THE')} ${await t('FIDELITY-PROGRAM')} ?`,
      header: `${await t('FIDELITY-PROGRAM')}`,
      icon: 'pi pi-info-circle',
      key: 'fidelity-program',
      accept: async () => {
        const res = await this.companyService.update({ _id: company?._id, isLoyaltyProgDistributor: !company?.isLoyaltyProgDistributor });
        await this.getDistributors();
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${company.isLoyaltyProgDistributor ? await t('REMOVE') : await t('ADDFIDELITY')} ${await t('Done')}` : res.data,
          detail: '' + res?.message,
        });
      },

    });
    this.currentCompany = null;
  }

  async exportToExcel() {
    this.isLoading = true;
    await this.getDistributors();
    this.dataOrder = this.distributors.map(elt => {
      const data = {};
      data['Raison social'] = elt?.name || 'N/A';
      data['Commercial region'] = elt?.address?.region;
      data['Régistre de commerce'] = elt?.rccm;
      data['Solto'] = elt?.erpSoldToId;
      data['Région'] = elt?.address?.region;
      data['Ville'] = elt?.address?.city;
      data['Telephone'] = elt?.tel;
      data['Nombre de revendeur'] = elt?.loyaltyProgDistributor?.nbResellers
      return data;
    });
    this.commonSrv.exportRetriveExcelFile(this.dataOrder, 'Liste des commandes revendeurs');
    this.isLoading = false;
  }
}
