<div class="orders-reporting-page page-container container">
  <div class="orders-reporting-page-container">
    <div class="header-reporting">
      <div class="title-container">
        <h2 class="title">Statistiques sur le programme de fidélité</h2>

        <div class="section-button">
          <button type="button" pButton label="Réinitialiser" icon="pi pi-replay" class="p-button-text p-button-warning"
            (click)="reset(); refresh()">
          </button>

          <button type="button" (click)="sideBarDisplay= true" pButton class="p-button-secondary" i18n-label=""
            label="Filtre" icon="pi pi-filter">
          </button>

          <button type="button" (click)="showDialogArchive = true" pButton class="p-button-danger" i18n-label=""
            label="Archives les points" icon="pi pi-inbox">
          </button>

        </div>
      </div>
    </div>

    <div class="orders-card-detail">
      <div class="container-main-bloc">
        <!-- <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataAnimatorVolume"></mcw-progress-bar>

          <ul>
            <li>Classement des Meilleurs AB</li>
            <p-table [value]="dataAnimatorVolume">
              <ng-template pTemplate="header">
                <tr>
                  <th>N°</th>
                  <th>Nom de l'AB</th>
                  <th>Nombre de Mamies</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-volume let-i="rowIndex">
                <tr>
                  <td>{{ i + 1 }}</td>
                  <td>{{ volume?.name }}</td>
                  <td><span>{{ volume?.totalMamiCount }}</span></td>
                </tr>
              </ng-template>
            </p-table>
          </ul>
        </div> -->
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataTotalPoints"></mcw-progress-bar>
          <ul>
            <li> Classement des clients indirects </li>
            <p-table [value]="dataTotalPoints">
              <ng-template pTemplate="header">
                <tr>
                  <th>N°</th>
                  <th>CLIENT</th>
                  <th>Point</th>
                  <th>Niveau de fidelité</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-total let-i="rowIndex">
                <tr>
                  <td>{{i+1}}</td>
                  <td>{{total?.clientName}}</td>
                  <td><span>{{ total?.totalPoints }}</span></td>
                  <span [innerHTML]="total?.statusFidelity | loyaltyProgramLevelColor"></span>
                </tr>
              </ng-template>
            </p-table>
          </ul>
        </div>
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataDistributorVolume"></mcw-progress-bar>
          <ul>
            <li>Classement des distributeurs</li>
            <p-table [value]="dataDistributorVolume">
              <ng-template pTemplate="header">
                <tr>
                  <th>N°</th>
                  <th>CLIENT</th>
                  <th>Volume</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-volume let-i="rowIndex">
                <tr>
                  <td>{{ i+1 }}</td>
                  <td>{{ volume?.supplierName }}</td>
                  <td><span>{{ volume?.totalBags }} Sacs</span></td>
                </tr>
              </ng-template>
            </p-table>
          </ul>
        </div>
      </div>

      <div class="container-main-bloc">
        <!-- <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataStateOrder"></mcw-progress-bar>
          <ul>
            <li> Commandes </li>
            <li> Total commandé <br> <br>
              <span> {{dataStateOrder?.totalOrderedQuantitySum}} Sacs</span>
            </li>
            <li>Nombre de commandes<br> <br>
              <span>{{dataStateOrder?.totalOrder}} </span>
            </li>
            <li>Validation commerciale<br> <br>
              <span>{{dataStateOrder?.totalOrderValidated}} </span>
            </li>
            <li>Commandes non validées<br> <br>
              <span>{{dataStateOrder?.totalOrderPending}}</span>
            </li>
            <li>Commandes rejeté<br> <br>
              <span> {{dataStateOrder?.totalOrderRejected}}</span>
            </li>

          </ul>
        </div> -->
        <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataAnimatorVolume"></mcw-progress-bar>

          <ul>
            <li>Classement des Meilleurs AB</li>
            <p-table [value]="dataAnimatorVolume">
              <ng-template pTemplate="header">
                <tr>
                  <th>N°</th>
                  <th>Nom de l'AB</th>
                  <th>Région</th>
                  <th>Nombre de Mamies</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-donutAnimator let-i="rowIndex">
                <tr>
                  <td>{{ i + 1 }}</td>
                  <td>{{ donutAnimator?.name }}</td>
                  <td>{{ donutAnimator?.commercialRegion }}</td>
                  <td><span>{{ donutAnimator?.totalMamiCount }}</span></td>
                </tr>
              </ng-template>
            </p-table>
          </ul>
        </div>

        <div class="table">
          <mcw-progress-bar *ngIf="isLoading && !dataStateProduct"></mcw-progress-bar>
          <ul>
            <li>Chiffres par produit</li>
            <li *ngFor="let data of dataStateProduct">
              <strong>
                {{ data?.packagingLabel || data?.packaging }} -
                {{ data?.productName | truncateString: 30 }}
              </strong>
              <br /><br />
              <span>{{ data?.totalQuantity }} Sacs</span>
            </li>
          </ul>
        </div>

      </div>
      <div class="orders-reporting-body">
        <div class="order-reporting-user">
          <div class="reporting-user">
            <mcw-progress-bar *ngIf="isLoading && !dataProductEvolutions"></mcw-progress-bar>
            <p-card>
              <ng-template pTemplate="header">
                <h3>Évolutions des ventes par produit</h3>
                <div class="right-block">
                  <!-- <div class="pdroSelect">
                    <p-dropdown [options]="chartTypes" [(ngModel)]="chartTypeProduct" placeholder="Mois"
                      (onChange)="generateDataEvolutionsProducts()" class="pdroSelect" optionLabel="name"
                      optionValue="key" [showClear]="true">
                    </p-dropdown>
                  </div> -->
                </div>
              </ng-template>
              <p-chart type="line" [data]="productData" width="100%" height="40vh">
              </p-chart>
            </p-card>
          </div>
        </div>

        <div class="order-reporting-product">
          <div class="reporting-product">
            <mcw-progress-bar *ngIf="isLoading && !dataEanPoint"></mcw-progress-bar>
            <p-card>
              <ng-template pTemplate="header">
                <h3>Evolution des Points</h3>
                <div class="right-block">
                  <!-- <div class="pdroSelect">
                    <p-dropdown [options]="chartTypes" [(ngModel)]="charType" placeholder="Mois"
                      (onChange)="generateDataEvolutionsPoint($event)" class="pdroSelect" optionLabel="name"
                      optionValue="key" [showClear]="true">
                    </p-dropdown>
                  </div> -->
                </div>
              </ng-template>
              <p-chart type="line" [data]="pointData" width="100%" height="40vh"> </p-chart>
            </p-card>

          </div>
        </div>
      </div>
    </div>

    <p-sidebar [(visible)]="sideBarDisplay" position="right">
      <section class="filter-sidebar">
        <section class="header-filter">
          <button type="button" pButton i18n-label="@@historyOrders-Reset" label="Réinitialiser" (click)="reset()"
            icon="pi pi-replay" class="p-button-text p-button-warning"></button>
        </section>

        <section class="body-filter">
          <form class="form">
            <div class="input-group">
              <label for="calendar" i18n="@@historyOrders-List_Date">Date de début</label>
              <p-calendar [(ngModel)]="filterForm.startDate" dateFormat="dd/mm/yy" InputId="clendar"
                i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
            </div>
            <div class="input-group">
              <label for="calendar" i18n="@@historyOrders-ListEndDate">Date de fin</label>
              <p-calendar [(ngModel)]="filterForm.endDate" dateFormat="dd/mm/yy" InputId="clendar"
                i18n-placeholder="@@historyOrders-List_dateStart" placeholder="Sélectionner une date"></p-calendar>
            </div>
            <!-- <div class="input-group">
            <label for="status">Statut commande </label>
            <p-dropdown [options]="orderStatus" optionLabel="name" optionValue="code" [(ngModel)]="filterForm.status"
              [showClear]="true" placeholder="Sélectionner un statut">
            </p-dropdown>
          </div> -->
            <div class="input-group">
              <label for="status" i18n="@@produit">Produit </label>
              <p-dropdown name="type" [options]="productsForFilters" optionLabel="label" optionValue="label"
                [showClear]="true" [filter]="true" filterBy="label" [(ngModel)]="filterForm.product"
                i18n-placeholder="@@productselect" placeholder="Sélectionner un produit">
              </p-dropdown>
            </div>

            <div class="input-group">
              <label for="status" i18n="@@region">Region</label>
              <p-dropdown name="type" [options]="region" optionLabel="name" optionValue="code"
                [(ngModel)]="filterForm.region" i18n-placeholder="@@regionselect" placeholder="Sélectionner une region">
              </p-dropdown>
            </div>

          </form>
        </section>

        <section class="footer-filter">
          <button pButton pRipple type="button" i18n-label="@@historyOrders-ListOut" label="Annuler" icon="pi pi-times"
            class="p-button-outlined p-button-secondary" (click)=" sideBarDisplay = false">
          </button>
          <button pButton pRipple type="button" i18n-label="@@historyOrders-ListFilter" label="Filtrer"
            icon="pi pi-search" class="p-button-success" (click)="refresh(); sideBarDisplay= false">
          </button>
        </section>
      </section>

    </p-sidebar>

    <p-dialog header="Archivages des points" [(visible)]="showDialogArchive" [modal]="true" [style]="{width: '25%'}"
      [resizable]="true">
      <div class="archive-container">
        <p> En cliquant sur le bouton Archiver, les points des revendeurs seront archivés.</p> <br>
        <label class="label" for="male">Entrer le nom de la session.</label> <br>
        <input type="text" pInputText [(ngModel)]="session" placeholder="Entrer le nom de la session">

      </div>

      <div class="dialog-foot">
        <button icon="pi pi-times" class="btn btn-tertiary btn-icon-inline" (click)="showDialogArchive = false">
          <i class="pi pi-times"></i> ANNULER
        </button>
        <button class="btn btn-primary btn-icon-inline" label="ACHIVER" icon="pi pi-check"
          [disabled]="!session || session.length < 2 || session.length > 5" (click)="archivedPoint()">
          <i class="pi" [ngClass]="isLoadingInModal ? 'pi pi-spin pi-spinner' : 'pi pi-check'"></i> ACHIVER
        </button>
      </div>
    </p-dialog>
  </div>


  <p-toast></p-toast>