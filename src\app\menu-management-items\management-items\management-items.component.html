<div class="account-list-page container page-container">
  <div class="account-list-page-container">
    <div class="header-list">
      <div class="title-container">
        <h2 class="title" i18n="@@manageProductTitle-0">Gestion des {{title}}</h2>
        <div class="section-button menubar">
          <button type="button" pButton i18n-label="@@manageProductReset" label="Réinitialiser" icon="pi pi-replay"
            class="p-button-text p-button-warning margin-rigth" (click)="reset()">
          </button>
          <button type="button" pButton class="p-button-secondary margin-rigth" i18n-label="@@manageProductfilter"
            label="Filtre" icon="pi pi-filter" (click)="showSideBar = true">
          </button>
          <button type="button" pButton i18n-label="@@manageProductTitle-add" label="Ajouter" icon="pi pi-plus"
            class="p-button-danger" (click)="op.toggle($event)">
          </button>
          <button [disabled]="products.length < 0" type="button" pButton i18n-label="@@manageProductTitle-configure"
            label="Configurer la disponibilité" icon="pi pi-calendar-plus" class="p-button-info"
            (click)="ops.toggle($event)">
          </button>
          <p-overlayPanel class="overlayPanel" #ops [style]="{width: '200px'}" [dismissable]="true"
            [hideTransitionOptions]="'1ms'">
            <ng-template pTemplate>
              <div class="align-column">
                <div class="btn btn-icon btn-icon-edit" i18n="@@manageProductCategory"
                  [ngStyle]="!timeRemaining ? {'pointer-events': 'none', 'opacity': '0.6'} : {}"
                  (click)="openMarketPlace()">
                  Ouvrir la marketplace
                </div>
                <div class="btn btn-icon btn-icon-edit" i18n="@@manageProductProduct" (click)="isExpired = true">
                  Fermer la marketplace
                </div>
              </div>
            </ng-template>
          </p-overlayPanel>
          <p-overlayPanel class="overlayPanel" #op [style]="{width: '110px'}" [dismissable]="true"
            [hideTransitionOptions]="'1ms'">
            <ng-template pTemplate>
              <div class="align-column">
                <div class="btn btn-icon btn-icon-edit" (click)="openAddCategoryModal(); op.hide()"
                  i18n="@@manageProductCategory"
                  *ngIf="this.commonSrv.user.authorizations.includes(categoryAction?.CREATE)">
                  Catégorie(s)
                </div>
                <div class="btn btn-icon btn-icon-edit" (click)="openAddProductModal(); op.hide()"
                  i18n="@@manageProductProduct"
                  *ngIf="this.commonSrv.user.authorizations.includes(itemsAction?.CREATE)">
                  Produit(s)
                </div>
              </div>
            </ng-template>
          </p-overlayPanel>
        </div>
      </div>
    </div>

    <div class="align-container product-list ">
      <div class="align-paginator padding">
        <div class="title-h4" *ngIf="timeRemaining">Temps restant : <strong
            class=" color-timer">{{timeRemaining}}</strong></div>
        <p-paginator (onPageChange)="paginate($event)" [rows]="limit" [totalRecords]="total"
          [showJumpToPageDropdown]="true" [showPageLinks]="false">
        </p-paginator>
        <div class="title-h4">Total: {{total}}</div>
      </div>

      <p-tabView (onChange)="handleTabview(indexTab)" class='product-list' [(activeIndex)]="indexTab">

        <p-tabPanel i18n-header="@@manageProductProd" header="Produits">
          <div class="list-container">
            <div class="cards" *ngIf="products?.length > 0 || isLoading">
              <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
              <p-card>
                <div class="card-container ">
                  <div class="product-side">
                    <div class="product" *ngFor='let item of products'
                      (click)='showUpdateProduct(item); isDetail = true'>
                      <div class="product-image">
                        <img [src]="item.image" alt="">
                        <div class="fidelity-overlay" *ngIf="getCurrentAdvantageId(item._id)">
                          <div class="fidelity-badge" [ngClass]="getFidelityClass(getCurrentAdvantageId(item._id))">
                            <i class="pi pi-star-fill"></i>
                            <span>{{getAdvantageLabelById(getCurrentAdvantageId(item._id))}}</span>
                          </div>
                        </div>
                      </div>

                      <label>{{item?.name}} ({{item?.sku}})- {{item?.price }} XAF</label>
                      <div class="product-card-footer">
                        <div class="fidelity-status" *ngIf="!getCurrentAdvantageId(item._id)">
                          <i class="pi pi-star"></i>
                          <span>Aucun niveau de fidélité</span>
                        </div>
                      </div>
                    </div>
                    <div class="product" (click)='openAddProductModal()'
                      *ngIf="this.commonSrv.user.authorizations.includes(itemsAction?.CREATE)">
                      <i class="pi pi-plus-circle width"></i>
                      <label i18n="@@manageProduct-add_product">Ajouter un produit</label>
                    </div>
                  </div>
                </div>
              </p-card>
            </div>
            <div *ngIf="products?.length <= 0 && !isLoading">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@manageProductEmpty">
                  Aucun produit trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>
        <p-tabPanel i18n-header="@@manageProductCateg" header="Catégories">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="categories" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="categories?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="max-width:50px">N°</th>
                  <th style="max-width:100px" i18n="@@manageProduct_Code">Sku</th>
                  <th style="max-width:150px" i18n="@@manageProduct_Name">Nom</th>
                  <th style="max-width:350px" i18n="@@manageProduct_Description">Description</th>
                  <th style="max-width:200px" i18n="@@manageProduct_Create">Date création</th>
                  <th class="iconsBtn action">Actions</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-category let-i="rowIndex">
                <tr>
                  <td style="max-width:50px">{{i + offset + 1}}</td>
                  <td style="max-width:100px">
                    {{category?.code || 'N/A' | truncateString:10}}
                  </td>
                  <td style="max-width:150px">
                    {{category?.label || 'N/A' | truncateString:30}}
                  </td>
                  <td style="max-width:350px">
                    {{category?.description || 'N/A' | truncateString:50}}
                  </td>
                  <td style="max-width:200px">
                    {{category?.created_at | date : 'dd/MM/yyyy' | truncateString:20 }}
                    à {{category?.created_at | date : 'shortTime' : '' : 'fr'}}
                  </td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@manageProductCategEdit"
                            pTooltip="Modifier cette catégorie" tooltipPosition="top"
                            (click)="openUpdateCategoryModal(category)"
                            *ngIf="this.commonSrv.user.authorizations.includes(categoryAction?.UPDATE) && category?.enable === true">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon" (click)="changeStateOfCategory(category)"
                            [pTooltip]="category?.enable? ('Disable' | translate | async) : ('Enable' | translate | async)"
                            tooltipPosition="top"
                            [ngClass]="category?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            *ngIf="this.commonSrv.user.authorizations.includes(categoryAction?.DELETE) || category?.enable === true">
                            <i [class]="category.enable? 'pi pi-eye' : 'pi pi-eye-slash'"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="categories?.length <= 0 && !isLoading">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@manageCategoryEmptyList">
                  Aucune catégorie trouvée
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="showSideBar" position="right">
  <section class="filter-sidebar">
    <section class="header-filter">
      <span></span>
      <button type="button" pButton i18n-label="@@manageProduct_Reset" label="Réinitialiser" icon="pi pi-replay"
        class="p-button-text p-button-warning" (click)="reset()"></button>
    </section>
    <section class="body-filter">
      <form class="form">
        <div class="input-group">
          <label for="status">{{'status' | translate | async}} </label>
          <p-dropdown name="type" [options]="statusAccount" optionValue="code" optionLabel="name"
            [(ngModel)]="filterForm.enable" i18n-placeholder="@@historyOrders-List_select"
            placeholder="{{'Sélectionner un statut'}}">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label i18n="@@manageProduct_Category">Catégorie </label>
          <p-dropdown name="type" [options]="categoriesLabel" optionValue="label" optionLabel="label" [filter]="true"
            [showClear]="true" filterBy="label" [(ngModel)]="filterForm.labelCateg"
            i18n-placeholder="@@manageProduct_Category" placeholder="Choisir la catégorie">
          </p-dropdown>
        </div>

        <div class="input-group">
          <label i18n="@@manageProduct_Name">Nom </label>
          <p-dropdown name="type" [options]="productNames" optionValue="name" optionLabel="name" [filter]="true"
            [showClear]="true" filterBy="name" [(ngModel)]="filterForm.name" i18n-placeholder="@@manage-Product"
            placeholder="Nom produit">
          </p-dropdown>

          <!-- <input type="text" pInputText i18n-placeholder="@@manage-Product" placeholder="Nom produit"
              [(ngModel)]="filterForm.label" /> -->
        </div>

        <div class="input-group">
          <label i18n="@@manageProduct_ref">Sku</label>
          <p-dropdown name="type" [options]="productSku" optionValue="sku" optionLabel="sku" [filter]="true"
            [showClear]="true" filterBy="sku" [(ngModel)]="filterForm.sku" i18n-placeholder="@@manageProduct--ref"
            placeholder="sku du Produit">
          </p-dropdown>
          <!-- <input type="text" pInputText i18n-placeholder="@@manageProduct--ref" placeholder="Reférence Produit"
              [(ngModel)]="filterForm.erpRef" /> -->
        </div>
      </form>
    </section>
    <section class="footer-filter ">
      <button pButton pRipple type="button" i18n-label="@@manageProduct--Title" label="Annuler" icon="pi pi-times"
        class="p-button-outlined p-button-secondary" (click)=" showSideBar = false">
      </button>
      <button pButton pRipple type="button" i18n-label="@@manageProduct_Title" label="Filtrer" icon="pi pi-search"
        [loading]="isLoading" class="p-button-success" (click)="handleTabview(indexTab); showSideBar = false">
      </button>
    </section>
  </section>
</p-sidebar>

<div class="add-dialog">
  <p-dialog position="center" [(visible)]="display" [modal]="true">
    <p-card class="insert-info" *ngIf="isEdit && !isDetail">
      <ng-template class="form-header" pTemplate="header" i18n="@@manageProductTitle1">
        {{selectedProduct?._id ? ('ProductTitleChange'| translate | async)+ ' ' + selectedProduct.name:
        ('ProductTitle'|
        translate | async)}}
      </ng-template>
      <div class="add-container">
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProduct_Category">Catégorie</label>
          <p-dropdown size="medium" type="text" class="form-input" [options]="categories" optionValue="code"
            optionLabel="label" [filter]="true" [showClear]="true" filterBy="label" [readonly]="!isEdit"
            [(ngModel)]="selectedProduct.category.code" i18n-placeholder="@@manageProduct_Category"
            placeholder="Choisir la catégorie">
          </p-dropdown>
        </div>
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProduct--name">Nom du produit</label>
          <input pInputText fullWidth size="medium" type="text" class="form-input" [(ngModel)]="selectedProduct.name"
            [readonly]="!isEdit">
        </div>
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProductNorm">sku du produit</label>
          <input pInputText fullWidth size="medium" type="text" class="form-input" [(ngModel)]="selectedProduct.sku"
            [readonly]="!isEdit">
        </div>
        <div class="rows">
          <div class="product-form">
            <label for="otherTrainingType" i18n="@@manageProductRef1">Prix unitaire</label>
            <input pInputText fullWidth size="medium" type="number" class="form-input"
              [(ngModel)]="selectedProduct.price" [readonly]="!isEdit">
          </div>

          <div class="product-form">
            <label for="otherTrainingType" i18n="@@manageProductDesc">Description</label>
            <textarea class="form-input" i18n-placeholder="@@manageProductinsertdesc"
              placeholder="Entrer une description" pInputTextarea [rows]="3" [cols]="10"
              [(ngModel)]="selectedProduct.description" [readonly]="!isEdit">
              </textarea>
          </div>
          <div class="product-form">
            <label for="fidelityStatus">Niveau de fidélité (privilège)</label>
            <ng-container *ngIf="advantagesList as advList">
              <p-dropdown id="fidelityStatus"
                [options]="[{label: 'Retirer le niveau de fidélité', value: 0}].concat(advList)" optionLabel="label"
                optionValue="statusValue" [(ngModel)]="selectedAdvantage"
                placeholder="Sélectionner un niveau de fidélité" [disabled]="!isEdit"
                (onChange)="onFidelityStatusChange(selectedProduct._id, getCurrentAdvantageId(selectedProduct._id))">
              </p-dropdown>
              <small class="fidelity-info" *ngIf="!getCurrentAdvantageId(selectedProduct._id)">
                <i class="pi pi-info-circle"></i>
                Ce produit n'a actuellement aucun niveau de fidélité assigné
              </small>
            </ng-container>
          </div>
          <!-- <p-tabView (onChange)='handleChangeTabProduct($event)'>
              <p-tabPanel i18n-header="@@manageProductspecif" header="Spécifications">
                <div class="product-forme">
                  <div class="col-12 md:col-4">
                    <div class="p-inputgroup">
                      <textarea placeholder="{{selectedProduct?.specifications ||('specifications' | translate | async)}}"
                        pInputTextarea [rows]="2" [cols]="15" [(ngModel)]="selectedSpecification" [readonly]="!isEdit">
                      </textarea>
                      <span i18n-pTooltip="@@manageProductTitle" pTooltip="Ajouter" tooltipPosition="top"
                        style="cursor: pointer" class="p-inputgroup-addon"
                        (click)="insert(selectedSpecification,selectedAdvantages,selectedCaution)">+</span>
                    </div>
                  </div>

                </div>
              </p-tabPanel>
              <p-tabPanel i18n-header="@@manageProductTitle01" header="Avantages">
                <div class="product-forme">
                  <div class="col-12 md:col-4">
                    <div class="p-inputgroup">
                      <textarea i18n-placeholder="@@manageProductAdvantages"
                        placeholder="{{selectedProduct?.advantages||('advantages' | translate | async)}}" pInputTextarea
                        [rows]="2" [cols]="15" [(ngModel)]="selectedAdvantages"></textarea>
                      <span i18n-pTooltip="@@manageProductAdd0" pTooltip="Ajouter" tooltipPosition="top"
                        class="p-inputgroup-addon"
                        (click)="insert(selectedSpecification,selectedAdvantages,selectedCaution)">+</span>
                    </div>
                  </div>
                </div>
              </p-tabPanel>
              <p-tabPanel i18n-header="@@manageProductCaution" header="Précautions">
                <div class="product-forme">
                  <div class="col-12 md:col-4">
                    <div class="p-inputgroup">
                      <textarea i18n-placeholder="@@manageProductinsertCaution"
                        placeholder="{{selectedProduct?.cautions || ('Cautions' | translate | async)}}" pInputTextarea
                        [rows]="1" [cols]="15" [(ngModel)]="selectedCaution"></textarea>
                      <span i18n-pTooltip="@@manageProductAdd2" pTooltip="Ajouter" tooltipPosition="top"
                        class="p-inputgroup-addon"
                        (click)="insert(selectedSpecification,selectedAdvantages,selectedCaution)">+</span>
                    </div>
                  </div>

                </div>
              </p-tabPanel>
            </p-tabView> -->
          <div class="product-form" *ngIf="!isImage">
            <button (click)="compressFile()" i18n="@@manageProductAddPicture">
              <i class="pi pi-download iconsBtn clr-primary-400 icon-space"> <span>
                  {{'Img' |translate |async}}</span></i>
            </button>
          </div>
          <div class="product-form" *ngIf="isImage && selectedProduct.image">
            <p-progressSpinner class="small-spinner" [ngClass]="{'display-none': !isLoading}"
              styleClass="custom-spinner" strokeWidth="2" animationDuration="1s"></p-progressSpinner>
            <img src="{{selectedProduct?.image}}" i18n-alt="@@manageProduct" alt="produit" (click)="compressFile()"
              class="img-size">
          </div>
        </div>
      </div>
      <ng-template pTemplate="footer">
        <div class="product-button">
          <button nbButton class="btn btn-tertiary iconsBtn button" status="basic" [disabled]="isLoading"
            (click)="close()" i18n="@@manageProductOut">
            <i class="pi pi-times"></i>
            Annuler
          </button>
          <button nbButton outline status="success" class="btn btn-primary iconsBtn button" [disabled]="isLoading"
            (click)='selectedProduct?._id? updateProduct() : addProduct()' i18n="@@manageProductsave"
            [disabled]="isLoadingInModal">
            <i class="pi" [ngClass]="isLoadingInModal ? 'pi-spin pi-spinner' : 'pi-check'">
            </i>
            {{selectedProduct?._id? ('BTNPRODUCT' | translate | async) : ('BTNPRODUCTSave' | translate | async)}}
          </button>
        </div>
      </ng-template>
    </p-card>
    <p-card class="insert-info Product-detail" *ngIf="isDetail">
      <div *ngIf="selectedProduct?._id">
        <h2 class="form-header" i18n="@@manageProductDetails">
          Détail du produit {{selectedProduct?.name | truncateString:10}}
        </h2>
        <div class="header-button">
          <div i18n-pTooltip="@@manageProductUpdate1" pTooltip="Modifier" tooltipPosition="top" class="iconsBtn size"
            (click)="showUpdateProduct(selectedProduct); isDetail = false"
            *ngIf="this.commonSrv.user.authorizations.includes(itemsAction?.UPDATE)">
            <i class="pi pi-pencil " [ngStyle]="{'color': 'var(--clr-primary-300)'}">
            </i>
          </div>
          <div class="iconsBtn size disabled" i18n-pTooltip="@@manageProductDisablle0"
            [pTooltip]="selectedProduct?.enable? 'Désactiver': 'Activer'" tooltipPosition="top"
            (click)="changeStateOfProduct(selectedProduct)"
            *ngIf="this.commonSrv.user.authorizations.includes(itemsAction?.DELETE)">
            <i [class]="selectedProduct?.enable? 'pi pi-trash' : 'pi pi-eye'"
              [ngStyle]="{'color': selectedProduct?.enable? 'var(--clr-secondary-400)' : 'var(--clr-secondary-300)'}">
            </i>
          </div>
        </div>
      </div>
      <div class="product">
        <img [src]="selectedProduct.image" alt="" class="img-size">
      </div>
      <div class="product-form">
        <label for="otherTrainingType" i18n="@@manageProduct_Category">Catégorie</label>
        <p-dropdown size="medium" type="text" class="form-input" [options]="categories" [disabled]="true"
          optionValue="code" optionLabel="label" [filter]="true" [showClear]="true" filterBy="label"
          [readonly]="!isEdit" [(ngModel)]="selectedProduct.category.code" i18n-placeholder="@@manageProduct_Category"
          placeholder="Choisir la catégorie">
        </p-dropdown>
      </div>
      <div class="product-form">
        <label for="otherTrainingType" i18n="@@manageProduct--name">Nom du produit</label>
        <input pInputText fullWidth size="medium" type="text" class="form-input" [disabled]="true"
          [(ngModel)]="selectedProduct.name" [readonly]="!isEdit">
      </div>
      <div class="product-form">
        <label for="otherTrainingType" i18n="@@manageProductNorm">sku du produit</label>
        <input pInputText fullWidth size="medium" [disabled]="true" type="text" class="form-input"
          [(ngModel)]="selectedProduct.sku" [readonly]="!isEdit">
      </div>
      <div class="rows">
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProductRef1">Prix unitaire</label>
          <input pInputText fullWidth [disabled]="true" size="medium" type="number" class="form-input"
            [(ngModel)]="selectedProduct.price" [readonly]="!isEdit">
        </div>

        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProductDesc">Description</label>
          <textarea class="form-input" [disabled]="true" i18n-placeholder="@@manageProductinsertdesc"
            placeholder="Entrer une description" pInputTextarea [rows]="3" [cols]="10"
            [(ngModel)]="selectedProduct.description" [readonly]="!isEdit">
            </textarea>
        </div>
      </div>
      <!-- <div class="product labelspace">
          <p-accordion>
            <p-accordionTab i18n-header="@@manageProductAdvantage" header="Advantages">
              <div *ngFor="let advantage of selectedProduct?.advantages">-{{advantage}}</div>
            </p-accordionTab>
            <p-accordionTab i18n-header="@@manageProduct_specification" header="Spécifications">
              <div *ngFor="let specification of selectedProduct?.specifications">- {{specification}}</div>
            </p-accordionTab>
            <p-accordionTab i18n-header="@@manageProductCautions" header="Cautions">
              <div *ngFor="let caution of selectedProduct?.cautions">-{{caution}}</div>
            </p-accordionTab>
          </p-accordion>
        </div> -->
      <ng-template pTemplate="footer">
        <div class="product-button">
          <button nbButton class="btn btn-tertiary iconsBtn button" status="basic" [disabled]="isLoading"
            (click)="close()" i18n="@@manageProductClose">
            <i class="pi pi-times"></i>
            Fermer
          </button>
        </div>
      </ng-template>
    </p-card>
  </p-dialog>
</div>


<div class="add-dialog">
  <p-dialog position="center" [(visible)]="isCategory" [modal]="true">
    <p-card class="insert-info">
      <ng-template class="form-header" pTemplate="header">
        {{selectedCategory?._id ? ('CHANGE_CATEGORYTITLE' | translate | async) + ' ' + selectedCategory?.label :
        ('ADD_CATEGORYTITLE' | translate | async)}}
      </ng-template>
      <div class="add-container">
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProduct_Code">Code</label>
          <input pInputText fullWidth size="medium" type="text" class="form-input"
            i18n-placeholder="@@modalCategCodePlacehold" placeholder="Insérer le code"
            [(ngModel)]="selectedCategory.code" [readonly]="!isEdit">
        </div>
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProduct_Name">Nom</label>
          <input pInputText fullWidth size="medium" type="text" class="form-input"
            i18n-placeholder="@@modalCategLabelPlacehold" placeholder="Insérer le libellé"
            [(ngModel)]="selectedCategory.label" [readonly]="!isEdit">
        </div>
        <div class="product-form">
          <label for="otherTrainingType" i18n="@@manageProduct_Description">Description</label>
          <textarea class="form-input" i18n-placeholder="@@modalCategDescPlacehold" placeholder="Entrer une description"
            pInputTextarea [rows]="4" [cols]="10" [(ngModel)]="selectedCategory.description" [readonly]="!isEdit">
            </textarea>
        </div>
      </div>
      <ng-template pTemplate="footer">
        <div class="product-button">
          <button nbButton class="btn btn-tertiary iconsBtn button" status="basic" (click)="close()"
            i18n="@@titleUpdatemodalOutUnit">
            <i class="pi pi-times"></i>
            Annuler
          </button>

          <button nbButton outline status="success" class="btn btn-primary iconsBtn button"
            (click)='selectedCategory?._id? updateCategory(selectedCategory) : addCategory()'
            [disabled]="isLoadingInModal">
            <i class="pi" [ngClass]="isLoadingInModal ? 'pi-spin pi-spinner' : 'pi-check'">
            </i>
            {{selectedCategory?._id? ('BTNPRODUCT' | translate | async) : ('BTNPRODUCTSave' | translate | async)}}
          </button>
        </div>
      </ng-template>
    </p-card>
  </p-dialog>
</div>

<div class="add-dialog">
  <p-dialog position="center" [(visible)]='isExpired' [modal]="true">
    <p-card class="insert-info">
      <ng-template class="form-header" pTemplate="header">
        Déterminez la période d'indisponibilité des récompenses
      </ng-template>

      <div class="add-container">
        <div class="product-form">
          <label for="dateStart" i18n="@@manageProduct--name">Date de début</label>
          <input pInputText fullWidth size="medium" type="date" class="form-input" id="dateStart"
            [(ngModel)]="dateStart" [min]="today">
        </div>

        <div class="product-form">
          <label for="dateEnd" i18n="@@manageProductNorm">Date de fin</label>
          <input pInputText fullWidth size="medium" type="date" class="form-input" id="dateEnd" [(ngModel)]="dateEnd"
            [min]="today">
        </div>
      </div>
      <ng-template pTemplate="footer">
        <div class="product-button">
          <button nbButton class="btn btn-tertiary iconsBtn button" status="basic" [disabled]="isLoading"
            (click)="isExpired=false" i18n="@@manageProductOut">
            <i class="pi pi-times"></i> Annuler
          </button>

          <button nbButton outline status="success" class="btn btn-primary iconsBtn button"
            [disabled]="isLoadingInModal" i18n="@@manageProductsave" (click)="updateMarketPlaceHours()">
            <i class="pi" [ngClass]="isLoadingInModal ? 'pi-spin pi-spinner' : 'pi-check'"></i>
            {{ 'BTNPRODUCTSave' | translate | async }}
          </button>
        </div>
      </ng-template>
    </p-card>
  </p-dialog>
</div>

<p-confirmDialog i18n-acceptLabel="@@titleUpdatemodalDisableUnit" [style]="{width: '40vw'}"
  i18n-rejectLabel="@@titleUpdatemodalOutUnit" rejectLabel="Annuler"
  rejectButtonStyleClass="p-button-text bg-tertiary-400 clr-default-400"
  [acceptButtonStyleClass]="filterForm?.enable? 'bg-secondary-400 border-secondary-400' : ''" defaultFocus="none"
  [acceptIcon]="isLoading ? 'pi pi-spin pi-spinner' : 'pi pi-check'">
</p-confirmDialog>

<p-toast></p-toast>