import {
  StatusOrderPipe,
  CategoryClientPipe,
  ColorStatusOrderPipe,
  StatusOrderRetailPipe,
  colorCategoryClientPipe,
  ColorRenderTypeOrderPipe,
  PromoCodeStatusColorPipe,
  RenderTypeStatusOrderPipe,
  ColorStatusOrderRetailPipe,
  StatusOrderEmployeesPipe,
} from './pipes/status-order.pipe';
import { NgModule } from '@angular/core';
import { ToastModule } from 'primeng/toast';
import { FormsModule } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { MenubarModule } from 'primeng/menubar';
import { TranslatePipe } from './pipes/translate.pipe';
import { HttpClientModule } from '@angular/common/http';
import { ProgressBarModule } from 'primeng/progressbar';
import { FormatNumberPipe } from './pipes/format-number.pipe';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TruncateStringPipe } from './pipes/truncate-string.pipe';
import { GetActionMeaningPipe } from './pipes/get-action-meaning.pipe';
import { HeaderComponent } from './components/header/header.component';
import { AuthModalComponent } from './components/auth-modal/auth-modal.component';
import { OrdersDetailComponent } from './components/orders-detail/orders-detail.component';
import { ProgressBarComponent } from './components/progress-bar/progress-bar.component';
import { ColorCategoriesPipe } from './pipes/color-categories.pipe';
import { CategoryToStringPipe, CategoryUserNamePipe } from './pipes/category-user-name.pipe';
import { OrderaRetailDetailComponent } from './components/ordera-retail-detail/ordera-retail-detail.component';
import { AuthorizationRemovalPipe, ColorAuthorizationRemovalPipe, GetFreightHandlingCodePipe } from './pipes/authorization-removal.pipe';
import { FeedbackPipe, FeedbackPipeColor } from './pipes/feedback.pipe';
import { ErrorStatusColorPipe, ErrorStatusPipe, OperatorStatusColorPipe, TransactionStatusColorPipe, TransactionStatusPipe } from './pipes/transaction-status.pipe';
import { AuthOtpModalComponent } from './components/auth-otp-modal/auth-otp-modal.component';
import { BlockLogoComponent } from './components/block-logo/block-logo.component';
import { FileUploadModule } from 'primeng/fileupload';
import { OrderItemDetailsComponent } from './components/order-item-details/order-item-details.component';
import { LoyaltyProgramPipe, LoyaltyProgramColorPipe } from './pipes/loyalty-program.pipe';
import { ColorStatusQrCodePipe, StatusQrCodePipe } from './pipes/status-qr-code.pipe';
@NgModule({
  declarations: [
    TranslatePipe,
    StatusOrderPipe,
    HeaderComponent,
    FormatNumberPipe,
    AuthModalComponent,
    AuthOtpModalComponent,
    CategoryClientPipe,
    TruncateStringPipe,
    ColorCategoriesPipe,
    ColorStatusOrderPipe,
    CategoryUserNamePipe,
    GetActionMeaningPipe,
    ProgressBarComponent,
    OrdersDetailComponent,
    OrderItemDetailsComponent,
    ColorRenderTypeOrderPipe,
    PromoCodeStatusColorPipe,
    RenderTypeStatusOrderPipe,
    CategoryToStringPipe,
    OrderaRetailDetailComponent,
    StatusOrderRetailPipe,
    StatusOrderEmployeesPipe,
    colorCategoryClientPipe,
    ColorRenderTypeOrderPipe,
    PromoCodeStatusColorPipe,
    RenderTypeStatusOrderPipe,
    ColorStatusOrderRetailPipe,
    AuthorizationRemovalPipe,
    ColorAuthorizationRemovalPipe,
    GetFreightHandlingCodePipe,
    FeedbackPipe,
    FeedbackPipeColor,
    ErrorStatusPipe,
    ErrorStatusColorPipe,
    TransactionStatusPipe,
    OperatorStatusColorPipe,
    TransactionStatusColorPipe,
    BlockLogoComponent,
    OrderItemDetailsComponent,
    LoyaltyProgramPipe,
    LoyaltyProgramColorPipe,
    StatusQrCodePipe,
    ColorStatusQrCodePipe
  ],
  imports: [
    FormsModule,
    ToastModule,
    CommonModule,
    MenubarModule,
    ButtonModule,
    FileUploadModule,
    HttpClientModule,
    ProgressBarModule,
    ProgressSpinnerModule,
  ],
  exports: [
    TranslatePipe,
    HeaderComponent,
    StatusOrderPipe,
    FormatNumberPipe,
    CategoryClientPipe,
    LoyaltyProgramPipe,
    LoyaltyProgramColorPipe,
    TruncateStringPipe,
    ColorCategoriesPipe,
    GetActionMeaningPipe,
    ProgressBarComponent,
    ColorStatusOrderPipe,
    CategoryUserNamePipe,
    OrdersDetailComponent,
    OrderItemDetailsComponent,
    StatusOrderRetailPipe,
    colorCategoryClientPipe,
    ColorRenderTypeOrderPipe,
    CategoryToStringPipe,
    PromoCodeStatusColorPipe,
    RenderTypeStatusOrderPipe,
    StatusOrderEmployeesPipe,
    OrderaRetailDetailComponent,
    ColorStatusOrderRetailPipe,
    AuthorizationRemovalPipe,
    ColorAuthorizationRemovalPipe,
    GetFreightHandlingCodePipe,
    FeedbackPipe,
    FeedbackPipeColor,
    ErrorStatusPipe,
    ErrorStatusColorPipe,
    TransactionStatusPipe,
    OperatorStatusColorPipe,
    TransactionStatusColorPipe,
    StatusQrCodePipe,
    ColorStatusQrCodePipe
  ],
  providers: [
    MessageService
  ]
})
export class SharedModule { }
