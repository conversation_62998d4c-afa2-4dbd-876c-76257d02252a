import { MessageService } from 'primeng/api';
import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { OrderStatus, PaymentMode } from 'src/app/shared/models/order';
import { t } from 'src/app/shared/functions/global.function';
import { PaymentModeColor, PaymentModeLib } from 'src/app/shared/enums/paymentMode.enum';
import { OrdersreportingService } from './services/ordersreporting.service';
import { CompanyCategory, CompanyCategoryColor } from 'src/app/shared/enums/Company-category.enum';
import { ProductService } from 'src/app/menu-administration/management-products/services/product.service';
import { Product } from 'src/app/shared/models/product.model';
import { StoresService } from 'src/app/menu-administration/management-stores/stores.service';
import { Store } from 'src/app/shared/models/store.model';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserCategory } from 'src/app/shared/enums/user-category.enum';
import { User } from 'src/app/shared/models/user.models';
import { ReportingAction } from 'src/app/shared/actions';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'mcw-orders-reporting',
  templateUrl: './orders-reporting.component.html',
  styles: [
  ]
})

export class OrdersReportingComponent implements OnInit {

  typeChart: boolean = false;
  valueChartType = 'linear';

  typeChart1: boolean = false;
  valueChartType1 = 'line';

  typeChart2: boolean = false;
  valueChartType2 = 'line';

  showDialogExport: boolean

  typeChart3: boolean = false;
  valueChartType3 = 'line';

  userData: any;
  user: User;
  regionData: any;
  productData: any;
  paimentData: any;
  userDataDoughnut: any;
  regionDataDoughnut: any;
  productDataDoughnut: any;
  paymentDataDoughnut: any;
  TotalSales: any[];
  sideBarDisplay: boolean;
  isLoading: boolean;
  products: Product[];
  options: any;
  filterForm = {
    status: '',
    startDate: 0,
    endDate: 0,
    product: '',
    region: '',
    user: '',
    payment: '',
    storeRef: ''
  };

  userCategories = [
    { name: 'Baker', code: CompanyCategory.Baker },
    { name: 'Grossiste', code: CompanyCategory.Baker },
    { name: 'GMS', code: CompanyCategory.GMS },
    { name: 'Export', code: CompanyCategory.EXPORT },
    { name: 'Industrielle', code: CompanyCategory.Industry },
    { name: 'Group', code: CompanyCategory.Group },
  ];

  userCategoriesEn = [
    { name: 'Baker', code: CompanyCategory.Baker },
    { name: 'WholeSaler', code: CompanyCategory.Baker },
    { name: 'GMS', code: CompanyCategory.GMS },
    { name: 'Industry', code: CompanyCategory.Industry },
    { name: 'Group', code: CompanyCategory.Group },
    { name: 'EXPORT', code: CompanyCategory.EXPORT },
  ];

  paymentModes = [
    { name: 'Mon Compte', code: PaymentMode.MY_ACCOUNT },
    { name: 'AFRILAND', code: PaymentMode.AFRILAND },
    { name: 'ORANGE MONEY', code: PaymentMode.ORANGE_MONEY },
    { name: 'MOBILE MONEY', code: PaymentMode.MOBILE_MONEY },
    { name: 'EXPRESS UNION', code: PaymentMode.EXPRESS_EXCHANGE },
    { name: 'CREDIT', code: PaymentMode.CREDIT },

  ];

  paymentModesEn = [
    { name: 'MY ACCOUNT', code: PaymentMode.MY_ACCOUNT },
    { name: 'AFRILAND', code: PaymentMode.AFRILAND },
    { name: 'ORANGE MONEY', code: PaymentMode.ORANGE_MONEY },
    { name: 'MOBILE MONEY', code: PaymentMode.MOBILE_MONEY },
    { name: 'EXPRESS UNION', code: PaymentMode.EXPRESS_EXCHANGE },
    { name: 'CREDIT', code: PaymentMode.CREDIT },
  ];

  orderStatus = [
    { name: 'créé', code: OrderStatus.CREATED },
    { name: 'payé', code: OrderStatus.PAID },
    { name: 'validé', code: OrderStatus.VALIDATED },
    { name: 'échec', code: OrderStatus.FAILD },
    { name: 'CRÉDIT POUR SOLDE INSUFFISANT', code: OrderStatus.CREDIT_IN_AWAIT_VALIDATION },
    { name: 'CRÉDIT EN VALIDATION', code: OrderStatus.CREDIT_IN_VALIDATION },
    { name: 'CRÉDIT REJETÉ', code: OrderStatus.CREDIT_REJECTED },
  ];

  orderStatusEn = [
    { name: 'CREATED', code: OrderStatus.CREATED },
    { name: 'PAID', code: OrderStatus.PAID },
    { name: 'VALIDATED', code: OrderStatus.VALIDATED },
    { name: 'FAILED', code: OrderStatus.FAILD },
    { name: 'CRÉDIT FOR LOW BALANCE', code: OrderStatus.CREDIT_IN_AWAIT_VALIDATION },
    { name: 'CREDIT IN VALIDATION', code: OrderStatus.CREDIT_IN_VALIDATION },
    { name: 'CREDIT REJECTED', code: OrderStatus.CREDIT_REJECTED },
  ];

  region = [
    { name: 'LSO', code: 'LSO' },
    { name: 'GNO', code: 'GNO' },
    { name: 'GNO 1', code: 'GNO 1' },
    { name: 'GNO 2', code: 'GNO 2' },
    { name: 'CS', code: 'CS' },
    { name: 'ONO', code: 'ONO' },
  ];

  months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
  enploy: any[] = [{ name: 'Particulier', }, { name: 'Compagnie' }];
  selectedenploy: any;

  orderNumber: number;
  AmountOrder: number;
  orderNotValid: any;
  dataTotalSell: any;
  dataEvolutionSalesProduct: any;

  rankingsData: any;
  userRecapData: any;
  orderRecapData: any;
  cementVolumesData: any;
  reportingAction = ReportingAction;
  language = this.baseHref?.replace(/\//g, '');
  stores: Store[];
  key: any;
  sellAprove: boolean = false;
  userAcountAproved: boolean = false;
  companyValueAprove: boolean = false;
  volumCimentAprove: boolean = false;
  avg: any;

  constructor(private messageService: MessageService,
    private orderService: OrdersreportingService,
    private productService: ProductService,
    private storeSrv: StoresService,
    private commonSrv: CommonService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    this.userCategories = (this.language === 'en') ? this.userCategoriesEn : this.userCategories;
    this.paymentModes = (this.language === 'en') ? this.paymentModesEn : this.paymentModes;
    this.orderStatus = (this.language === 'en') ? this.orderStatusEn : this.orderStatus;

    this.products = (await this.productService.getProducts()).data;
    this.stores = (await this.storeSrv.getStores({ projection: 'storeRef,label' }))?.data;
    await this.refresh();
    this.user = this.commonSrv.user;
  }

  async reset(): Promise<void> {
    this.filterForm = {
      status: '',
      startDate: 0,
      endDate: 0,
      product: '',
      region: '',
      user: '',
      payment: '',
      storeRef: ''
    };
    await this.refresh();
  }

  async refresh(): Promise<void> {
    if ((this.filterForm.startDate || this.filterForm.endDate)
      && (this.filterForm.startDate > this.filterForm.endDate)) {
      this.messageService.add({ severity: 'error', summary: '', detail: await t('ERROR_INTERVAL_DATE') });
    }
    await this.getTotalSales();
    await this.getUserReporting();
    await this.getRankings();
    await this.getCementSalesVolumes();
    await this.generateDataForEvolutionSalesUsers();
    await this.generateDataForEvolutionSalesProducts();
    await this.generateDataEvolutionSalesRegions();
    await this.generateDataForEvolutionSalesPayments();
    await this.getAvgTime();

  }

  async getTotalSales() {
    this.isLoading = true;
    this.TotalSales = await this.orderService.getTotalSales({ ...this.filterForm });
    if(this.TotalSales instanceof HttpErrorResponse || this.TotalSales instanceof Error) {
      return this.messageService.add({ severity: 'error', summary: `${await t('ACTION_FAILED')}`, detail: this.TotalSales['error']?.message });
    }
    this.orderNumber = this.totalOrders();
    this.orderNotValid = this.TotalSales.find(elt => elt._id === 200)?.NumberOfOrders;
    this.isLoading = false;
  }

  allUserData() {
    this.regionData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      datasets: [
        {
          label: 'LSO',
          data: [65, 59, 80, 81, 56, 55, 40, 23, 34, 54, 64, 33],
          fill: false,
          borderColor: '#1f77b4',
          backgroundColor: '#1f77b4',
          tension: 0.4
        },
        {
          label: 'GNO',
          data: [28, 48, 40, 19, 86, 27, 90, 45, 56, 7, 8, 9],
          fill: false,
          backgroundColor: '#ff7f0e',
          borderColor: '#ff7f0e',
          tension: 0.4
        },
        {
          label: 'GNO 1',
          data: [23, 44, 46, 18, 88, 45, 78, 98, 80, 68, 35, 67],
          fill: false,
          backgroundColor: '#2ca02c',
          borderColor: '#2ca02c',
          tension: 0.4
        },
        {
          label: 'GNO 2',
          data: [89, 45, 5, 83, 68, 98, 86, 9, 63, 43, 87, 23],
          fill: false,
          backgroundColor: '#d62728',
          borderColor: '#d62728',
          tension: 0.4
        },
        {
          label: 'CS',
          data: [12, 34, 56, 78, 43, 21, 76, 87, 34, 65, 54, 43],
          fill: false,
          backgroundColor: '#9467bd',
          borderColor: '#9467bd',
          tension: 0.4
        },
        {
          label: 'ONO',
          data: [45, 78, 12, 67, 89, 45, 23, 67, 45, 87, 45, 12],
          fill: false,
          backgroundColor: '#8c564b',
          borderColor: '#8c564b',
          tension: 0.4
        }
      ]
    };
  }


  async getUserReporting(): Promise<void> {
    this.isLoading = true;
    this.userRecapData = await this.orderService.getTotalUsers({ ...this.filterForm });
    this.isLoading = false;
  }

  async getExportRecap(): Promise<void> {
    this.isLoading = true;
    this.orderRecapData = await this.orderService.getExportRecap({ ...this.filterForm });

    const dataExport = this.orderRecapData?.map((item) => {
      const data = {}
      data['Periodes'] = item?.monthYear;
      data['Montant totales des ventes (Xaf)'] = item?.totalSalesAmount;
      data['Volume totale des produits vendu (Tone)'] = item?.cementVolumeSold;

      return data;

    })


    if (this.sellAprove) {

      this.getTotalSales();
      const data = [];
      data.push({ 'Total des ventes (XAF)': this.AmountOrder, 'Nombre de commandes': this.AmountOrder, 'Nombre de commandes non valides ': this.orderNotValid })
      this.commonSrv.exportRetriveExcelFile(data, 'hello')

    }

    if (this.userAcountAproved) {

      this.getUserReporting();

      const dataExpor = this.userRecapData?.dataUserCategories?.map((item) => {
        const data = {}
        data['Utilisateure'] = UserCategory[item?._id] ?? CompanyCategory[item?._id];
        data['Totale utilisateurs'] = item?.totalUser;
        return data;

      })
      this.commonSrv.exportRetriveExcelFile(dataExpor, 'User');

    }

    if (this.companyValueAprove) {

      this.getRankings();

      const dataExpor = this.rankingsData?.map((item) => {
        const data = {}
        data['Societé'] = item?.label;
        data['Totale achats (Tonne)'] = item?.totalValue;
        return data;

      })
      this.commonSrv.exportRetriveExcelFile(dataExpor, 'recap achats  utilisateur');

    }

    if (this.volumCimentAprove) {

      this.getCementSalesVolumes();

      const dataExpor = this.rankingsData?.map((item) => {
        const data = {}
        data['Label'] = item?.label;
        data['Totale achats'] = item?.totalValue;
        return data;

      })
      this.commonSrv.exportRetriveExcelFile(dataExpor, 'recap achat produit');

    }


    this.commonSrv.exportRetriveExcelFile(dataExport, 'Recapitulatif');
    this.isLoading = false;
    this.reset();
  }

  async getRankings(): Promise<void> {
    try {
      this.isLoading = true;
      const data = await this.orderService.getRankings(this.filterForm);
      this.rankingsData = data.map((item: { totalValue: any; }) => ({
        ...item,
        totalValue: Number(item.totalValue)
      }));
    } catch (error) {
      console.error('Error in getRankings:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async getCementSalesVolumes(): Promise<void> {
    this.isLoading = true;
    this.cementVolumesData = await this.orderService.getCementSalesVolumes({ ...this.filterForm });
    this.isLoading = false;
  }

  async generateDataForEvolutionSalesUsers(): Promise<void> {
    try {
      this.isLoading = true;

      // Récupération des données pour le graphique en lignes
      const data = await this.orderService.getSalesEvolutionByUsers({ ...this.filterForm });
      this.userData = {
        labels: this.months, // Assurez-vous que `this.months` contient bien les mois dans le bon ordre
        datasets: []
      };

      for (const key in data) {
        const color = CompanyCategoryColor[CompanyCategory[key]];
        this.userData.datasets.push({
          label: key,
          data: data[key], // Assurez-vous que `data[key]` est un tableau de valeurs numériques correspondant à chaque mois
          fill: false,
          tension: 0.4,
          backgroundColor: color,
          borderColor: color,
        });
      }

      // Récupération des données pour le diagramme circulaire
      const dataDoughnut = await this.orderService.getSalesEvolutionByUsers({ ...this.filterForm, isDoughnut: 'true' });

      const totals = dataDoughnut?.reduce((acc, item) => acc + item?.total, 0);

      const labels = dataDoughnut?.map(item => {
        const category = CompanyCategory[item?._id];
        const percentage = totals > 0 ? ((item?.total / totals) * 100).toFixed(2) : 0;
        return `${category} ${percentage}% (${item?.total?.toFixed(2)} Tonnes)`;
      });

      const percentages = dataDoughnut?.map(item => totals > 0 ? (item?.total / totals) * 100 : 0);

      const colors = dataDoughnut?.map(item => CompanyCategoryColor[item?._id]);

      this.userDataDoughnut = {
        labels: labels,
        datasets: [{
          data: percentages,
          backgroundColor: colors,
          borderColor: colors,
        }]
      };
      this.isLoading = false;
    } catch (error) {

    }
  }


  async generateDataForEvolutionSalesProducts(): Promise<void> {
    this.isLoading = true;
    const dataEvolutionSalesProduct = await this.orderService.getSalesEvolutionByProduct({ ...this.filterForm });
    this.productData = {
      labels: this.months,
      datasets: []
    };

    const colors = ['#0071a6', '#b2b29e', '#fdfda5', '#143c5d', '#ee2f3d', '#036178'];
    const labelColorMap: { [key: string]: string } = {}; // To store label-color pairs

    for (const key in dataEvolutionSalesProduct) {
      const i = Object.keys(dataEvolutionSalesProduct).findIndex(elt => elt === key);
      const color = colors[i] ? colors[i] : `rgb(${(255 % (i + 5))},${(255 / (i + 1))} , ${(255 - (i + 10))})`;
      labelColorMap[key] = color;

      this.productData.datasets.push({
        label: key,
        data: dataEvolutionSalesProduct[key],
        fill: false,
        tension: .4,
        backgroundColor: color,
        borderColor: color,
      });
    }

    const dataDoughnut = await this.orderService.getSalesEvolutionByProduct({ ...this.filterForm, isDoughnut: true });
    const productNames = ['LA GENOISE', 'AMIGO', 'PELICAN', 'COLOMBE', 'INDUSTRIELLE', 'LA CAMEROUNAISE', 'SEMOULE', 'BISCUITIERE'];
    const filteredDataDoughnut = dataDoughnut.filter(item => productNames.includes(item?.label));
    const totals = filteredDataDoughnut.reduce((acc, item) => acc + item.total, 0);
    const labels = filteredDataDoughnut.map(item => `${item?.label} ${((item?.total / totals) * 100).toFixed(2)}% (${item?.total} Tonnes)`);
    const percentages = filteredDataDoughnut.map(item => (item?.total / totals) * 100);

    this.productDataDoughnut = {
      labels: labels,
      datasets: [{
        data: percentages,
        backgroundColor: filteredDataDoughnut.map(item => labelColorMap[item.label] || '#000000'),
        borderColor: filteredDataDoughnut.map(item => labelColorMap[item.label] || '#000000'),
      }]
    };

    this.isLoading = false;
  }

  generateRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }


  async generateDataEvolutionSalesRegions(): Promise<void> {
    this.isLoading = true;
    const dataEvolutionSalesRegion = await this.orderService.getSalesEvolutionByRegions({ ...this.filterForm });
    this.regionData = {
      labels: this.months,
      datasets: []
    };

    const colors = ['#0071a6', '#b2b29e', '#fdfda5', '#143c5d', '#ee2f3d', '#036178'];
    for (const key in dataEvolutionSalesRegion) {
      const i = Object.keys(dataEvolutionSalesRegion).findIndex(elt => elt === key);
      this.regionData.datasets.push({
        label: key,
        data: dataEvolutionSalesRegion[key],
        fill: false,
        tension: .4,
        backgroundColor: colors[i] ? colors[i] : `rgb(${(255 % (i + 5))},${(255 / (i + 1))} , ${(255 - (i + 10))})`,
        borderColor: colors[i] ? colors[i] : `rgb(${(255 % (i + 5))},${(255 / (i + 1))} , ${(255 - (i + 10))})`,
      });
    }

    const dataDoughnut = await this.orderService.getSalesEvolutionByRegions({ ...this.filterForm, isDoughnut: true });
    const totals = dataDoughnut.reduce((acc, item) => acc + item.total, 0);
    const labels = dataDoughnut.map(item => `${item.label} ${((item.total / totals) * 100).toFixed(2)}% (${item.total} tonne)`);
    const percentages = dataDoughnut.map(item => (item.total / totals) * 100);
    this.regionDataDoughnut = {
      labels: labels,
      datasets: [{
        data: percentages,
        backgroundColor: colors,
        borderColor: colors,
      }]
    };
    for (const key in dataDoughnut) {
      this.regionDataDoughnut.datasets[0].data.push(dataDoughnut[key]);
    }

    this.options = {
      maintainAspectRatio: false,
      aspectRatio: 0.8,
      plugins: {
        tooltips: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          stacked: true,
        },
        y: {
          stacked: true,
        }
      }
    };
    this.isLoading = false;
  }

  async generateDataForEvolutionSalesPayments(): Promise<void> {
    this.isLoading = true;
    const data = await this.orderService.getSalesEvolutionByPaiements({ ...this.filterForm });
    this.paimentData = {
      labels: this.months,
      datasets: []
    };

    for (const key in data) {
      const i = Object.keys(data).findIndex(elt => elt === key);
      this.paimentData.datasets.push({
        label: key,
        data: data[key],
        fill: false,
        backgroundColor: PaymentModeColor[PaymentMode[key]],
        borderColor: PaymentModeColor[PaymentMode[key]],
        tension: .4
      })
    }

    const dataDoughnut = await this.orderService.getSalesEvolutionByPaiements({ ...this.filterForm, isDoughnut: true });
    const totals = dataDoughnut.reduce((acc, item) => acc + item.total, 0);

    const labels = dataDoughnut.map(item => `${PaymentModeLib[item._id]} ${((item.total / totals) * 100).toFixed(2)}% (${item.total} xaf)`);
    const percentages = dataDoughnut.map(item => (item.total / totals) * 100);
    const colors = dataDoughnut.map(item => PaymentModeColor[item._id]);
    this.paymentDataDoughnut = {
      labels: labels,
      datasets: [{
        data: percentages,
        backgroundColor: colors,
        borderColor: colors,
      }]
    };
    for (const key in dataDoughnut) {
      this.paymentDataDoughnut.datasets[0].data.push(dataDoughnut[key]);
      this.paymentDataDoughnut.datasets[0].backgroundColor.push(PaymentModeColor[PaymentMode[key]]);
      this.paymentDataDoughnut.datasets[0].borderColor.push(PaymentModeColor[PaymentMode[key]]);
    }
    this.isLoading = false;
  }

  totalSales(): number {
    let initialValue = 0;
    let sum = this.TotalSales?.reduce((accumulator, curValue) =>
      accumulator + curValue?.totalSale, initialValue)
    return sum;
  }

  totalOrders(): number {
    let initialValue = 0;
    let sum = this.TotalSales?.reduce(function (accumulator, curValue) {
      return accumulator + curValue.NumberOfOrders
    }, initialValue)
    return sum;
  }

  private async getAvgTime() {
    this.isLoading = true;

    try {
      const res = await this.orderService. getOrderAverageTime({ ...this.filterForm });
      this.avg = res?.[0] ?? null;

      if (this.avg?.averageDelayInMilliseconds) {
        this.avg.averageDelayInMinutes = this.avg.averageDelayInMilliseconds / 60000;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération du temps moyen:', error);
      this.avg = null;
    } finally {
      this.isLoading = false;
    }
  }

  formatAverageTime(): { hours: number, minutes: number } {
    if (!this.avg?.averageDelayInMinutes) {
      return { hours: 0, minutes: 0 };
    }

    const totalMinutes = this.avg.averageDelayInMinutes;
    return {
      hours: Math.floor(totalMinutes / 60),
      minutes: Math.floor(totalMinutes % 60)
    };
  }
}








