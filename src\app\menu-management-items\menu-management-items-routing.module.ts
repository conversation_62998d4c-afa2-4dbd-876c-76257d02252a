import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ManagementItemsComponent } from './management-items/management-items.component';

const routes: Routes = [
  {
    path: '',
    component: ManagementItemsComponent
  },
  {
    path: 'orders',
    loadChildren: () => import('./orders/orders.module').then(m => m.OrdersModule)
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MenuManagementItemsRoutingModule { }
