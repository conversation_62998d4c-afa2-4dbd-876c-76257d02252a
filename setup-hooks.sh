#!/bin/bash

HOOKS_DIR="hooks"
GIT_HOOKS_DIR=".git/hooks"

declare -A HOOKS
HOOKS["pre-commit"]="$HOOKS_DIR/pre-commit/pre-commit.sh"
HOOKS["pre-push"]="$HOOKS_DIR/pre-push/pre-push.sh"

for hook in "${!HOOKS[@]}"; do
  SCRIPT_PATH="${HOOKS[$hook]}"
  DEST="$GIT_HOOKS_DIR/$hook"

  # Supprimer l'ancien hook s'il existe
  if [ -e "$DEST" ] || [ -L "$DEST" ]; then
    rm "$DEST"
  fi

  # Créer un wrapper qui appelle le vrai script
  echo "#!/bin/bash" > "$DEST"
  echo "bash \"$(pwd)/$SCRIPT_PATH\"" >> "$DEST"
  chmod +x "$DEST"
  chmod +x "$SCRIPT_PATH"

  echo "🔗 Hook $hook lié avec succès"
done

echo "✅ Tous les hooks sont maintenant actifs."
