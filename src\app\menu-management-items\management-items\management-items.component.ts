import { PackagingAction } from 'src/app/shared/actions/packaging-authorization.action';
import { ProductAction } from 'src/app/shared/actions/product-authorization.action';
import { UnitAction } from 'src/app/shared/actions/unit-authorization.action';
import { CommonService } from 'src/app/shared/services/common.service';
import { Packaging } from 'src/app/shared/models/packaging.model';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Product } from 'src/app/shared/models/product.model';
import { t } from 'src/app/shared/functions/global.function';
import { Component, Inject, OnInit } from '@angular/core';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { User } from 'src/app/shared/models/user.models';
import { Unit } from 'src/app/shared/models/unit.model';
import { APP_BASE_HREF } from '@angular/common';
import { Router } from '@angular/router';
import { NgxImageCompressService } from 'ngx-image-compress';
import { Category } from 'src/app/shared/models/category.model';
import { CategoryAction } from 'src/app/shared/actions/category-authorization.action';
import { UnitService } from 'src/app/menu-administration/management-products/services/unit.service';
import { ProductService } from 'src/app/menu-administration/management-products/services/product.service';
import { CategoryService } from 'src/app/menu-administration/management-products/services/category.service';
import { PackagingService } from 'src/app/menu-administration/management-products/services/packaging.service';
import { ItemsAction } from 'src/app/shared/actions/items.action';
import { Items, MarketplaceStatus } from 'src/app/shared/models/items.model';
import { MarketPlaceService } from '../services/market-place.service';
import * as moment from 'moment';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'mcw-management-items',
  templateUrl: './management-items.component.html',

  providers: [MessageService, ConfirmationService]

})
export class ManagementItemsComponent implements OnInit {

  isLoading: boolean;
  selectedProducts: Product;
  display: boolean = false;
  isEdit: boolean;
  specifications: string[] = [];
  advantages: string[] = [];
  cautions: string[] = [];
  statusAccount = [
    { name: 'Actif', code: true, },
    { name: 'Inactif', code: false, },
  ];
  statusAccountEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ];
  filterForm = { enable: true, labelCateg: '', sku: '', name: "", created_at: "", }
  users: User[];
  products: Items[] = [];
  categories: Category[] = [];

  showSideBar: boolean;
  offset = 0;
  limit = 50;
  total = 0;
  isCategory: boolean;
  isImage: boolean;
  selectedUnit: Unit = new Unit();
  isDetail: boolean = true;
  selectedProduct: Items = new Items();
  selectedCategory: Category = new Category();
  deleterPoductModal: boolean;
  title: string;
  selectedAdvantages: string[];
  selectedCaution: string[];
  selectedSpecification: string[];
  index: number = 0;
  language = this.baseHref?.replace(/\//g, '');
  itemsAction = ItemsAction;
  categoryAction = CategoryAction;
  isLoadingInModal: boolean;
  indexTab = 0;
  productNames: any;
  productSku: any;
  categoriesLabel: any;
  dateEnd: Date;
  today: string = moment().format('YYYY-MM-DD');
  dateStart: Date;
  isExpired: boolean = false;
  timeRemaining: string;
  intervalId: ReturnType<typeof setInterval> | null = null;
  private SECONDS_IN_MINUTE = 60;
  private SECONDS_IN_HOUR = 60 * this.SECONDS_IN_MINUTE; // 3600
  private SECONDS_IN_DAY = 24 * this.SECONDS_IN_HOUR;    // 86400
  private ONE_SECOND_IN_MS = 1000;
  advantagesList: any[] = [];
  selectedAdvantage: number | null = null;
  // Nouvelles propriétés pour gérer les changements de niveau de fidélité
  previousAdvantageId: string | null = null;
  pendingFidelityChanges: { itemId: string, newAdvantageId: number | null, previousAdvantageId: string | null } | null = null;

  constructor(
    private router: Router,
    public commonSrv: CommonService,
    public marketPlaceService: MarketPlaceService,
    public categorySrv: CategoryService,
    private messageService: MessageService,
    private imageCompress: NgxImageCompressService,
    private confirmationService: ConfirmationService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    this.statusAccount = (this.language === 'en') ? this.statusAccountEn : this.statusAccount;
    await this.handleTabview(this.indexTab);
    await this.getElementsForFilters();
    await this.loadAdvantages();
  }

  async loadAdvantages() {
    this.isLoading = true;
    const res = await this.marketPlaceService.getAdvantages();
    if (res && Array.isArray(res.data)) {
      this.advantagesList = res.data;
      this.messageService.add({ severity: 'success', summary: 'Succès', detail: 'Les avantages ont été bien récupérés.' });
    } else {
      this.advantagesList = [];
      this.messageService.add({ severity: 'error', summary: 'Erreur', detail: 'Impossible de charger les avantages' });
    }
    this.isLoading = false;
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  async handleTabview(index: number): Promise<void> {
    this.isLoading = true;
    switch (index) {
      case 0:
        this.title = `${await t('PRODUCTS')}`;
        await this.getProduct();
        break;
      case 1:
        this.title = `categorie`;
        await this.getCategory();
        break;
      default:
        this.title = `${await t('PRODUCTS')}`;
        await this.getProduct();
        break;
    }
    delete this.filterForm.name;
    delete this.filterForm.labelCateg;
    delete this.filterForm.sku;
    delete this.filterForm.created_at;
    this.isLoading = false;
  }

  async getElementsForFilters() {
    let keyForFilters = ['name', 'sku'];
    const productsFiltersOptions = await this.commonSrv.getElementForFilterByKeys('items', { keyForFilters });
    this.productNames = productsFiltersOptions?.dataname;
    this.productSku = productsFiltersOptions?.datasku;

    keyForFilters = ['label'];
    const categoriesFilterOptions = await this.commonSrv.getElementForFilterByKeys('category', { keyForFilters });
    this.categoriesLabel = categoriesFilterOptions?.datalabel;
  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getProduct();
  }

  async reset() {
    this.selectedProduct = new Items();
    this.offset = 0;
    this.filterForm = { labelCateg: '', name: '', enable: true, sku: '', created_at: "" };
    await this.handleTabview(this.indexTab);
  }

  async getProduct() {
    this.isLoading = true;
    const query = {
      offset: this.offset,
      limit: this.limit,
      ...this.filterForm,
      label: this.filterForm.name
    };
    const res = await this.marketPlaceService.getItems(query);
    if (res instanceof HttpErrorResponse) {
      return this.messageService.add({ severity: 'error', summary: `${await t('ACTION_FAILED')}`, detail: res?.error?.message });
    }
    this.total = res.count;
    this.products = res.data;
    this.isLoading = false;
    this.startCountdown(this.products[0]?.timeRemaining)
  }

  async getCategory() {
    this.isLoading = true;
    const query = {
      offset: this.offset,
      limit: this.limit,
      ...this.filterForm,
      label: this.filterForm.labelCateg
    };
    const res = await this.categorySrv.getCategory(query);
    if (res instanceof HttpErrorResponse) {
      return this.messageService.add({ severity: 'error', summary: `${await t('ACTION_FAILED')}`, detail: res?.error?.message });
    }
    this.total = res.count;
    this.categories = res.data;
    this.isLoading = false;
  }


  openDeleteproductModal(position: string) {
    this.deleterPoductModal = true;
    this.display = false;
  }

  async changeStateOfProduct(product: Items) {
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')} ${product.enable ? await t('disable') : await t('enable')}  ${await t('PRODUCT')} ?`,
      header: `${product.enable ? await t('Disable') : await t('Enable')} ${await t('OF_PRODUCT')} ${product.name.split(' ')[0]}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        this.isLoading = true;
        await this.marketPlaceService.delete(product);
        await this.getProduct();
        return this.messageService.add({ severity: 'success', summary: `${product.enable ? await t('Disable') : await t('Enable')} ${await t('Done')}` });
      },


    });
    this.display = false;
    this.isLoading = false;
  }

  async changeStateOfCategory(category: Category) {
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')} ${category.enable ? await t('disable') : await t('enable')} ${await t('CATEGORY')}?`,
      header: `${category.enable ? await t('Disable') : await t('Enable')} ${await t('OF_CATEGORY')} ${category?.label.split(' ')[0]}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        this.isLoading = true;
        await this.categorySrv.deleteCategory(category);
        await this.getCategory();
        return this.messageService.add({ severity: 'success', summary: `${category.enable ? await t('Disable') : await t('Enable')} ${await t('Done')}` });
      },

    });
    this.isCategory = false;
    this.isLoading = false;
  }

  goTo(): void {
    this.router.navigate(['path']);
  }

  openAddProductModal() {
    this.getCategory();
    this.selectedProduct = new Items();
    this.selectedAdvantage = null;
    this.previousAdvantageId = null;
    this.pendingFidelityChanges = null;
    this.display = true;
    this.isDetail = false;
    this.isEdit = true;
    this.isImage = false;
    this.isCategory = false;
  }

  async showUpdateProduct(product: Items) {
    // S'assurer que les avantages sont chargés
    if (!this.advantagesList || this.advantagesList.length === 0) {
      await this.loadAdvantages();
    }
    
    this.getCategory();
    this.selectedProduct = { ...product };
    this.selectedProduct.category.code = this.selectedProduct.category.code || '';
    
    // Récupérer l'ID de l'avantage actuel du produit
    const currentAdvantageId = this.getCurrentAdvantageId(product._id);
    
    // Pré-remplir le niveau de fidélité si déjà assigné
    if (currentAdvantageId) {
      // Trouver l'avantage dans la liste et récupérer son statusValue
      const advantage = this.advantagesList.find(adv => adv._id === currentAdvantageId);
      this.selectedAdvantage = advantage ? advantage.statusValue : null;
    } else {
      this.selectedAdvantage = null;
    }
    
    // Stocker l'ID de l'avantage précédent pour les comparaisons
    this.previousAdvantageId = currentAdvantageId;
    // Réinitialiser les changements en attente
    this.pendingFidelityChanges = null;
    this.isEdit = true;
    this.display = true;
    this.isImage = true;
    this.isCategory = false;
  }

  async addProduct(): Promise<boolean> {
    this.isLoadingInModal = true;
    if (!this.selectedProduct.image) {
      this.messageService.add({ severity: 'error', summary: `${await t('DATA_ERROR')}`, detail: `${await t('IMG_IMPORT')}` });
      return this.isLoadingInModal = false;
    }

    const category = this.categories.find(category => category.code === this.selectedProduct.category.code);
    delete category.created_at;
    delete category.description;
    delete category.enable;

    this.selectedProduct = {
      ...this.selectedProduct,
      name: this.selectedProduct.name,
      sku: this.selectedProduct.sku,
      // cautions: this.cautions,
      // advantages: this.advantages,
      category: { ...category }
    };

    const validationFields = this.commonSrv.verifyFieldsForm(this.selectedProduct, ['openingTime', 'closingTime', 'isActive']);
    if (validationFields) {
      this.isLoading = false;
      this.messageService.add({ severity: 'error', summary: `${await t('DATA_ERROR')}`, detail: validationFields as string });
      return this.isLoadingInModal = false;
    }

    // Pour la création, pas besoin de vérifier si l'item est déjà assigné car il n'existe pas encore

    const res = await this.marketPlaceService.createProduct(this.selectedProduct);
    console.log(res);

    if (res.status == HttpStatusCode.Created) {
      // Si un niveau de fidélité est sélectionné, l'assigner au produit créé
      if (this.selectedAdvantage && res.data?._id) {
        try {
          await this.marketPlaceService.assignItemToFidelityStatus(res.data._id, this.selectedAdvantage);
          this.messageService.add({
            severity: 'success',
            summary: 'Succès',
            detail: 'Le niveau de fidélité a été assigné au produit.'
          });
        } catch (error: any) {
          const errorMsg = error?.error?.message || error?.message || 'Erreur lors de l\'assignation du niveau de fidélité';
          this.messageService.add({
            severity: 'error',
            summary: 'Erreur',
            detail: errorMsg
          });
        }
      }

      this.messageService.add({ severity: 'success', summary: 'Produit créé avec succès', detail: res.message });
      this.display = false;
      await this.getProduct(); // Recharger pour voir les changements
      this.selectedProduct = new Items();
    } else {
      this.messageService.add({ severity: 'error', summary: res.data, detail: '' + res.message });
    }
    return this.isLoadingInModal = false;
  }

  insert(specification, advantage, caution) {

    if (this.index === 0) {
      this.specifications.push(specification);
      this.selectedSpecification = [''];
    }
    if (this.index === 1) {
      this.advantages.push(advantage);
      this.selectedAdvantages = [''];
    }
    if (this.index === 2) {
      this.cautions.push(caution);
      this.selectedCaution = [''];
    }
  }

  async handleChangeTabProduct(event: any): Promise<void> {
    this.index = event.index;
  }

  async updateProduct(): Promise<boolean> {
    this.isLoadingInModal = true;

    const category = this.categories.find(category => category.code === this.selectedProduct.category.code);
    delete category.created_at;
    delete category['create_at'];
    delete category?.description;
    delete category?.enable;

    const productId: string = this.selectedProduct?._id;

    // Vérification côté front : l'item ne doit pas déjà être dans un autre advantage
    if (this.selectedAdvantage && this.advantagesList?.length) {
      const alreadyAssigned = this.advantagesList.some(
        adv =>
          adv._id !== this.selectedAdvantage.toString() &&
          Array.isArray(adv.rewardItems) &&
          adv.rewardItems.includes(productId)
      );
      if (alreadyAssigned) {
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Ce produit est déjà attribué à un autre avantage (niveau de fidélité).',
        });
        this.isLoadingInModal = false;
        return false;
      }
    }

    delete this.selectedProduct?.created_at;
    delete this.selectedProduct?._id;
    delete this.selectedProduct?.enable;
    delete this.selectedProduct?.timeRemaining;
    this.selectedProduct = { ...this.selectedProduct, category: { ...category } };
    
    // Appliquer les changements de fidélité directement dans le payload si nécessaire
    if (this.pendingFidelityChanges && this.pendingFidelityChanges.itemId === productId) {
      if (!this.pendingFidelityChanges.newAdvantageId) {
        // Retirer le niveau de fidélité : mettre statusValue à 0
        (this.selectedProduct as any).statusValue = 0;
      } else {
        // Assigner un nouveau niveau de fidélité - newAdvantageId est déjà un nombre
        (this.selectedProduct as any).statusValue = this.pendingFidelityChanges.newAdvantageId;
      }
    }
    
    const dataProductSelected = { ...this.selectedProduct };

    const validationFeilds = this.commonSrv.verifyFieldsForm(dataProductSelected, ['openingTime', 'closingTime', 'isActive']);
    if (validationFeilds) {
      this.messageService.add({ severity: 'error', summary: `${await t('DATA_ERROR')}`, detail: validationFeilds as string });
      return this.isLoadingInModal = false;
    }

    delete this.selectedProduct?.created_at;

    const res = await this.marketPlaceService.updateProduct(productId, this.selectedProduct);
    if (res.status == HttpStatusCode.Ok) {
      // Appliquer les changements d'avantages si nécessaire (pour les assignations/désassignations)
      const fidelitySuccess = await this.applyFidelityChanges();
      if (!fidelitySuccess) {
        this.isLoadingInModal = false;
        return false;
      }

      this.messageService.add({ severity: 'success', summary: `${await t('CREATE_PRODUCT')}`, detail: res.message });
      this.display = false;
      this.selectedProduct = new Items();
    } else {
      this.messageService.add({ severity: 'error', summary: res.data, detail: res.message });
    }
    return this.isLoadingInModal = false;
  }

  compressFile(): void {
    this.imageCompress.uploadFile().then(({ image, orientation }): any => {
      this.imageCompress.compressFile(image, orientation, 50, 50).then(async (result: any) => {
        this.selectedProduct.image = result;
      });
    });
    this.isImage = true;
  }

  close() {
    this.display = false;
    this.isCategory = false;
    this.selectedProduct = new Items();
    this.selectedUnit = new Unit();
    this.selectedCategory = new Category();
    this.selectedAdvantage = null;
    this.previousAdvantageId = null;
    this.pendingFidelityChanges = null;
  }

  openAddCategoryModal() {
    this.selectedCategory = new Category();
    this.isCategory = true;
    this.isEdit = true;
    this.display = false;
  }

  openUpdateCategoryModal(category: Category) {
    this.selectedCategory = { ...category };
    this.isCategory = true;
    this.isEdit = true;
    this.display = false;
  }

  async addCategory(): Promise<boolean> {
    this.isLoadingInModal = true;

    this.selectedCategory = {
      ...this.selectedCategory,
      description: this.selectedCategory?.description || this.selectedCategory?.label
    };
    const validationFeilds = this.commonSrv.verifyAllFieldsForm({ ...this.selectedCategory });

    if (validationFeilds) {
      this.messageService.add({ severity: 'error', summary: await t('DATA_ERROR'), detail: validationFeilds as string });
      return this.isLoadingInModal = false;
    }
    const res = await this.categorySrv.createCategory(this.selectedCategory);
    if (res.status == HttpStatusCode.Ok) {
      this.messageService.add({ severity: 'success', summary: await t('CREATE_UNIT'), detail: '' + res.message });
      this.isCategory = false;
      this.selectedCategory = new Category();
      await this.getCategory();
    } else {
      this.messageService.add({ severity: 'error', summary: '' + res.data, detail: '' + res.message });
    }
    return this.isLoadingInModal = false;
  }

  async updateCategory(category: Category): Promise<boolean> {
    this.isLoadingInModal = true;
    category = {
      ...category,
      description: category?.description || category?.label
    }
    const validationFeilds = this.commonSrv.verifyAllFieldsForm(category);

    if (validationFeilds) {
      this.isLoading = false;
      this.messageService.add({ severity: 'error', summary: await t('DATA_ERROR'), detail: validationFeilds as string });
      return this.isLoadingInModal = false;
    }
    const res = await this.categorySrv.updateCategory(category);

    if (res.status == HttpStatusCode.Ok) {
      this.messageService.add({ severity: 'success', summary: await t('CREATE_UNIT'), detail: '' + res.message, });
      this.selectedCategory = new Category();
      this.isCategory = false;
      await this.getCategory();
    } else {
      this.messageService.add({ severity: 'error', summary: '' + res.data, detail: '' + res.message });
    }
    return this.isLoadingInModal = false;
  }

  async openMarketPlace() {
    this.confirmationService.confirm({
      message: await t('CONFIRM_CLOSE_MARKETPLACE'),
      header: await t('CLOSE_MARKETPLACE'),
      icon: 'pi pi-check-circle',
      acceptLabel: await t('VALIDATE'),
      accept: async () => {
        this.isLoading = true;
        try {
          const marketplaceStatus: MarketplaceStatus = {
            openingTime: null,
            closingTime: null,
            isActive: false,
          };

          const res = await this.marketPlaceService.updateMarketplaceStatus(marketplaceStatus);

          if (res instanceof HttpErrorResponse) {
            this.handleError(res, 'Une erreur est survenue lors de la fermeture');
          } else {
            this.messageService.add({
              severity: 'success',
              summary: await t('CLOSE_SUCCESS'),
              detail: await t('MARKETPLACE_CLOSED'),
            });
            await this.getProduct();
            this.timeRemaining = null;
          }
        } catch (error) {
          this.handleError(error, 'Une erreur est survenue lors de la fermeture de place');
        } finally {
          this.isLoading = false;
        }
      },
    });
  }

  async updateMarketPlaceHours() {
    this.isLoadingInModal = true;

    try {
      if (!(await this.validateDates())) {
        return;
      }

      const marketplaceStatus: MarketplaceStatus = {
        openingTime: new Date(this.dateStart).getTime(),
        closingTime: new Date(this.dateEnd).getTime(),
        isActive: true,
      };

      const res = await this.marketPlaceService.updateMarketplaceStatus(marketplaceStatus);

      if (res instanceof HttpErrorResponse) {
        this.handleError(res, 'Une erreur est survenue lors de la mise à jour');
      } else {
        this.messageService.add({
          severity: 'success',
          summary: await t('UPDATE_DATE_EXPIRED'),
          detail: await t('UPDATE_DATE_EXPIRED_SUCCESS'),
        });
        await this.getProduct();
      }
    } catch (error) {
      this.handleError(error, 'Une erreur est survenue');
    } finally {
      this.resetModalState();
    }
  }

  async validateDates(): Promise<boolean> {
    if (!this.dateStart || !this.dateEnd) {
      this.showDateError(await t('DATE_REQUIRED'));
      return false;
    }
    if (this.dateStart > this.dateEnd) {
      this.showDateError(await t('DATE_START_END'));
      return false;
    }
    return true;
  }

  private async showDateError(detail: string) {
    this.messageService.add({
      severity: 'error',
      summary: await t('DATE_CHECK'),
      detail,
    });
  }

  async startCountdown(timeString: string): Promise<void> {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    if (!timeString) {
      return;
    }

    const [days, hours, minutes, seconds] = timeString.match(/\d+/g)?.map(Number) || [0, 0, 0, 0];
    let totalTimeInSeconds =
      days * this.SECONDS_IN_DAY +
      hours * this.SECONDS_IN_HOUR +
      minutes * this.SECONDS_IN_MINUTE +
      seconds;

    if (totalTimeInSeconds === 0) {
      return;
    }

    this.updateTimeRemaining(totalTimeInSeconds);

    this.intervalId = setInterval(async () => {
      if (totalTimeInSeconds <= 1) {
        clearInterval(this.intervalId);
        await this.closeMarketplaceOnCountdownEnd();
      } else {
        totalTimeInSeconds--;
        this.updateTimeRemaining(totalTimeInSeconds);
      }
    }, this.ONE_SECOND_IN_MS);
  }


  private async closeMarketplaceOnCountdownEnd() {
    const marketplaceStatus: MarketplaceStatus = {
      openingTime: null,
      closingTime: null,
      isActive: false,
    };

    const res = await this.marketPlaceService.updateMarketplaceStatus(marketplaceStatus);

    if (res instanceof HttpErrorResponse) {
      this.handleError(res, 'Une erreur est survenue lors de la fermeture');
    } else {
      this.messageService.add({
        severity: 'warn',
        summary: 'Fermeture de la marketplace',
        detail: 'La marketplace est temporairement fermée. Veuillez réessayer plus tard.',
      });
      await this.getProduct();
      this.timeRemaining = null;
    }
  }

  updateTimeRemaining(totalTimeInSeconds: number) {
    const days = Math.floor(totalTimeInSeconds / this.SECONDS_IN_DAY);
    const hours = Math.floor((totalTimeInSeconds % this.SECONDS_IN_DAY) / this.SECONDS_IN_HOUR);
    const minutes = Math.floor((totalTimeInSeconds % this.SECONDS_IN_HOUR) / this.SECONDS_IN_MINUTE);
    const seconds = totalTimeInSeconds % this.SECONDS_IN_MINUTE;
    this.timeRemaining = `${days}j ${hours}h ${minutes}m ${seconds}s`;
  }

  private async handleError(error: any, defaultMessage: string) {
    const message = error instanceof HttpErrorResponse ? error.error?.message : defaultMessage;
    this.messageService.add({
      severity: 'error',
      summary: await t('ERROR'),
      detail: message,
    });
  }

  private resetModalState() {
    this.selectedProduct = new Items();
    this.isLoadingInModal = false;
    this.isLoading = false;
    this.isExpired = false;
  }

  async onFidelityStatusChange(itemId: string, previousAdvantageId: string | null) {
    // Stocker les changements au lieu de les appliquer immédiatement
    this.pendingFidelityChanges = {
      itemId: itemId || null, // itemId peut être null lors de la création
      newAdvantageId: this.selectedAdvantage && this.selectedAdvantage !== 0 ? this.selectedAdvantage : null,
      previousAdvantageId: previousAdvantageId
    };
  }

  getCurrentAdvantageId(itemId: string): string | null {
    if (!this.advantagesList) return null;
    const found = this.advantagesList.find(
      adv => Array.isArray(adv.rewardItems) && adv.rewardItems.includes(itemId)
    );
    return found ? found._id : null;
  }

  getAdvantageLabelById(advId: string): string {
    const found = this.advantagesList?.find(a => a._id === advId);
    return found?.label || 'N/A';
  }

  getFidelityClass(advId: string): string {
    if (!advId) return '';
    
    const advantage = this.advantagesList?.find(a => a._id === advId);
    if (!advantage) return '';
    
    const label = advantage.label?.toLowerCase() || '';
    
    if (label.includes('elite')) return 'elite';
    if (label.includes('premium')) return 'premium';
    if (label.includes('standard') || label.includes('basique')) return 'standard';
    
    return '';
  }

  async applyFidelityChanges(): Promise<boolean> {
    if (!this.pendingFidelityChanges) {
      return true; // Aucun changement en attente
    }

    const { itemId, newAdvantageId, previousAdvantageId } = this.pendingFidelityChanges;
    
    // Si pas d'itemId (cas de création), on ne peut pas appliquer les changements
    if (!itemId) {
      return true;
    }

    try {
      // Si on retire le niveau de fidélité (newAdvantageId est null)
      if (!newAdvantageId) {
        // Le statusValue a déjà été mis à null dans updateProduct, 
        // on n'a plus besoin de faire de requête supplémentaire
        // Recharger les données
        await this.loadAdvantages();
        await this.getProduct();

        // Réinitialiser les changements en attente
        this.pendingFidelityChanges = null;

        this.messageService.add({
          severity: 'success',
          summary: 'Succès',
          detail: 'Le niveau de fidélité a été retiré du produit.'
        });
        return true;
      } else {
        // Si on change d'avantage, d'abord retirer l'ancien s'il existe
        if (previousAdvantageId && previousAdvantageId !== newAdvantageId.toString()) {
          await this.marketPlaceService.removeRewardItemFromAdvantage(previousAdvantageId, itemId);
        }
        // Ensuite assigner le nouvel avantage
        await this.marketPlaceService.assignItemToFidelityStatus(itemId, newAdvantageId);
        
        // Recharger les données
        await this.loadAdvantages();
        await this.getProduct();

        // Réinitialiser les changements en attente
        this.pendingFidelityChanges = null;

        this.messageService.add({
          severity: 'success',
          summary: 'Succès',
          detail: 'Le niveau de fidélité a été mis à jour avec succès.'
        });
        return true;
      }
    } catch (error: any) {
      const errorMsg = error?.error?.message || error?.message || 'Une erreur est survenue lors de la mise à jour du niveau de fidélité';
      this.messageService.add({
        severity: 'error',
        summary: 'Erreur',
        detail: errorMsg
      });
      return false;
    }
  }


}
