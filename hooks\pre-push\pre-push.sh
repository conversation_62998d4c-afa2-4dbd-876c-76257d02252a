# #!/bin/bash

# # Hook pre-push Git
# # Ce script s'exécute avant chaque git push pour les branches feature/, bugfix/ et hotfix/
# # - feature/bugfix: rebase automatique avec develop
# # - hotfix: validation du workflow Git Flow (double merge requis)

# set -e  # Arrêter le script en cas d'erreur

# # Configuration
# ENFORCE_HOTFIX_DOUBLE_MERGE=true  # Mettre à false pour être moins strict

# # Couleurs pour les messages
# RED='\033[0;31m'
# GREEN='\033[0;32m'
# YELLOW='\033[1;33m'
# BLUE='\033[0;34m'
# MAGENTA='\033[0;35m'
# NC='\033[0m' # No Color

# # Fonction pour afficher les messages colorés
# log_info() {
#     echo -e "${BLUE}[INFO]${NC} $1"
# }

# log_success() {
#     echo -e "${GREEN}[SUCCESS]${NC} $1"
# }

# log_warning() {
#     echo -e "${YELLOW}[WARNING]${NC} $1"
# }

# log_error() {
#     echo -e "${RED}[ERROR]${NC} $1"
# }

# log_hotfix() {
#     echo -e "${MAGENTA}[HOTFIX]${NC} $1"
# }

# # Récupérer la branche actuelle
# current_branch=$(git branch --show-current)

# log_info "Branche actuelle: $current_branch"

# # Fonction de nettoyage en cas d'erreur
# cleanup_on_error() {
#     log_error "Une erreur s'est produite. Retour à la branche d'origine..."
#     git checkout "$current_branch" 2>/dev/null || true
#     log_info "Vous pouvez restaurer vos modifications avec: git stash pop"
#     exit 1
# }

# # Piège pour nettoyer en cas d'erreur
# trap cleanup_on_error ERR

# # ============================================
# # GESTION DES BRANCHES HOTFIX
# # ============================================
# if [[ "$current_branch" =~ ^hotfix/ ]]; then
#     log_hotfix "Branche hotfix détectée. Validation du workflow Git Flow..."
    
#     # Déterminer la branche principale
#     if git show-ref --verify --quiet refs/heads/main; then
#         main_branch="main"
#     elif git show-ref --verify --quiet refs/heads/master; then
#         main_branch="master"
#     else
#         log_error "Aucune branche main ou master trouvée!"
#         exit 1
#     fi
    
#     log_hotfix "Branche principale: $main_branch"
    
#     # Fonction pour vérifier si les commits du hotfix sont dans une branche
#     check_commits_in_branch() {
#         local target_branch=$1
#         local missing_commits=0
        
#         # Obtenir tous les commits du hotfix qui ne sont pas dans main
#         local hotfix_commits=$(git rev-list $main_branch..$current_branch 2>/dev/null)
        
#         if [ -z "$hotfix_commits" ]; then
#             return 0
#         fi
        
#         # Vérifier chaque commit
#         for commit in $hotfix_commits; do
#             if ! git branch -r --contains $commit 2>/dev/null | grep -q "origin/$target_branch"; then
#                 missing_commits=$((missing_commits + 1))
#             fi
#         done
        
#         return $missing_commits
#     }
    
#     # Fonction pour proposer le merge automatique dans develop
#     suggest_auto_merge_to_develop() {
#         echo
#         log_warning "Le hotfix n'est pas encore dans develop!"
#         log_info "Voulez-vous merger automatiquement dans develop? (y/n)"
#         read -r response < /dev/tty
        
#         if [[ "$response" =~ ^[Yy]$ ]]; then
#             log_info "Merge automatique en cours..."
            
#             # Sauvegarder l'état actuel
#             git stash push -m "pre-push-auto-merge-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
            
#             # Merger dans develop
#             git checkout develop
#             git pull origin develop
#             log_info "Tentative de merge de $current_branch dans develop..."
#             if git merge $current_branch --no-ff -m "Merge $current_branch into develop (Git Flow)"; then
#                 log_success "Merge réussi dans develop!"
#                 git push origin develop
                
#                 # Retour sur le hotfix
#                 git checkout $current_branch
                
#                 # Restaurer le stash si nécessaire
#                 if git stash list | grep -q "pre-push-auto-merge"; then
#                     git stash pop
#                 fi
                
#                 return 0
#             else
#                 log_error "Conflits détectés lors du merge dans develop!"
#                 echo
#                 log_warning "Résolvez les conflits manuellement:"
#                 log_warning "1. Résolvez les conflits dans les fichiers"
#                 log_warning "2. git add <fichiers résolus>"
#                 log_warning "3. git commit"
#                 log_warning "4. git push origin develop"
#                 log_warning "5. git checkout $current_branch"
#                 return 1
#             fi
#         fi
        
#         return 1
#     }
    
#     # Sauvegarder l'état actuel
#     log_info "Sauvegarde de l'état actuel..."
#     git stash push -m "pre-push-hotfix-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
    
#     # Vérifier que le hotfix est basé sur la dernière version de main
#     log_hotfix "Étape 1/3: Vérification que le hotfix est à jour avec $main_branch..."
#     git fetch origin $main_branch
    
#     if ! git merge-base --is-ancestor origin/$main_branch HEAD; then
#         log_warning "Le hotfix n'est pas basé sur la dernière version de $main_branch"
#         log_info "Mise à jour en cours..."
        
#         if git rebase origin/$main_branch; then
#             log_success "Hotfix mis à jour avec succès"
#         else
#             log_error "Conflits lors de la mise à jour avec $main_branch"
#             exit 1
#         fi
#     else
#         log_success "✓ Le hotfix est basé sur la dernière version de $main_branch"
#     fi
    
#     # Vérifier si develop existe et faire les vérifications
#     if git show-ref --verify --quiet refs/heads/develop; then
#         log_hotfix "Étape 2/3: Vérification du merge dans develop..."
        
#         # Fetch develop pour avoir les dernières infos
#         git fetch origin develop
        
#         # Vérifier si les commits sont dans develop
#         if check_commits_in_branch "develop"; then
#             log_success "✓ Tous les commits du hotfix sont déjà dans develop"
#         else
#             if [ "$ENFORCE_HOTFIX_DOUBLE_MERGE" = true ]; then
#                 log_error "✗ Le workflow Git Flow n'est pas respecté!"
#                 echo
#                 log_error "Les commits du hotfix DOIVENT être dans develop avant le push."
#                 log_error "Cela évite que le bug corrigé ne revienne dans la prochaine release!"
                
#                 # Proposer le merge automatique
#                 if ! suggest_auto_merge_to_develop; then
#                     echo
#                     log_error "Options disponibles:"
#                     log_info "1. Merger manuellement dans develop:"
#                     log_info "   git checkout develop"
#                     log_info "   git pull origin develop"
#                     log_info "   git merge --no-ff $current_branch"
#                     log_info "   git push origin develop"
#                     log_info "   git checkout $current_branch"
#                     echo
#                     log_info "2. Forcer le push (DANGEREUX - le bug reviendra!):"
#                     log_info "   git push --no-verify"
#                     echo
#                     exit 1
#                 fi
#             else
#                 log_warning "⚠ ATTENTION: Les commits du hotfix ne sont pas dans develop"
#                 log_warning "Le bug risque de revenir dans la prochaine release!"
#                 log_warning "Pensez à merger dans develop après ce push!"
#             fi
#         fi
        
#         # Test de compatibilité avec develop
#         log_hotfix "Étape 3/3: Test de compatibilité future avec develop..."
#         temp_branch="temp-hotfix-check-$(date +%s)"
#         git checkout -b "$temp_branch" >/dev/null 2>&1
        
#         if git merge origin/develop --no-commit --no-ff >/dev/null 2>&1; then
#             log_success "✓ Aucun conflit prévu avec develop"
#             git merge --abort >/dev/null 2>&1
#         else
#             log_warning "⚠ Des conflits sont attendus lors du merge dans develop"
#             git merge --abort >/dev/null 2>&1
#         fi
        
#         git checkout "$current_branch" >/dev/null 2>&1
#         git branch -D "$temp_branch" >/dev/null 2>&1
#     fi
    
#     # Restaurer les modifications stashées
#     if git stash list | grep -q "pre-push-hotfix-backup"; then
#         log_info "Restauration des modifications sauvegardées..."
#         git stash pop
#     fi
    
#     # Rappel du workflow
#     echo
#     log_hotfix "=== RAPPEL DU WORKFLOW HOTFIX ==="
#     log_hotfix "Après ce push:"
#     log_hotfix "1. Créez/Mergez la PR vers $main_branch"
#     log_hotfix "2. Taggez la nouvelle version après merge"
#     log_hotfix "3. Si pas encore fait: mergez dans develop"
#     log_hotfix "4. Supprimez la branche hotfix"
    
#     log_success "Validation hotfix terminée. Push autorisé!"
#     exit 0
# fi

# # ============================================
# # GESTION DES BRANCHES FEATURE/BUGFIX (code original)
# # ============================================
# if [[ ! "$current_branch" =~ ^(feature|bugfix)/ ]]; then
#     log_info "Cette branche ($current_branch) n'est pas une branche feature/, bugfix/ ou hotfix/"
#     log_info "Le hook pre-push ne s'applique pas. Push autorisé."
#     exit 0
# fi

# log_info "Branche détectée comme feature/ ou bugfix/. Démarrage du processus de rebase..."

# # Vérifier que la branche develop existe
# if ! git show-ref --verify --quiet refs/heads/develop; then
#     log_error "La branche 'develop' n'existe pas localement."
#     log_error "Veuillez créer ou récupérer la branche develop avant de continuer."
#     exit 1
# fi

# # Sauvegarder l'état actuel au cas où quelque chose se passe mal
# log_info "Sauvegarde de l'état actuel..."
# git stash push -m "pre-push-hook-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true

# # Étape 1: Passer sur la branche develop
# log_info "Étape 1/4: Passage sur la branche develop..."
# git checkout develop

# # Étape 2: Récupérer les dernières modifications de develop
# log_info "Étape 2/4: Récupération des dernières modifications de develop..."
# if ! git pull origin develop; then
#     log_error "Impossible de récupérer les modifications de develop depuis origin"
#     git checkout "$current_branch"
#     exit 1
# fi

# # Étape 3: Retourner sur la branche d'origine
# log_info "Étape 3/4: Retour sur la branche $current_branch..."
# git checkout "$current_branch"

# # Étape 4: Rebase de develop sur la branche courante
# log_info "Étape 4/4: Rebase de develop sur $current_branch..."

# # Effectuer le rebase
# if git rebase develop; then
#     log_success "Rebase terminé avec succès!"
    
#     # Restaurer les modifications stashées si elles existent
#     if git stash list | grep -q "pre-push-hook-backup"; then
#         log_info "Restauration des modifications sauvegardées..."
#         git stash pop
#     fi
    
#     log_success "La branche $current_branch est maintenant à jour avec develop."
#     log_success "Push autorisé!"
#     exit 0
# else
#     log_error "CONFLIT DÉTECTÉ lors du rebase!"
#     log_error "Le push a été interrompu."
#     echo
#     log_warning "Pour résoudre les conflits:"
#     log_warning "1. Résolvez les conflits dans les fichiers marqués"
#     log_warning "2. Ajoutez les fichiers résolus: git add <fichier>"
#     log_warning "3. Continuez le rebase: git rebase --continue"
#     log_warning "4. Ou annulez le rebase: git rebase --abort"
#     echo
#     log_warning "Une fois les conflits résolus, vous pourrez refaire le push."
    
#     # Ne pas restaurer le stash en cas de conflit pour éviter d'autres conflits
#     exit 1
# fi
#!/bin/bash

# Hook pre-push Git
# Ce script s'exécute avant chaque git push pour les branches feature/, bugfix/, hotfix/ et release/
# - feature/bugfix: rebase automatique avec develop
# - hotfix: validation du workflow Git Flow (double merge requis)
# - release: validation complète du workflow release

# set -e  # Arrêter le script en cas d'erreur

# # Configuration
# ENFORCE_HOTFIX_DOUBLE_MERGE=true  # Mettre à false pour être moins strict
# ENFORCE_RELEASE_RULES=true         # Mettre à false pour être moins strict sur les releases
# CHECK_VERSION_FILES=true           # Vérifier la mise à jour des fichiers de version
# ALLOW_FEATURES_IN_RELEASE=false    # Interdire les nouvelles features dans les releases

# # Couleurs pour les messages
# RED='\033[0;31m'
# GREEN='\033[0;32m'
# YELLOW='\033[1;33m'
# BLUE='\033[0;34m'
# MAGENTA='\033[0;35m'
# CYAN='\033[0;36m'
# NC='\033[0m' # No Color

# # Fonction pour afficher les messages colorés
# log_info() {
#     echo -e "${BLUE}[INFO]${NC} $1"
# }

# log_success() {
#     echo -e "${GREEN}[SUCCESS]${NC} $1"
# }

# log_warning() {
#     echo -e "${YELLOW}[WARNING]${NC} $1"
# }

# log_error() {
#     echo -e "${RED}[ERROR]${NC} $1"
# }

# log_hotfix() {
#     echo -e "${MAGENTA}[HOTFIX]${NC} $1"
# }

# log_release() {
#     echo -e "${CYAN}[RELEASE]${NC} $1"
# }

# # Récupérer la branche actuelle
# current_branch=$(git branch --show-current)

# log_info "Branche actuelle: $current_branch"

# # Fonction de nettoyage en cas d'erreur
# cleanup_on_error() {
#     log_error "Une erreur s'est produite. Retour à la branche d'origine..."
#     git checkout "$current_branch" 2>/dev/null || true
#     log_info "Vous pouvez restaurer vos modifications avec: git stash pop"
#     exit 1
# }

# # Piège pour nettoyer en cas d'erreur
# trap cleanup_on_error ERR

# # ============================================
# # GESTION DES BRANCHES RELEASE
# # ============================================
# if [[ "$current_branch" =~ ^release/ ]]; then
#     log_release "Branche release détectée. Validation du workflow..."
    
#     # Extraire la version du nom de branche
#     version=$(echo "$current_branch" | sed 's/release\///')
#     log_release "Version cible : $version"
    
#     # Vérifier que develop existe
#     if ! git show-ref --verify --quiet refs/heads/develop; then
#         log_error "La branche develop n'existe pas localement!"
#         exit 1
#     fi
    
#     # Déterminer la branche principale
#     if git show-ref --verify --quiet refs/heads/main; then
#         main_branch="main"
#     elif git show-ref --verify --quiet refs/heads/master; then
#         main_branch="master"
#     else
#         log_error "Aucune branche main ou master trouvée!"
#         exit 1
#     fi
    
#     # Sauvegarder l'état actuel
#     log_info "Sauvegarde de l'état actuel..."
#     git stash push -m "pre-push-release-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
    
#     # Étape 1: Vérifier que la release contient tout develop
#     log_release "Étape 1/5: Vérification que la release contient tous les commits de develop..."
#     git fetch origin develop --quiet
    
#     commits_behind=$(git rev-list --count HEAD..origin/develop)
#     commits_ahead=$(git rev-list --count origin/develop..HEAD)
    
#     if [ $commits_behind -gt 0 ]; then
#         log_error "✗ La release est en retard de $commits_behind commits sur develop!"
#         log_warning "Une release DOIT contenir TOUS les commits de develop"
        
#         # Montrer quelques commits manquants
#         log_info "Commits manquants (5 premiers):"
#         git log --oneline HEAD..origin/develop | head -5
        
#         if [ "$ENFORCE_RELEASE_RULES" = true ]; then
#             echo
#             log_error "Vous devez d'abord mettre à jour avec develop:"
#             log_info "git merge origin/develop"
#             log_info "git push origin $current_branch"
            
#             # Restaurer le stash
#             if git stash list | grep -q "pre-push-release-backup"; then
#                 git stash pop
#             fi
#             exit 1
#         fi
#     else
#         log_success "✓ La release contient tous les commits de develop"
#         if [ $commits_ahead -gt 0 ]; then
#             log_info "  $commits_ahead commits spécifiques à la release"
#         fi
#     fi
    
#     # Étape 2: Vérifier le contenu des commits de la release
#     log_release "Étape 2/5: Analyse des commits de la release..."
    
#     # Obtenir les commits propres à la release
#     release_commits=$(git rev-list origin/develop..HEAD 2>/dev/null || echo "")
    
#     if [ -n "$release_commits" ]; then
#         feature_count=0
#         fix_count=0
#         chore_count=0
#         docs_count=0
#         other_count=0
        
#         echo
#         for commit in $release_commits; do
#             msg=$(git log -1 --pretty=format:"%s" $commit)
#             hash=$(git log -1 --pretty=format:"%h" $commit)
            
#             # Classifier le commit
#             if [[ "$msg" =~ ^(feat|feature): ]]; then
#                 ((feature_count++))
#                 if [ "$ALLOW_FEATURES_IN_RELEASE" = false ]; then
#                     log_error "  $hash ❌ NOUVELLE FEATURE: $msg"
#                 else
#                     log_warning "  $hash ⚠️  Feature: $msg"
#                 fi
#             elif [[ "$msg" =~ ^fix: ]]; then
#                 ((fix_count++))
#                 log_success "  $hash ✓ Fix: $msg"
#             elif [[ "$msg" =~ ^(chore|version): ]]; then
#                 ((chore_count++))
#                 log_success "  $hash ✓ Chore: $msg"
#             elif [[ "$msg" =~ ^docs: ]]; then
#                 ((docs_count++))
#                 log_success "  $hash ✓ Docs: $msg"
#             else
#                 ((other_count++))
#                 log_info "  $hash - $msg"
#             fi
#         done
        
#         echo
#         log_info "Résumé des commits de la release:"
#         [ $fix_count -gt 0 ] && log_info "  • Corrections: $fix_count"
#         [ $chore_count -gt 0 ] && log_info "  • Maintenance: $chore_count"
#         [ $docs_count -gt 0 ] && log_info "  • Documentation: $docs_count"
#         [ $feature_count -gt 0 ] && log_warning "  • Features: $feature_count (non recommandé)"
#         [ $other_count -gt 0 ] && log_info "  • Autres: $other_count"
        
#         if [ "$ALLOW_FEATURES_IN_RELEASE" = false ] && [ $feature_count -gt 0 ]; then
#             echo
#             log_error "Les branches release ne doivent contenir QUE:"
#             log_info "  • Corrections de bugs (fix:)"
#             log_info "  • Mises à jour de version (chore: ou version:)"
#             log_info "  • Documentation (docs:)"
            
#             if [ "$ENFORCE_RELEASE_RULES" = true ]; then
#                 # Restaurer le stash
#                 if git stash list | grep -q "pre-push-release-backup"; then
#                     git stash pop
#                 fi
#                 exit 1
#             fi
#         fi
#     fi
    
#     # Étape 3: Vérifier les fichiers de version
#     if [ "$CHECK_VERSION_FILES" = true ]; then
#         log_release "Étape 3/5: Vérification des fichiers de version..."
        
#         # Liste des fichiers de version courants
#         version_files=("package.json" "package-lock.json" "pom.xml" "build.gradle" "setup.py" "Cargo.toml" "version.txt" "VERSION" "pubspec.yaml")
#         version_updated=false
        
#         echo
#         for file in "${version_files[@]}"; do
#             if [ -f "$file" ]; then
#                 # Vérifier si modifié dans cette release
#                 if git diff origin/develop..HEAD --name-only | grep -q "^$file$"; then
#                     log_success "  ✓ $file mis à jour"
#                     version_updated=true
#                 else
#                     log_warning "  ⚠ $file existe mais non modifié"
#                 fi
#             fi
#         done
        
#         if [ "$version_updated" = false ]; then
#             log_warning "⚠ Aucun fichier de version n'a été mis à jour!"
#             log_warning "Assurez-vous de mettre à jour le numéro de version"
#         fi
#     fi
    
#     # Étape 4: Vérifier le CHANGELOG
#     log_release "Étape 4/5: Vérification du CHANGELOG..."
    
#     changelog_files=("CHANGELOG.md" "CHANGELOG" "HISTORY.md" "NEWS.md")
#     changelog_found=false
#     changelog_updated=false
    
#     for file in "${changelog_files[@]}"; do
#         if [ -f "$file" ]; then
#             changelog_found=true
            
#             if git diff origin/develop..HEAD --name-only | grep -q "^$file$"; then
#                 changelog_updated=true
#                 log_success "✓ $file a été mis à jour"
                
#                 # Vérifier si la version est mentionnée
#                 if git diff origin/develop..HEAD -- "$file" | grep -qi "$version"; then
#                     log_success "✓ La version $version est mentionnée dans le changelog"
#                 else
#                     log_warning "⚠ La version $version n'apparaît pas dans les modifications du changelog"
#                 fi
#             else
#                 log_warning "⚠ $file existe mais n'a pas été mis à jour"
#             fi
#             break
#         fi
#     done
    
#     if [ "$changelog_found" = false ]; then
#         log_info "Aucun fichier CHANGELOG trouvé"
#     fi
    
#     # Étape 5: Checklist finale
#     log_release "Étape 5/5: Checklist finale..."
#     echo
    
#     if [ "$ENFORCE_RELEASE_RULES" = true ]; then
#         log_info "Avant de pusher cette release, confirmez:"
#         echo "  □ Les tests unitaires passent"
#         echo "  □ Les tests d'intégration sont OK"
#         echo "  □ La documentation est à jour"
#         echo "  □ Le numéro de version est correct ($version)"
#         echo "  □ Le CHANGELOG est complet"
#         echo "  □ Les migrations DB sont prêtes (si applicable)"
#         echo
#         log_info "Tout est prêt? (y/n)"
#         read -r response < /dev/tty
        
#         if [[ ! "$response" =~ ^[Yy]$ ]]; then
#             log_warning "Push annulé. Finissez la préparation de la release."
            
#             # Restaurer le stash
#             if git stash list | grep -q "pre-push-release-backup"; then
#                 git stash pop
#             fi
#             exit 1
#         fi
#     fi
    
#     # Restaurer les modifications stashées
#     if git stash list | grep -q "pre-push-release-backup"; then
#         log_info "Restauration des modifications sauvegardées..."
#         git stash pop
#     fi
    
#     # Guide pour la suite
#     echo
#     log_release "=== WORKFLOW À SUIVRE APRÈS CE PUSH ==="
#     log_release "1. Créer une PR : $current_branch → $main_branch"
#     log_release "2. Après validation et merge dans $main_branch :"
#     log_release "   git checkout $main_branch"
#     log_release "   git pull origin $main_branch"
#     log_release "   git tag -a $version -m \"Release $version\""
#     log_release "   git push origin $version"
#     log_release "3. Merger aussi dans develop (IMPORTANT!):"
#     log_release "   git checkout develop"
#     log_release "   git merge $main_branch"
#     log_release "   git push origin develop"
#     log_release "4. Supprimer la branche release:"
#     log_release "   git branch -d $current_branch"
#     log_release "   git push origin --delete $current_branch"
#     log_release "5. Déployer en production avec le tag $version"
    
#     log_success "Validation release terminée. Push autorisé!"
#     exit 0
# fi

# # ============================================
# # GESTION DES BRANCHES HOTFIX
# # ============================================
# if [[ "$current_branch" =~ ^hotfix/ ]]; then
#     log_hotfix "Branche hotfix détectée. Validation du workflow Git Flow..."
    
#     # Déterminer la branche principale
#     if git show-ref --verify --quiet refs/heads/main; then
#         main_branch="main"
#     elif git show-ref --verify --quiet refs/heads/master; then
#         main_branch="master"
#     else
#         log_error "Aucune branche main ou master trouvée!"
#         exit 1
#     fi
    
#     log_hotfix "Branche principale: $main_branch"
    
#     # Fonction pour vérifier si les commits du hotfix sont dans une branche
#     check_commits_in_branch() {
#         local target_branch=$1
#         local missing_commits=0
        
#         # Obtenir tous les commits du hotfix qui ne sont pas dans main
#         local hotfix_commits=$(git rev-list $main_branch..$current_branch 2>/dev/null)
        
#         if [ -z "$hotfix_commits" ]; then
#             return 0
#         fi
        
#         # Vérifier chaque commit
#         for commit in $hotfix_commits; do
#             if ! git branch -r --contains $commit 2>/dev/null | grep -q "origin/$target_branch"; then
#                 missing_commits=$((missing_commits + 1))
#             fi
#         done
        
#         return $missing_commits
#     }
    
#     # Fonction pour proposer le merge automatique dans develop
#     suggest_auto_merge_to_develop() {
#         echo
#         log_warning "Le hotfix n'est pas encore dans develop!"
#         log_info "Voulez-vous merger automatiquement dans develop? (y/n)"
#         read -r response < /dev/tty
        
#         if [[ "$response" =~ ^[Yy]$ ]]; then
#             log_info "Merge automatique en cours..."
            
#             # Sauvegarder l'état actuel
#             git stash push -m "pre-push-auto-merge-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
            
#             # Merger dans develop
#             git checkout develop
#             git pull origin develop
            
#             log_info "Tentative de merge de $current_branch dans develop..."
#             if git merge $current_branch --no-ff -m "Merge $current_branch into develop (Git Flow)"; then
#                 log_success "Merge réussi dans develop!"
#                 git push origin develop
                
#                 # Retour sur le hotfix
#                 git checkout $current_branch
                
#                 # Restaurer le stash si nécessaire
#                 if git stash list | grep -q "pre-push-auto-merge"; then
#                     git stash pop
#                 fi
                
#                 return 0
#             else
#                 log_error "Conflits détectés lors du merge dans develop!"
#                 echo
#                 log_warning "Résolvez les conflits manuellement:"
#                 log_warning "1. Résolvez les conflits dans les fichiers"
#                 log_warning "2. git add <fichiers résolus>"
#                 log_warning "3. git commit"
#                 log_warning "4. git push origin develop"
#                 log_warning "5. git checkout $current_branch"
#                 return 1
#             fi
#         fi
        
#         return 1
#     }
    
#     # Sauvegarder l'état actuel
#     log_info "Sauvegarde de l'état actuel..."
#     git stash push -m "pre-push-hotfix-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
    
#     # Vérifier que le hotfix est basé sur la dernière version de main
#     log_hotfix "Étape 1/3: Vérification que le hotfix est à jour avec $main_branch..."
#     git fetch origin $main_branch
    
#     if ! git merge-base --is-ancestor origin/$main_branch HEAD; then
#         log_warning "Le hotfix n'est pas basé sur la dernière version de $main_branch"
#         log_info "Mise à jour en cours..."
        
#         if git rebase origin/$main_branch; then
#             log_success "Hotfix mis à jour avec succès"
#         else
#             log_error "Conflits lors de la mise à jour avec $main_branch"
#             exit 1
#         fi
#     else
#         log_success "✓ Le hotfix est basé sur la dernière version de $main_branch"
#     fi
    
#     # Vérifier si develop existe et faire les vérifications
#     if git show-ref --verify --quiet refs/heads/develop; then
#         log_hotfix "Étape 2/3: Vérification du merge dans develop..."
        
#         # Fetch develop pour avoir les dernières infos
#         git fetch origin develop
        
#         # Vérifier si les commits sont dans develop
#         if check_commits_in_branch "develop"; then
#             log_success "✓ Tous les commits du hotfix sont déjà dans develop"
#         else
#             if [ "$ENFORCE_HOTFIX_DOUBLE_MERGE" = true ]; then
#                 log_error "✗ Le workflow Git Flow n'est pas respecté!"
#                 echo
#                 log_error "Les commits du hotfix DOIVENT être dans develop avant le push."
#                 log_error "Cela évite que le bug corrigé ne revienne dans la prochaine release!"
                
#                 # Proposer le merge automatique
#                 if ! suggest_auto_merge_to_develop; then
#                     echo
#                     log_error "Options disponibles:"
#                     log_info "1. Merger manuellement dans develop:"
#                     log_info "   git checkout develop"
#                     log_info "   git pull origin develop"
#                     log_info "   git merge --no-ff $current_branch"
#                     log_info "   git push origin develop"
#                     log_info "   git checkout $current_branch"
#                     echo
#                     log_info "2. Forcer le push (DANGEREUX - le bug reviendra!):"
#                     log_info "   git push --no-verify"
#                     echo
#                     exit 1
#                 fi
#             else
#                 log_warning "⚠ ATTENTION: Les commits du hotfix ne sont pas dans develop"
#                 log_warning "Le bug risque de revenir dans la prochaine release!"
#                 log_warning "Pensez à merger dans develop après ce push!"
#             fi
#         fi
        
#         # Test de compatibilité avec develop
#         log_hotfix "Étape 3/3: Test de compatibilité future avec develop..."
#         temp_branch="temp-hotfix-check-$(date +%s)"
#         git checkout -b "$temp_branch" >/dev/null 2>&1
        
#         if git merge origin/develop --no-commit --no-ff >/dev/null 2>&1; then
#             log_success "✓ Aucun conflit prévu avec develop"
#             git merge --abort >/dev/null 2>&1
#         else
#             log_warning "⚠ Des conflits sont attendus lors du merge dans develop"
#             git merge --abort >/dev/null 2>&1
#         fi
        
#         git checkout "$current_branch" >/dev/null 2>&1
#         git branch -D "$temp_branch" >/dev/null 2>&1
#     fi
    
#     # Restaurer les modifications stashées
#     if git stash list | grep -q "pre-push-hotfix-backup"; then
#         log_info "Restauration des modifications sauvegardées..."
#         git stash pop
#     fi
    
#     # Rappel du workflow
#     echo
#     log_hotfix "=== RAPPEL DU WORKFLOW HOTFIX ==="
#     log_hotfix "Après ce push:"
#     log_hotfix "1. Créez/Mergez la PR vers $main_branch"
#     log_hotfix "2. Taggez la nouvelle version après merge"
#     log_hotfix "3. Si pas encore fait: mergez dans develop"
#     log_hotfix "4. Supprimez la branche hotfix"
    
#     log_success "Validation hotfix terminée. Push autorisé!"
#     exit 0
# fi

# # ============================================
# # GESTION DES BRANCHES FEATURE/BUGFIX
# # ============================================
# if [[ ! "$current_branch" =~ ^(feature|bugfix)/ ]]; then
#     log_info "Cette branche ($current_branch) n'est pas une branche feature/, bugfix/, hotfix/ ou release/"
#     log_info "Le hook pre-push ne s'applique pas. Push autorisé."
#     exit 0
# fi

# log_info "Branche détectée comme feature/ ou bugfix/. Démarrage du processus de rebase..."

# # Vérifier que la branche develop existe
# if ! git show-ref --verify --quiet refs/heads/develop; then
#     log_error "La branche 'develop' n'existe pas localement."
#     log_error "Veuillez créer ou récupérer la branche develop avant de continuer."
#     exit 1
# fi

# # Sauvegarder l'état actuel au cas où quelque chose se passe mal
# log_info "Sauvegarde de l'état actuel..."
# git stash push -m "pre-push-hook-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true

# # Étape 1: Passer sur la branche develop
# log_info "Étape 1/4: Passage sur la branche develop..."
# git checkout develop

# # Étape 2: Récupérer les dernières modifications de develop
# log_info "Étape 2/4: Récupération des dernières modifications de develop..."
# if ! git pull origin develop; then
#     log_error "Impossible de récupérer les modifications de develop depuis origin"
#     git checkout "$current_branch"
#     exit 1
# fi

# # Étape 3: Retourner sur la branche d'origine
# log_info "Étape 3/4: Retour sur la branche $current_branch..."
# git checkout "$current_branch"

# # Étape 4: Rebase de develop sur la branche courante
# log_info "Étape 4/4: Rebase de develop sur $current_branch..."

# # Effectuer le rebase
# if git rebase develop; then
#     log_success "Rebase terminé avec succès!"
    
#     # Restaurer les modifications stashées si elles existent
#     if git stash list | grep -q "pre-push-hook-backup"; then
#         log_info "Restauration des modifications sauvegardées..."
#         git stash pop
#     fi
    
#     log_success "La branche $current_branch est maintenant à jour avec develop."
#     log_success "Push autorisé!"
#     exit 0
# else
#     log_error "CONFLIT DÉTECTÉ lors du rebase!"
#     log_error "Le push a été interrompu."
#     echo
#     log_warning "Pour résoudre les conflits:"
#     log_warning "1. Résolvez les conflits dans les fichiers marqués"
#     log_warning "2. Ajoutez les fichiers résolus: git add <fichier>"
#     log_warning "3. Continuez le rebase: git rebase --continue"
#     log_warning "4. Ou annulez le rebase: git rebase --abort"
#     echo
#     log_warning "Une fois les conflits résolus, vous pourrez refaire le push."
    
#     # Ne pas restaurer le stash en cas de conflit pour éviter d'autres conflits
#     exit 1
# fi
#!/bin/bash

# Hook pre-push Git
# Ce script s'exécute avant chaque git push pour les branches feature/, bugfix/, hotfix/ et release/
# - feature/bugfix: rebase automatique avec develop
# - hotfix: validation du workflow Git Flow (double merge requis)
# - release: validation complète du workflow release

set -e  # Arrêter le script en cas d'erreur

# Configuration
ENFORCE_HOTFIX_DOUBLE_MERGE=true  # Mettre à false pour être moins strict
ENFORCE_RELEASE_RULES=true         # Mettre à false pour être moins strict sur les releases
CHECK_VERSION_FILES=true           # Vérifier la mise à jour des fichiers de version
ALLOW_FEATURES_IN_RELEASE=false    # Interdire les nouvelles features dans les releases

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_hotfix() {
    echo -e "${MAGENTA}[HOTFIX]${NC} $1"
}

log_release() {
    echo -e "${CYAN}[RELEASE]${NC} $1"
}

# Récupérer la branche actuelle
current_branch=$(git branch --show-current)

log_info "Branche actuelle: $current_branch"

# Fonction de nettoyage en cas d'erreur
cleanup_on_error() {
    log_error "Une erreur s'est produite. Retour à la branche d'origine..."
    git checkout "$current_branch" 2>/dev/null || true
    log_info "Vous pouvez restaurer vos modifications avec: git stash pop"
    exit 1
}

# Piège pour nettoyer en cas d'erreur
trap cleanup_on_error ERR

# ============================================
# GESTION DES BRANCHES RELEASE
# ============================================
if [[ "$current_branch" =~ ^release/ ]]; then
    log_release "Branche release détectée. Validation du workflow..."
    
    # Extraire la version du nom de branche
    version=$(echo "$current_branch" | sed 's/release\///')
    log_release "Version cible : $version"
    
    # Vérifier que develop existe
    if ! git show-ref --verify --quiet refs/heads/develop; then
        log_error "La branche develop n'existe pas localement!"
        exit 1
    fi
    
    # Déterminer la branche principale
    if git show-ref --verify --quiet refs/heads/main; then
        main_branch="main"
    elif git show-ref --verify --quiet refs/heads/master; then
        main_branch="master"
    else
        log_error "Aucune branche main ou master trouvée!"
        exit 1
    fi
    
    # Sauvegarder l'état actuel
    log_info "Sauvegarde de l'état actuel..."
    git stash push -m "pre-push-release-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
    
    # Étape 1: Vérifier que la release contient tout develop
    log_release "Étape 1/5: Vérification que la release contient tous les commits de develop..."
    git fetch origin develop --quiet
    
    commits_behind=$(git rev-list --count HEAD..origin/develop || echo "0")
    commits_ahead=$(git rev-list --count origin/develop..HEAD || echo "0")
    
    if [ "$commits_behind" -gt 0 ]; then
        log_error "✗ La release est en retard de $commits_behind commits sur develop!"
        log_warning "Une release DOIT contenir TOUS les commits de develop"
        
        # Montrer quelques commits manquants
        log_info "Commits manquants (5 premiers):"
        git log --oneline HEAD..origin/develop | head -5
        
        if [ "$ENFORCE_RELEASE_RULES" = true ]; then
            echo
            log_error "Vous devez d'abord mettre à jour avec develop:"
            log_info "git merge origin/develop"
            log_info "git push origin $current_branch"
            
            # Restaurer le stash
            if git stash list | grep -q "pre-push-release-backup"; then
                git stash pop
            fi
            exit 1
        fi
    else
        log_success "✓ La release contient tous les commits de develop"
        if [ "$commits_ahead" -gt 0 ]; then
            log_info "  $commits_ahead commits spécifiques à la release"
        fi
    fi
    
    # Étape 2: Vérifier le contenu des commits de la release
    log_release "Étape 2/5: Analyse des commits de la release..."
    
    # Obtenir les commits propres à la release
    release_commits=""
    if git rev-list origin/develop..HEAD >/dev/null 2>&1; then
        release_commits=$(git rev-list origin/develop..HEAD)
    fi
    
    if [ -n "$release_commits" ]; then
        feature_count=0
        fix_count=0
        chore_count=0
        docs_count=0
        other_count=0
        
        echo
        while IFS= read -r commit; do
            if [ -z "$commit" ]; then
                continue
            fi
            
            msg=$(git log -1 --pretty=format:"%s" "$commit" || echo "")
            hash=$(git log -1 --pretty=format:"%h" "$commit" || echo "")
            
            # Classifier le commit
            if echo "$msg" | grep -qE "^(feat|feature):"; then
                feature_count=$((feature_count + 1))
                if [ "$ALLOW_FEATURES_IN_RELEASE" = false ]; then
                    log_error "  $hash ❌ NOUVELLE FEATURE: $msg"
                else
                    log_warning "  $hash ⚠️  Feature: $msg"
                fi
            elif echo "$msg" | grep -q "^fix:"; then
                fix_count=$((fix_count + 1))
                log_success "  $hash ✓ Fix: $msg"
            elif echo "$msg" | grep -qE "^(chore|version):"; then
                chore_count=$((chore_count + 1))
                log_success "  $hash ✓ Chore: $msg"
            elif echo "$msg" | grep -q "^docs:"; then
                docs_count=$((docs_count + 1))
                log_success "  $hash ✓ Docs: $msg"
            else
                other_count=$((other_count + 1))
                log_info "  $hash - $msg"
            fi
        done <<< "$release_commits"
        
        echo
        log_info "Résumé des commits de la release:"
        [ $fix_count -gt 0 ] && log_info "  • Corrections: $fix_count"
        [ $chore_count -gt 0 ] && log_info "  • Maintenance: $chore_count"
        [ $docs_count -gt 0 ] && log_info "  • Documentation: $docs_count"
        [ $feature_count -gt 0 ] && log_warning "  • Features: $feature_count (non recommandé)"
        [ $other_count -gt 0 ] && log_info "  • Autres: $other_count"
        
        if [ "$ALLOW_FEATURES_IN_RELEASE" = false ] && [ $feature_count -gt 0 ]; then
            echo
            log_error "Les branches release ne doivent contenir QUE:"
            log_info "  • Corrections de bugs (fix:)"
            log_info "  • Mises à jour de version (chore: ou version:)"
            log_info "  • Documentation (docs:)"
            
            if [ "$ENFORCE_RELEASE_RULES" = true ]; then
                # Restaurer le stash
                if git stash list | grep -q "pre-push-release-backup"; then
                    git stash pop
                fi
                exit 1
            fi
        fi
    fi
    
    # Étape 3: Vérifier les fichiers de version
    if [ "$CHECK_VERSION_FILES" = true ]; then
        log_release "Étape 3/5: Vérification des fichiers de version..."
        
        # Liste des fichiers de version courants
        version_files=("package.json" "package-lock.json" "pom.xml" "build.gradle" "setup.py" "Cargo.toml" "version.txt" "VERSION" "pubspec.yaml")
        version_updated=false
        
        echo
        for file in "${version_files[@]}"; do
            if [ -f "$file" ]; then
                # Vérifier si modifié dans cette release
                if git diff origin/develop..HEAD --name-only 2>/dev/null | grep -q "^$file$"; then
                    log_success "  ✓ $file mis à jour"
                    version_updated=true
                else
                    log_warning "  ⚠ $file existe mais non modifié"
                fi
            fi
        done
        
        if [ "$version_updated" = false ]; then
            log_warning "⚠ Aucun fichier de version n'a été mis à jour!"
            log_warning "Assurez-vous de mettre à jour le numéro de version"
        fi
    fi
    
    # Étape 4: Vérifier le CHANGELOG
    log_release "Étape 4/5: Vérification du CHANGELOG..."
    
    changelog_files=("CHANGELOG.md" "CHANGELOG" "HISTORY.md" "NEWS.md")
    changelog_found=false
    changelog_updated=false
    
    for file in "${changelog_files[@]}"; do
        if [ -f "$file" ]; then
            changelog_found=true
            
            if git diff origin/develop..HEAD --name-only 2>/dev/null | grep -q "^$file$"; then
                changelog_updated=true
                log_success "✓ $file a été mis à jour"
                
                # Vérifier si la version est mentionnée
                if git diff origin/develop..HEAD -- "$file" 2>/dev/null | grep -qi "$version"; then
                    log_success "✓ La version $version est mentionnée dans le changelog"
                else
                    log_warning "⚠ La version $version n'apparaît pas dans les modifications du changelog"
                fi
            else
                log_warning "⚠ $file existe mais n'a pas été mis à jour"
            fi
            break
        fi
    done
    
    if [ "$changelog_found" = false ]; then
        log_info "Aucun fichier CHANGELOG trouvé"
    fi
    
    # Étape 5: Checklist finale
    log_release "Étape 5/5: Checklist finale..."
    echo
    
    if [ "$ENFORCE_RELEASE_RULES" = true ]; then
        log_info "Avant de pusher cette release, confirmez:"
        echo "  □ Les tests unitaires passent"
        echo "  □ Les tests d'intégration sont OK"
        echo "  □ La documentation est à jour"
        echo "  □ Le numéro de version est correct ($version)"
        echo "  □ Le CHANGELOG est complet"
        echo "  □ Les migrations DB sont prêtes (si applicable)"
        echo
        log_info "Tout est prêt? (y/n)"
        read -r response < /dev/tty || response="y"
        
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_warning "Push annulé. Finissez la préparation de la release."
            
            # Restaurer le stash
            if git stash list | grep -q "pre-push-release-backup"; then
                git stash pop
            fi
            exit 1
        fi
    fi
    
    # Restaurer les modifications stashées
    if git stash list | grep -q "pre-push-release-backup"; then
        log_info "Restauration des modifications sauvegardées..."
        git stash pop
    fi
    
    # Guide pour la suite
    echo
    log_release "=== WORKFLOW À SUIVRE APRÈS CE PUSH ==="
    log_release "1. Créer une PR : $current_branch → $main_branch"
    log_release "2. Après validation et merge dans $main_branch :"
    log_release "   git checkout $main_branch"
    log_release "   git pull origin $main_branch"
    log_release "   git tag -a $version -m \"Release $version\""
    log_release "   git push origin $version"
    log_release "3. Merger aussi dans develop (IMPORTANT!):"
    log_release "   git checkout develop"
    log_release "   git merge $main_branch"
    log_release "   git push origin develop"
    log_release "4. Supprimer la branche release:"
    log_release "   git branch -d $current_branch"
    log_release "   git push origin --delete $current_branch"
    log_release "5. Déployer en production avec le tag $version"
    
    log_success "Validation release terminée. Push autorisé!"
    exit 0
fi

# ============================================
# GESTION DES BRANCHES HOTFIX
# ============================================
if [[ "$current_branch" =~ ^hotfix/ ]]; then
    log_hotfix "Branche hotfix détectée. Validation du workflow Git Flow..."
    
    # Déterminer la branche principale
    if git show-ref --verify --quiet refs/heads/main; then
        main_branch="main"
    elif git show-ref --verify --quiet refs/heads/master; then
        main_branch="master"
    else
        log_error "Aucune branche main ou master trouvée!"
        exit 1
    fi
    
    log_hotfix "Branche principale: $main_branch"
    
    # Fonction pour vérifier si les commits du hotfix sont dans une branche
    check_commits_in_branch() {
        local target_branch=$1
        local missing_commits=0
        
        # Obtenir tous les commits du hotfix qui ne sont pas dans main
        local hotfix_commits=$(git rev-list $main_branch..$current_branch 2>/dev/null)
        
        if [ -z "$hotfix_commits" ]; then
            return 0
        fi
        
        # Vérifier chaque commit
        for commit in $hotfix_commits; do
            if ! git branch -r --contains $commit 2>/dev/null | grep -q "origin/$target_branch"; then
                missing_commits=$((missing_commits + 1))
            fi
        done
        
        return $missing_commits
    }
    
    # Fonction pour proposer le merge automatique dans develop
    suggest_auto_merge_to_develop() {
        echo
        log_warning "Le hotfix n'est pas encore dans develop!"
        log_info "Voulez-vous merger automatiquement dans develop? (y/n)"
        read -r response < /dev/tty
        
        if [[ "$response" =~ ^[Yy]$ ]]; then
            log_info "Merge automatique en cours..."
            
            # Sauvegarder l'état actuel
            git stash push -m "pre-push-auto-merge-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
            
            # Merger dans develop
            git checkout develop
            git pull origin develop
            
            log_info "Tentative de merge de $current_branch dans develop..."
            if git merge $current_branch --no-ff -m "Merge $current_branch into develop (Git Flow)"; then
                log_success "Merge réussi dans develop!"
                git push origin develop
                
                # Retour sur le hotfix
                git checkout $current_branch
                
                # Restaurer le stash si nécessaire
                if git stash list | grep -q "pre-push-auto-merge"; then
                    git stash pop
                fi
                
                return 0
            else
                log_error "Conflits détectés lors du merge dans develop!"
                echo
                log_warning "Résolvez les conflits manuellement:"
                log_warning "1. Résolvez les conflits dans les fichiers"
                log_warning "2. git add <fichiers résolus>"
                log_warning "3. git commit"
                log_warning "4. git push origin develop"
                log_warning "5. git checkout $current_branch"
                return 1
            fi
        fi
        
        return 1
    }
    
    # Sauvegarder l'état actuel
    log_info "Sauvegarde de l'état actuel..."
    git stash push -m "pre-push-hotfix-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true
    
    # Vérifier que le hotfix est basé sur la dernière version de main
    log_hotfix "Étape 1/3: Vérification que le hotfix est à jour avec $main_branch..."
    git fetch origin $main_branch
    
    if ! git merge-base --is-ancestor origin/$main_branch HEAD; then
        log_warning "Le hotfix n'est pas basé sur la dernière version de $main_branch"
        log_info "Mise à jour en cours..."
        
        if git rebase origin/$main_branch; then
            log_success "Hotfix mis à jour avec succès"
        else
            log_error "Conflits lors de la mise à jour avec $main_branch"
            exit 1
        fi
    else
        log_success "✓ Le hotfix est basé sur la dernière version de $main_branch"
    fi
    
    # Vérifier si develop existe et faire les vérifications
    if git show-ref --verify --quiet refs/heads/develop; then
        log_hotfix "Étape 2/3: Vérification du merge dans develop..."
        
        # Fetch develop pour avoir les dernières infos
        git fetch origin develop
        
        # Vérifier si les commits sont dans develop
        if check_commits_in_branch "develop"; then
            log_success "✓ Tous les commits du hotfix sont déjà dans develop"
        else
            if [ "$ENFORCE_HOTFIX_DOUBLE_MERGE" = true ]; then
                log_error "✗ Le workflow Git Flow n'est pas respecté!"
                echo
                log_error "Les commits du hotfix DOIVENT être dans develop avant le push."
                log_error "Cela évite que le bug corrigé ne revienne dans la prochaine release!"
                
                # Proposer le merge automatique
                if ! suggest_auto_merge_to_develop; then
                    echo
                    log_error "Options disponibles:"
                    log_info "1. Merger manuellement dans develop:"
                    log_info "   git checkout develop"
                    log_info "   git pull origin develop"
                    log_info "   git merge --no-ff $current_branch"
                    log_info "   git push origin develop"
                    log_info "   git checkout $current_branch"
                    echo
                    log_info "2. Forcer le push (DANGEREUX - le bug reviendra!):"
                    log_info "   git push --no-verify"
                    echo
                    exit 1
                fi
            else
                log_warning "⚠ ATTENTION: Les commits du hotfix ne sont pas dans develop"
                log_warning "Le bug risque de revenir dans la prochaine release!"
                log_warning "Pensez à merger dans develop après ce push!"
            fi
        fi
        
        # Test de compatibilité avec develop
        log_hotfix "Étape 3/3: Test de compatibilité future avec develop..."
        temp_branch="temp-hotfix-check-$(date +%s)"
        git checkout -b "$temp_branch" >/dev/null 2>&1
        
        if git merge origin/develop --no-commit --no-ff >/dev/null 2>&1; then
            log_success "✓ Aucun conflit prévu avec develop"
            git merge --abort >/dev/null 2>&1
        else
            log_warning "⚠ Des conflits sont attendus lors du merge dans develop"
            git merge --abort >/dev/null 2>&1
        fi
        
        git checkout "$current_branch" >/dev/null 2>&1
        git branch -D "$temp_branch" >/dev/null 2>&1
    fi
    
    # Restaurer les modifications stashées
    if git stash list | grep -q "pre-push-hotfix-backup"; then
        log_info "Restauration des modifications sauvegardées..."
        git stash pop
    fi
    
    # Rappel du workflow
    echo
    log_hotfix "=== RAPPEL DU WORKFLOW HOTFIX ==="
    log_hotfix "Après ce push:"
    log_hotfix "1. Créez/Mergez la PR vers $main_branch"
    log_hotfix "2. Taggez la nouvelle version après merge"
    log_hotfix "3. Si pas encore fait: mergez dans develop"
    log_hotfix "4. Supprimez la branche hotfix"
    
    log_success "Validation hotfix terminée. Push autorisé!"
    exit 0
fi

# ============================================
# GESTION DES BRANCHES FEATURE/BUGFIX (code original)
# ============================================
if [[ ! "$current_branch" =~ ^(feature|bugfix)/ ]]; then
    log_info "Cette branche ($current_branch) n'est pas une branche feature/, bugfix/, hotfix/ ou release/"
    log_info "Le hook pre-push ne s'applique pas. Push autorisé."
    exit 0
fi

log_info "Branche détectée comme feature/ ou bugfix/. Démarrage du processus de rebase..."

# Vérifier que la branche develop existe
if ! git show-ref --verify --quiet refs/heads/develop; then
    log_error "La branche 'develop' n'existe pas localement."
    log_error "Veuillez créer ou récupérer la branche develop avant de continuer."
    exit 1
fi

# Sauvegarder l'état actuel au cas où quelque chose se passe mal
log_info "Sauvegarde de l'état actuel..."
git stash push -m "pre-push-hook-backup-$(date +%Y%m%d-%H%M%S)" --include-untracked || true

# Étape 1: Passer sur la branche develop
log_info "Étape 1/4: Passage sur la branche develop..."
git checkout develop

# Étape 2: Récupérer les dernières modifications de develop
log_info "Étape 2/4: Récupération des dernières modifications de develop..."
if ! git pull origin develop; then
    log_error "Impossible de récupérer les modifications de develop depuis origin"
    git checkout "$current_branch"
    exit 1
fi

# Étape 3: Retourner sur la branche d'origine
log_info "Étape 3/4: Retour sur la branche $current_branch..."
git checkout "$current_branch"

# Étape 4: Rebase de develop sur la branche courante
log_info "Étape 4/4: Rebase de develop sur $current_branch..."

# Effectuer le rebase
if git rebase develop; then
    log_success "Rebase terminé avec succès!"
    
    # Restaurer les modifications stashées si elles existent
    if git stash list | grep -q "pre-push-hook-backup"; then
        log_info "Restauration des modifications sauvegardées..."
        git stash pop
    fi
    
    log_success "La branche $current_branch est maintenant à jour avec develop."
    log_success "Push autorisé!"
    exit 0
else
    log_error "CONFLIT DÉTECTÉ lors du rebase!"
    log_error "Le push a été interrompu."
    echo
    log_warning "Pour résoudre les conflits:"
    log_warning "1. Résolvez les conflits dans les fichiers marqués"
    log_warning "2. Ajoutez les fichiers résolus: git add <fichier>"
    log_warning "3. Continuez le rebase: git rebase --continue"
    log_warning "4. Ou annulez le rebase: git rebase --abort"
    echo
    log_warning "Une fois les conflits résolus, vous pourrez refaire le push."
    
    # Ne pas restaurer le stash en cas de conflit pour éviter d'autres conflits
    exit 1
fi