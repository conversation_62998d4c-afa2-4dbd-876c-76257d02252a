import { Injectable } from '@angular/core';
import { BaseUrlService } from './base-url.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import { DatePipe } from '@angular/common';
import { CommonService } from './common.service';
import { WholeSale } from '../models/whole-sale';
import { environment } from 'src/environments/environment';
import { firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class WholeSaleService {

  base_url: string;

  constructor(
    private baseUrl: BaseUrlService,
    private http: HttpClient,
    private commonSrv: CommonService
  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}/whole-sale`;
  }

  async getWholeSale(param: any): Promise<{ data: WholeSale[], count: number }> {

    try {
      let params = new HttpParams();

      const { offset, limit, startDate, endDate, tel, name, commercialRegion, animateDonutId } = param;

      if (startDate && endDate) {
        params = params.append('startDate', new DatePipe('fr').transform(startDate, 'YYYY-MM-dd'));
        params = params.append('endDate', new DatePipe('fr').transform(endDate, 'YYYY-MM-dd'));
      }
      if (offset) params = params.append('offset', offset);
      if (limit) params = params.append('limit', limit);
      if (name) params = params.append('firstName', name);
      if (commercialRegion) params = params.append('address.commercialRegion', commercialRegion);
      if (animateDonutId) params = params.append('associatedDonutAnimator._id', animateDonutId);
      if (tel) params = params.append('tel', tel);
      params = params.append('enable', true);

      const response = await firstValueFrom(this.http.get<{ data: WholeSale[], count: number }>(`${this.base_url}`, { params }));
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      console.error(errorMessage)
      return error;
    }
  }
}
