import { lastValue<PERSON>rom } from 'rxjs';
import { Injectable } from '@angular/core';
import { Buffer } from 'buffer';
import { Particular, User } from '../../shared/models/user.models';
import { environment } from 'src/environments/environment';
import { QueryResult } from '../../shared/models/query-result';
import { CommonService } from '../../shared/services/common.service';
import { BaseUrlService } from '../../shared/services/base-url.service';
import { UserAction } from '../../shared/actions/user-authorizations.action';
import { EmployeeCimencam } from 'src/app/shared/models/user.models';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Authorization } from '../../shared/types';
import { t } from '../../shared/functions/global.function';
import { Company } from 'src/app/shared/models/company.models';
@Injectable({
  providedIn: 'root',
})
export class UserService {
  base_url: string;
  userAction = UserAction;
  filterListUser = {
    email: '',
    tel: '',
    enable: true
  };
  filterForm = {
    email: '',
    tel: '',
    enable: true,
    firstName: '',
    employeeType: '',
    startDate: null,
    endDate: null,
    region: '',
    city: '',
    district: '',
    commercialRegion: '',
    neighborhood: ''
  };

  indexTab = 0;
  indexTabClient = 0;

  constructor(
    private baseUrl: BaseUrlService,
    private http: HttpClient,
    protected commonSrv: CommonService
  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment?.basePath}`;
  }

  async createUser(user: User): Promise<QueryResult> {
    try {
      delete user._id;
      delete user.profiles;
      user.tel = +user?.tel;
      return await lastValueFrom(
        this.http.post<QueryResult>(`${this.base_url}/users`, user,
          { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      console.log("Error in createUser method:", error);
      console.log("Test commonSrv instance:", this.commonSrv.user);

      return await this.commonSrv.getError(await t('DATA_ERROR'), error);
    }
  }

  async getUser(idUser: string): Promise<User> {
    try {
      return await lastValueFrom(
        this.http.get<User>(`${this.base_url}/users` + '/' + idUser));
    } catch (error) {
      return error;
    }
  }

  async getAfrilandCode(param: any) {
    try {
      const encodedCredentials = Buffer.from(`LONDO:Vui2na3t0lrNn7Cxl`).toString(
        'base64',
      );

      return await lastValueFrom(
        this.http.post<any[]>(`${this.base_url}/callback/generate-validation-key`, { email: param },
          { headers: { 'Authorization': `Basic ${encodedCredentials}` } }));
    } catch (error) {
      return error;
    }
  }

  async getUsers(param: any): Promise<HttpErrorResponse | { data: User[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { tel, email, category, offset, limit, employeeType, isValidated, enable = true, projection, startDate, endDate, region, city, district, commercialRegion, firstName } = param;
      if (category !== undefined) { params = params.append('category', category); }
      if (offset) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (email) { params = params.append('email', email); }
      if (employeeType) { params = params.append('employeeType', employeeType); }
      if (tel) { params = params.append('tel', tel); }
      if (isValidated !== undefined) { params = params.append('isValidated', isValidated); }
      if (projection) { params = params.append('projection', projection); }
      if (startDate) {
        params = params.append('startDate', this.formatDateForAPI(startDate));
      }
      if (endDate) {
        params = params.append('endDate', this.formatDateForAPI(endDate));
      }
      if (region) {
        params = params.append('address.region', region);
      }
      if (city) {
        params = params.append('address.city', city);
      }
      if (district) {
        params = params.append('address.district', district);
      }
      if (firstName) { params = params.append('firstName', firstName); }
      if (commercialRegion) {
        params = params.append('address.commercialRegion', commercialRegion);
      }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<HttpErrorResponse | { data: User[], count: number }>(`${this.base_url}/users`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async getUsersPoint(param: any): Promise<HttpErrorResponse | { data: Particular[], count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { tel, email, category, offset, limit, isValidated, enable = true, projection } = param;
      if (category !== undefined) { params = params.append('category', category); }
      if (offset) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (email) { params = params.append('email', email); }
      if (tel) { params = params.append('tel', tel); }
      if (isValidated !== undefined) { params = params.append('isValidated', isValidated); }
      if (projection) { params = params.append('projection', projection); }
      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<HttpErrorResponse | { data: Particular[], count: number }>(`${this.base_url}/users`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async getAuthorization(): Promise<Authorization[]> {
    try {
      return await lastValueFrom(this.http.get<Authorization[]>(`${this.base_url}/authorizations`));
    } catch (error) {
      return error;
    }
  }

  async updateUsers(user: User): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      if (user?.tel) user.tel = +user?.tel;
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/users/${user._id}`, user));
    } catch (error) {
      return this.commonSrv.getError('Une erreur s\'est produite', error);
    } finally { this.commonSrv.isLoading = false; };
  }

  async deleteUser(user: User): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/users/${user._id}`, { enable: !user?.enable }));
    } catch (error) {
      return this.commonSrv.getError('Une erreur est survenue', error);
    } finally { this.commonSrv.isLoading = false; };
  }

  async changePassword(user: User): Promise<QueryResult> {
    try {
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/users/${user._id}/reset-password`, { password: user.password }));
    } catch (error) {
      return this.commonSrv.getError('Une erreur s\'est produite', error);
    }
  }

  async getDirection(): Promise<any> {
    try {
      return await lastValueFrom(
        this.http.get<any[]>(`${this.base_url}/directions`));
    } catch (error) {
      return await this.commonSrv.getError('Une erreur s\'est produite', error);
    }
  }

  async ValidateAccountEmployee(user: EmployeeCimencam): Promise<QueryResult> {
    try {
      return await lastValueFrom(
        this.http.patch(`${this.base_url}/users/validate/${user._id}`, { enable: true, isValidated: !user.isValidated }));
    } catch (error) {
      return this.commonSrv.getError('Une erreur s\'est produite', error);
    }
  }

  async migrateUserToOtherCompany(userId: string, newCompany: Company): Promise<QueryResult> {
    try {
      const response = await lastValueFrom(
        this.http.patch(`${this.base_url}/users/${userId}/migrate-user`, { companyId: newCompany._id })
      );
      return response;
    } catch (error) {
      return this.commonSrv.getError("Erreur lors du déplacement de l'utilisateur :", error);
    }
  }
  private formatDateForAPI(date: Date): string {
    if (!date) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * Migre plusieurs utilisateurs particuliers vers un animateur donné
   * @param userIds string[] : IDs des utilisateurs particuliers à migrer
   * @param targetAnimator { _id: string, firstName: string, category: number }
   */
  async migrateParticuliersToAnimator(userIds: string[], targetAnimator: { _id: string, firstName: string, category: number }): Promise<any> {
    try {
      return await lastValueFrom(
        this.http.patch<any>(`${this.base_url}/users/migrate-user-particular-to-donut-animator`, { userIds, targetAnimator })
      );
    } catch (error) {
      return await this.commonSrv.getError("Erreur lors de la migration :", error);
    }
  }

}
