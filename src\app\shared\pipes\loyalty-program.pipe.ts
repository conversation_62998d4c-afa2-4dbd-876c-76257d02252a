import { Pipe, PipeTransform } from '@angular/core';
import { FidelityStatus } from '../models/user.models';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'loyaltyProgramLevelLabel'
})
export class LoyaltyProgramPipe implements PipeTransform {

  transform(value: FidelityStatus, ...args: unknown[]): unknown {
    let label: string
    switch (value) {
      case FidelityStatus.PRIVILEGE:
        label = 'PRIVILEGE';
        break;
      case FidelityStatus.DIAMOND:
        label = 'DIAMOND';
        break;
      case FidelityStatus.PREMIUM:
        label = 'PREMIUM';
        break;

      default:
        label = '0';
        break;
    }
    return label;
  }

}

@Pipe({
  name: 'loyaltyProgramLevelColor'
})
export class LoyaltyProgramColorPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: FidelityStatus): SafeHtml {
    let label: string;
    let color: string;

    switch (value) {
      case FidelityStatus.PRIVILEGE:
        label = 'PRIVILEGE';
        color = '#F59E0B'; // orange
        break;
      case FidelityStatus.DIAMOND:
        label = 'DIAMOND';
        color = '#0EA5E9'; // bleu
        break;
      case FidelityStatus.PREMIUM:
        label = 'PREMIUM';
        color = '#10B981'; // vert
        break;
      default:
        label = 'N/A';
        color = '#9CA3AF'; // gris
        break;
    }

    return this.sanitizer.bypassSecurityTrustHtml(
      `<span style="font-size: 0.75rem; font-weight: bold; color: ${color}; background-color: #f3f4f6; padding: 2px 6px; border-radius: 6px;">
        ${label}
      </span>`
    );
  }
}
