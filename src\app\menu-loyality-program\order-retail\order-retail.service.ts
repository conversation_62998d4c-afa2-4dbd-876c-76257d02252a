import { lastValueFrom } from 'rxjs';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams, } from '@angular/common/http';
import { CommonService } from 'src/app/shared/services/common.service';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { ValidationImage, OrderRetail, } from 'src/app/shared/models/orderRetail';
import { ImageOrderRetail, QueryFilter, QueryResult } from 'src/app/shared/types';
import { t } from 'src/app/shared/functions/global.function';
import * as moment from 'moment';
import { UserCategory } from 'src/app/shared/enums/user-category.enum';

@Injectable({
  providedIn: 'root',
})
export class OrderRetailService {
  url: string;
  urls: string;

  modalDetail: boolean;
  showDialogValidated: any;
  order: OrderRetail;

  constructor(
    private http: HttpClient,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService
  ) {
    this.url =
    `${this.baseUrlService.getOrigin()}${environment.basePath}/scanner-data`;
    this.urls = this.baseUrlService.getOrigin() + environment.basePath;

  }

  async getOrdersRetails(param?: any): Promise<{ data: OrderRetail[]; count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      const { appRef, status, offset, limit, userCategory, date, enable = true, region } = param;
      if (offset) params = params.append('offset', offset);

      if (appRef) params = params.append('appRef', appRef);

      if (limit) params = params.append('limit', limit);

      if (status) params = params.append('status', status);

      if ((this.commonSrv?.user?.category === UserCategory.COMMERCIAL) && userCommercialReg) { params = params.append('user.address.commercialRegion', userCommercialReg) }
      if (userCategory) params = params.append('user.category', userCategory);
      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      if (region) params = params.append('user.address.commercialRegion', region);

      params = params.append('enable', enable);

      return await lastValueFrom(
        this.http.get<{ data: OrderRetail[]; count: number }>(`${this.url}`, {
          params,
        })
      );
    } catch (error) {
      return error;
    } finally {
      this.commonSrv.isLoading = false;
    }
  }

  async getRecapInfosForOrderList(param: any): Promise<{ data: { totalAmount: number, totalTonnes: number, numberOfOrders: number } }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const userCommercialReg = this.commonSrv?.user?.address?.commercialRegion;

      const { appRef, status, userCategory, date, enable = true, region } = param;
      if (appRef) params = params.append('appRef', appRef);

      if (status) params = params.append('status', status);

      if ((this.commonSrv?.user?.category === UserCategory.COMMERCIAL) && userCommercialReg) { params = params.append('user.address.commercialRegion', userCommercialReg) }
      if (userCategory) params = params.append('user.category', userCategory);
      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      if (region) params = params.append('user.address.commercialRegion', region);

      params = params.append('enable', enable);
      return await lastValueFrom(this.http.get<
        { data: { totalAmount: number, totalTonnes: number, numberOfOrders: number } }>(`${this.url}/recap-orderRetail-list`, { params }));
    } catch (error) {
      return error;
    } finally { this.commonSrv.isLoading = false; }
  }

  async validateRetailOrder(order: OrderRetail): Promise<QueryResult> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.patch(
          `${this.url}/${order._id}/validate`,
          { cart: order?.items },
          {
            headers: {
              Authorization: `Bearer ${this.commonSrv?.user?.accessToken}`,
            },
          }
        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async rejectRetailOrder(
    order: OrderRetail,
    messageReject: any
  ): Promise<QueryResult> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.patch(
          `${this.url}/${order._id}/rejectOder`,
          {
            rejectMessage: messageReject,
            user: this.commonSrv.user,
          },
          {
            headers: {
              Authorization: `Bearer ${this.commonSrv?.user?.accessToken}`,
            },
          }
        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async getImage(query: QueryFilter) {
    let params = new HttpParams();
    const { appRef } = query;
    if (appRef) { params = params.append('appRef', appRef); }
    return await lastValueFrom(this.http.get<ValidationImage>(`${this.urls}/images`, { params })
    );
  }

  async saveImage(imgOrder: ImageOrderRetail): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.post<any>(`${this.urls}/images`, imgOrder));
    } catch (error) {
      return await this.commonSrv.getError(await t('OPERATION_DONNE'), error);
    }
  }

  async updateImage(id: string, body: any): Promise<QueryResult> {
    try {
      return await lastValueFrom(this.http.patch(this.urls + '/images/' + id, { dataUrls: body }));
    } catch (error) {
      return error;
    }
  }
}
