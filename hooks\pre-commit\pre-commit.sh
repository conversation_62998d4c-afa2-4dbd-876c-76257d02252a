# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages d'erreur
print_error() {
    echo -e "${RED}❌ ERREUR: $1${NC}"
}

# Fonction pour afficher les messages d'information
print_info() {
    echo -e "${YELLOW}ℹ️  INFO: $1${NC}"
}

# Fonction pour afficher les messages de succès
print_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

# Récupération du nom de la branche courante
current_branch=$(git symbolic-ref --short HEAD 2>/dev/null)

# Vérification si on est sur une branche
if [ -z "$current_branch" ]; then
    print_error "Impossible de déterminer la branche courante"
    exit 1
fi

print_info "Vérification du format de la branche: $current_branch"

# Fonction pour valider le format des branches feature et bugfix
validate_feature_bugfix() {
    local branch_type=$1
    local branch_name=$2
    
    # Format attendu: feature/<nom>_YYYYMMDD ou bugfix/<nom>_YYYYMMDD
    if [[ $branch_name =~ ^${branch_type}/[a-zA-Z0-9_-]+_[0-9]{8}$ ]]; then
        # Extraction de la date
        date_part=$(echo "$branch_name" | grep -o '[0-9]{8}$')
        
        # Validation de la date (format YYYYMMDD)
        if date -d "$date_part" >/dev/null 2>&1; then
            return 0
        else
            print_error "La date '$date_part' n'est pas valide (format YYYYMMDD attendu)"
            return 1
        fi
    else
        print_error "Format incorrect pour la branche $branch_type"
        print_info "Format attendu: $branch_type/<nom>_YYYYMMDD"
        print_info "Exemple: $branch_type/nouvelle-fonctionnalite_20250124"
        return 1
    fi
}

# Fonction pour valider le format des branches hotfix
validate_hotfix() {
    local branch_name=$1
    
    # Format attendu: hotfix/<nom>_<version affectée>
    if [[ $branch_name =~ ^hotfix/[a-zA-Z0-9_-]+_[a-zA-Z0-9._-]+$ ]]; then
        return 0
    else
        print_error "Format incorrect pour la branche hotfix"
        print_info "Format attendu: hotfix/<nom>_<version_affectée>"
        print_info "Exemple: hotfix/correction-critique_v1.2.3"
        return 1
    fi
}

# Fonction pour valider le format des branches release
validate_release() {
    local branch_name=$1
    
    # Format attendu: release/<version de la release>
    if [[ $branch_name =~ ^release/[a-zA-Z0-9._-]+$ ]]; then
        return 0
    else
        print_error "Format incorrect pour la branche release"
        print_info "Format attendu: release/<version_de_la_release>"
        print_info "Exemple: release/v1.5.0 ou release/2.0.0-beta"
        return 1
    fi
}

# Validation selon le type de branche
case "$current_branch" in
    feature/*)
        if validate_feature_bugfix "feature" "$current_branch"; then
            print_success "Format de branche feature valide"
        else
            print_error "Commit bloqué - Veuillez renommer votre branche selon le format requis"
            exit 1
        fi
        ;;
    bugfix/*)
        if validate_feature_bugfix "bugfix" "$current_branch"; then
            print_success "Format de branche bugfix valide"
        else
            print_error "Commit bloqué - Veuillez renommer votre branche selon le format requis"
            exit 1
        fi
        ;;
    hotfix/*)
        if validate_hotfix "$current_branch"; then
            print_success "Format de branche hotfix valide"
        else
            print_error "Commit bloqué - Veuillez renommer votre branche selon le format requis"
            exit 1
        fi
        ;;
    release/*)
        if validate_release "$current_branch"; then
            print_success "Format de branche release valide"
        else
            print_error "Commit bloqué - Veuillez renommer votre branche selon le format requis"
            exit 1
        fi
        ;;
    main|master|develop|dev)
        print_info "Branche principale détectée ($current_branch) - aucune validation requise"
        ;;
    *)
        print_error "Branche '$current_branch' ne respecte pas les conventions de nommage"
        print_info "Seules les branches suivantes sont autorisées :"
        echo "  • feature/<nom>_YYYYMMDD"
        echo "  • bugfix/<nom>_YYYYMMDD"
        echo "  • hotfix/<nom>_<version_affectée>"
        echo "  • release/<version_de_la_release>"
        echo "  • main, master, develop, dev (branches principales)"
        print_error "Commit bloqué - Veuillez renommer votre branche selon un format autorisé"
        exit 1
        ;;
esac

exit 0
