import { Pipe, PipeTransform } from '@angular/core';
import { CompanyCategory } from '../enums/Company-category.enum';
import { UserCategory } from '../enums/user-category.enum';

@Pipe({
  name: 'categoryUserName'
})
export class CategoryUserNamePipe implements PipeTransform {

  transform(value: any, ...args: unknown[]): unknown {

    if (value >= CompanyCategory.Baker) {
      return CompanyCategory[value] || UserCategory[value];
    }
    return UserCategory[value] || `${'Non renseigné'}`;
  }

}


@Pipe({
  name: 'categoryToString'
})
export class CategoryToStringPipe implements PipeTransform {
  transform(categories: number[]): string[] {
    return categories?.map(category => {
      switch (category) {
        case UserCategory.Particular:
          return 'Particulier';
        case UserCategory.RETAILER:
          return ' Revendeur';
        case UserCategory.EmployeeEntity:
          return ' Employé';
        case CompanyCategory.Baker:
          return ' Boulanger';
        case CompanyCategory.WholeSaler:
          return ' Grossiste';
        case CompanyCategory.GMS:
          return ' GMS';
        // case CompanyCategory.Distributor4:
        //   return ' Grossiste';
        // case CompanyCategory.Distributor5:
        //   return ' Grossiste';
        default:
          return 'inconnu';
      }
    });
  }
}

