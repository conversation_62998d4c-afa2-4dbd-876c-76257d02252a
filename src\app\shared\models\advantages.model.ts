import { FidelityStatus } from "./user.models";

export interface Advantage {
    _id?: string;
    name: string;
    statusValue: FidelityStatus;
    pointsRange: { min: number; max?: number };
    pointsRequired: number;
    oneTimeBenefit: string[];
    monthlyBenefit: string[];
    annualBenefit: string[];
    enable: boolean;
    label?: string;
    imageUrl?: string;
    rewardItems?: string[];
  }