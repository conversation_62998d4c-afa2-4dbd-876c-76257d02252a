import { Order } from 'src/app/shared/models/order';
import { Pipe, PipeTransform } from '@angular/core';
import { RenderType } from '../enums/render-type';
import { t } from '../functions/global.function';
import { OrderStatus } from '../models/order';
import { OrderResellerStatus } from '../models/orderRetail';
import { CompanyCategory } from '../enums/Company-category.enum';
import { UserCategory } from '../enums/user-category.enum';
import { User } from '../models/user.models';

@Pipe({
  name: 'statusOrder',
})
export class StatusOrderPipe implements PipeTransform {
  async transform(value: number, ...users: User[]): Promise<string> {
    if (value === OrderStatus.CREDIT_IN_VALIDATION) {
      return `${await t('CREDIT_IN_VALIDATION_DRH')}`;
    }
    if (value === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      return `${await t('CREDIT_IN_VALIDATION')}`;
    }
    if (value === OrderStatus.PAID) {
      return `${await t('WAITING')}`;
    }
    if (value === OrderStatus.CREATED && users[0]?.category === UserCategory.EmployeeEntity) {
      return `${await t('WAITING_RH')}`;
    }
    if (value === OrderStatus.CREATED) {
      return `${await t('WAITING')}`;
    }
    if (value === OrderStatus.CREDIT_REJECTED) {
      return `${await t('CREDIT_REJECTED')}`;
    }
    if (value === OrderStatus.FAILD) {
      return `${await t('REJECTED')}`;
    }
    if (value === OrderStatus.VALIDATED) {
      return `${await t('VALIDATED')}`;
    }
    if (value === OrderStatus.CANCELLED) {
      return `${await t('CANCEL')}`;
    }
    return '';
  }
}


@Pipe({
  name: 'statusOrderEmployees',
})
export class StatusOrderEmployeesPipe implements PipeTransform {
  async transform(value: number,): Promise<string> {
    if (value === OrderStatus.CREDIT_IN_VALIDATION) {
      return `${await t('CREDIT_IN_VALIDATION_DRH')}`;
    }
    if (value === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      return `${await t('CREDIT_IN_VALIDATION')}`;
    }
    if (value === OrderStatus.PAID) {
      return `${await t('PAID')}`;
    }
    if (value === OrderStatus.CREATED) {
      return `${await t('WAITING_RH')}`;
    }
    if (value === OrderStatus.CREATED) {
      return `${await t('WAITING')}`;
    }
    if (value === OrderStatus.CREDIT_REJECTED) {
      return `${await t('REJECTED')}`;
    }
    if (value === OrderStatus.FAILD) {
      return `${await t('FAILD')}`;
    }
    if (value === OrderStatus.VALIDATED) {
      return `${await t('VALIDATED')}`;
    }
    return '';
  }
}

@Pipe({
  name: 'CategoryClient',
})
export class CategoryClientPipe implements PipeTransform {
  async transform(value: number, ...args: unknown[]): Promise<string> {
    if (value === CompanyCategory.Baker) {
      return 'Baker';
    }
    if (value === CompanyCategory.Baker) {
      return `${await t('DISTRIBUT')}`;
    }
    if (value === CompanyCategory.GMS) {
      return 'GMS';
    }
    // if (value === CompanyCategory.Distributor4) {
    //   return 'Enseigne';
    // }
    // if (value === CompanyCategory.Distributor5) {
    //   return 'Export';
    // }
    if (value === UserCategory.Particular) {
      return 'Particulier';
    }

    return 'Client en compte';
  }
}

@Pipe({
  name: 'statusOrderRetail',
})
export class StatusOrderRetailPipe implements PipeTransform {
  async transform(value: number, ...args: unknown[]): Promise<string> {
    if (value === OrderResellerStatus.CREATED) {
      return `${await t('CREATED')}`;
    }
    if (value === OrderResellerStatus.PREVAIDATED) {
      return `${await t('PREVALID')}`;
    }
    if (value === OrderResellerStatus.VALIDATED) {
      return `${await t('VALIDATED')}`;
    }
    if (value === OrderResellerStatus.REJECTED) {
      return `${await t('REJECTED')}`;
    }
    return '';
  }
}
@Pipe({
  name: 'colorStatusOrder',
})
export class ColorStatusOrderPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === OrderStatus.CREATED) {
      return 'bg-tertiary-200';
    }
    if (value === OrderStatus.PAID) {
      return 'bg-info-100 clr-info-500';
    }
    if (value === OrderStatus.CREDIT_IN_VALIDATION) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === OrderStatus.CREDIT_REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
    if (value === OrderStatus.CREDIT_REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
    if (value === OrderStatus.VALIDATED) {
      return 'bg-primary-300 clr-default-400';
    }
    if (value === OrderStatus.CANCELLED) {
      return 'bg-danger-400 clr-default-400';
    }
     if (value === OrderStatus.FAILD) {
      return 'bg-danger-100 clr-danger-400';
    }
    return '';
  }
}

@Pipe({
  name: 'colorCategoryClient',
})
export class colorCategoryClientPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === 101) {
      return 'bg-tertiary-200';
    }
    if (value === 102) {
      return 'bg-info-100 clr-info-500';
    }
    if (value === 103) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === 104) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === 105) {
      return 'bg-danger-100 clr-danger-400';
    }
    return 'bg-primary-300 clr-default-400';
  }
}

@Pipe({
  name: 'colorRenderTypeOrder',
})

export class ColorRenderTypeOrderPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === RenderType.PICKUP) {
      return 'bg-tertiary-100';
    }
    if (value === RenderType.RENDU) {
      return 'bg-primary-200 clr-default-400';
    }
    return '';
  }
}

@Pipe({
  name: 'PromoCodeStatusColor',
})
export class PromoCodeStatusColorPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === 0) {
      return 'bg-success-200 clr-primary-400';
    }
    if (value === 1) {
      return 'bg-danger-100 clr-danger-400';
    }
    // if (value === -1) {
    //   return 'bg-info-500 clr-default-400';
    // }
    return 'bg-info-500 clr-default-400';
  }
}

@Pipe({
  name: 'renderTypeStatusOrder',
})
export class RenderTypeStatusOrderPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === RenderType.RENDU) {
      return 'Livraison';
    }
    if (value === RenderType.PICKUP) {
      return 'Retrait';
    }
    return '';
  }
}
@Pipe({
  name: 'colorStatusOrderRetail',
})
export class ColorStatusOrderRetailPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === OrderResellerStatus.CREATED) {
      return 'bg-tertiary-200';
    }
    if (value === OrderResellerStatus.PREVAIDATED) {
      return 'bg-info-100 clr-info-500';
    }
    if (value === OrderResellerStatus.VALIDATED) {
      return 'bg-primary-200  clr-tertiary-100';
    }
    if (value === OrderResellerStatus.REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
    return '';
  }
}
