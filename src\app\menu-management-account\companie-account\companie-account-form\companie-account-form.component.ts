import { ShippingService } from 'src/app/menu-administration/management-shippings/shipping.service';
import { CompanyService } from 'src/app/menu-management-account/companie-account/company.service';
import { StoresService } from 'src/app/menu-administration/management-stores/stores.service';
import { CompanyCategory } from 'src/app/shared/enums/Company-category.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserCategory } from 'src/app/shared/enums/user-category.enum';
import { Component, Inject, OnInit } from '@angular/core';
import { CompanyEmployee, Logo, LogoType, User } from 'src/app/shared/models/user.models';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Company } from 'src/app/shared/models/company.models';
import { t } from 'src/app/shared/functions/global.function';
import { Shipping } from 'src/app/shared/models/shipping';
import { Store } from 'src/app/shared/models/store.model';
import { ActivatedRoute, Router } from '@angular/router';
import { APP_BASE_HREF, Location } from '@angular/common';
import { UserService } from '../../services/user.service';
import { Address, Authorization } from 'src/app/shared/types';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { NgxImageCompressService } from 'ngx-image-compress';
import { Balance } from 'src/app/shared/models/balance.model';
import { LogoService } from 'src/app/shared/services/logo.service';
import { Option } from 'src/app/shared/models/option.model';
import { OptionService } from 'src/app/menu-administration/management-options/option.service';

@Component({
  selector: 'mcw-companie-account-form',
  templateUrl: './companie-account-form.component.html',
  styles: [],
  providers: [ConfirmationService, MessageService],
})
export class CompanieAccountFormComponent implements OnInit {
  user: CompanyEmployee = new CompanyEmployee(UserCategory.CompanyUser);
  authorizations: Authorization[] = [];
  usersCompany: CompanyEmployee[] = [];
  regions: any[];
  options: Option[];
  existingUsers: User[] = [];
  selectedExistingUser: User;
  isEditMode: boolean = true;
  modalUserCompany: boolean = false;
  isCreationMode: boolean = true;

  globalShippingAddresses: Shipping[] = [];
  showGlobalShippingAddress: boolean = false;
  selectedGlobalShippingAddress: string[] = [];
  selectedOptionIds: string[] = [];

  address: Address;
  company: Company = new Company();
  title: string;
  index: number;


  isEditUser: boolean;

  precompteRate = [
    { value: 0, label: 'Aucun' },
    { value: 5, label: 'Réel' },
    { value: 2, label: 'Simplifier' },
    { value: 10, label: 'Impôt libératoire' },
  ];

  precompteRateEn = [
    { value: 0, label: 'Empty' },
    { value: 5, label: 'Real' },
    { value: 2, label: 'Simplify' },
    { value: 1, label: 'Final tax' },
  ];
  category = [
    { name: 'Boulanger', code: CompanyCategory.Baker },
    { name: 'Grossiste', code: CompanyCategory.WholeSaler },
    { name: 'GMS', code: CompanyCategory.GMS },
    { name: 'Group', code: CompanyCategory.Group },
    { name: 'Industriel', code: CompanyCategory.Industry },
    { code: CompanyCategory.EXPORT, name: "EXPORT" },
  ];

  cities: any[] = [];
  commercialRegion: any;
  isDetail: boolean;
  isCompanyShipping: boolean;
  isLoading: boolean;
  isLoadingInModal: boolean;
  isEdit: boolean;

  shipping: any = {
    startRef: '',
    endRef: '',
    label: '',
    category: 0,
    companyId: '',
    erpShipToId: '',
    erpShipToDesc: '',
  };
  filterShipping = { startRef: '', endRef: '', enable: true, created_at: '' };
  companyCategory: { name: string; code: CompanyCategory }[];
  filterForm = { companyId: '' };
  companies: Company[];
  stores: Store[];
  offset = 0;
  limit = 50;
  total = 0;
  maxFileSize = 1000000;
  companyShippings: Shipping[];
  language = this.baseHref?.replace(/\//g, '');
  balance: Balance;
  commercials: User[];
  uploadedFiles: any[];
  imageBlobLogo: any;
  imageBlobSignature: any;
  maxSizeImage = 7302567;
  signature: Logo;
  logo: Logo;
  availableBalance: number;

  constructor(
    private location: Location,
    private router: Router,
    private logoSrv: LogoService,
    private storeService: StoresService,
    private route: ActivatedRoute,
    private userService: UserService,
    private shippingService: ShippingService,
    public commonService: CommonService,
    private companyService: CompanyService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private imageCompress: NgxImageCompressService,
    private optionService: OptionService,

    @Inject(APP_BASE_HREF) private baseHref: string,
  ) {
    this.address = commonService.initAddress();
    this.regions = commonService.getRegions();
  }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    this.precompteRate =
      this.language === 'en' ? this.precompteRateEn : this.precompteRate;
    this.selectedcity();
    this.companyCategory = this.commonService.companyCategory;
    if (this.route.snapshot.params['id']) {
      this.isEditMode = true;
      this.company = await this.companyService.find(
        this.route.snapshot.params['id']
      );

      await this.getUsersCompanies();
    }
    if (this.router.url.indexOf('show') > -1) {
      this.isEditMode = false;
    }
    await this.getAuthorization();
    await this.getCommercials();
    await this.getOptions();
    await this.getExistingUsers();
    await this.getCompanyShipping();
    await this.getLogoAndSignature()
    await this.loadDefaultShippingAddress();
    await this.getOptions();
    if (this.company?._id) {
      await this.loadCompanyDetails(this.company._id);
    }

    const { creditLimit, openOrderAmount, invoiceInProgress, othersAmount } = this.balance = await this.companyService.getBalance(this.company?._id) as Balance;
    this.availableBalance = (+creditLimit || 0) - (+openOrderAmount || 0) - (+invoiceInProgress || 0) - (+othersAmount || 0);

    this.isCreationMode = !this.company._id;
    this.isLoading = false;
  }

  async getLogoAndSignature() {
    const data = (await this.logoSrv.getLogoCompany({ 'erpSoldToId': this.company?.erpSoldToId }))?.data
    this.logo = data.find(el => el?.logoType === LogoType.LOGO);
    this.signature = data.find(el => el?.logoType === LogoType.SIGNATURE);
  }

  async getCommercials(): Promise<boolean> {
    this.isLoading = true;
    const query = {
      category: UserCategory.COMMERCIAL,
      projection: 'email,tel,firstName,lastName'
    }
    const result = await this.userService.getUsers(query);

    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('GETDATA_ERROR')}`,
        detail: result.error.message
      });
      return this.isLoading = false;
    }
    this.commercials = result.data;
    return this.isLoading = false;
  }

  async selectedcity() {
    const query = {
      offset: this.offset,
      limit: this.limit,
    };
    this.regions = this.commonService.getRegions();
    this.regions.forEach((elt) => {
      const res = this.commonService.getCities(elt);
      this.cities = this.cities.concat(res);

      return this.commonService.getCities(elt);
    });

    const res = await this.storeService.getStores(query);
    this.total = res.count;
    this.stores = res.data;
  }

  regionChange(event: any): string[] {
    const commercialRegion = Object.entries(
      this.commonService.commercialRegions
    ).find(([key, value]) => value.includes(event.value))?.[0];

    if (commercialRegion) {
      this.company.address.commercialRegion = commercialRegion;
    }

    this.cities = this.commonService.getCities(event.value);
    return this.cities;
  }

  async getAuthorization(): Promise<void> {
    const res = await this.userService.getAuthorization();
    this.authorizations = res;
  }

  async getTitle(event: any): Promise<void> {
    if ((event.index = 0)) {
      this.title == `${await t('Detail')}`;
    }
    if ((event.index = 1)) {
      this.title == `${await t('Change')}`;
    }
  }

  manageAction(e: any, action: string) {
    e.checked
      ? this.user?.authorizations?.push(action)
      : this.user?.authorizations?.splice(this.user?.authorizations[action], 1);
  }

  async getExistingUsers(): Promise<void> {
    try {
      const query = {
        category: UserCategory.CompanyUser,
        projection: 'email,firstName,lastName,tel'
      };
      const users = await this.commonService.fetchCompanyUsers(query);

      if (users instanceof HttpErrorResponse) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Error fetching users',
          detail: 'There was a problem retrieving the list of users'
        });
      } else {
        this.existingUsers = users?.data || [];
      }
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'An unexpected error occurred while fetching users'
      });
    }
  }

  private async fetchCompanyUsers(): Promise<{ data: User[] }> {
    const query = {
      category: UserCategory.CompanyUser,
      projection: 'email,firstName,lastName,tel'
    };
    return await this.userService.getUsers(query) as unknown as { data: User[] };
  }

  async createCompany(): Promise<void> {
    this.isLoading = true;

    try {
      // Vérification des champs obligatoires
      if (!this.company.name || !this.company.tel) {
        this.messageService.add({
          severity: 'error',
          summary: `${await t('CREATE_ERROR')}`,
          detail: `${await t('FILL_BLANKSPACE')}`,
        });
        return;
      }

      const mandatoryFields = {
        soldTo: this.company?.erpSoldToId,
        tel: this.company?.tel,
        name: this.company?.name,
      };

      const validationFields = this.commonService.verifyAllFieldsForm(mandatoryFields);
      if (validationFields) {
        this.messageService.add({
          severity: 'error',
          summary: await t('DATA_ERROR'),
          detail: validationFields as string,
        });
        return;
      }

      if (!this.usersCompany.length) {
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'La compagnie doit contenir au moins un utilisateur.',
        });
        return;
      }

      this.company.users = this.usersCompany;
      this.company.users.forEach(user => user.address = this.company?.address);

      const response = await this.companyService.create(this.company);

      if (response?.status < HttpStatusCode.BadRequest) {
        this.messageService.add({
          severity: 'success',
          key: 'confirm',
          sticky: true,
          summary: `${await t('CREATE_ACOUNT')}`,
          detail: response.message || 'La compagnie a été créée avec succès.',
        });
        this.resetForm();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: '' + response?.data,
          detail: '' + response.message,
        });
      }
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Une erreur est survenue lors de la création de la compagnie.',
      });
    } finally {
      this.isLoading = false;
    }
  }

  private resetForm(): void {
    this.company = new Company();
    this.usersCompany = [];
    this.selectedExistingUser;
  }

  onExistingUserSelected() {
    const isUserAlreadyExist = this.usersCompany?.some(user => user?.email === this.selectedExistingUser?.email);
    if (!isUserAlreadyExist) {
      this.usersCompany.push(this.selectedExistingUser as CompanyEmployee);
      this.selectedExistingUser = null;
    } else {
      this.messageService.add({
        severity: 'warn',
        summary: 'User already added',
        detail: 'This user is already in the company'
      });
    }
  }

  openAddUserModal() {
    this.modalUserCompany = true;
    this.user = new CompanyEmployee(UserCategory.CompanyUser);
    this.isEditUser = false;
  }
  async updateCompany(): Promise<boolean> {
    this.isLoading = true;

    try {

      if (!this.company?.name || !this.company?.tel) {
        this.messageService.add({
          severity: 'error',
          summary: `${await t('CREATE_ERROR')}`,
          detail: `${await t('FILL_BLANKSPACE')}`,
        });
        return (this.isLoading = false);
      }


      const mandatoryFields = {
        soldTo: this.company?.erpSoldToId,
        tel: this.company?.tel,
        name: this.company?.name,
      };

      const validationFields = this.commonService.verifyAllFieldsForm(mandatoryFields);
      if (validationFields) {
        this.messageService.add({
          severity: 'error',
          summary: await t('DATA_ERROR'),
          detail: validationFields as string,
        });
        return this.isLoading = false;
      }


      this.company.users = this.usersCompany;
      // this.company.users.forEach(user => user.address = this.company?.address);

      const response = await this.companyService.update(this.company);

      if (response?.status < HttpStatusCode.BadRequest) {
        this.messageService.add({
          severity: 'success',
          key: 'confirm',
          sticky: true,
          summary: `${await t('UPDATE_SUCCESS')}`,
          detail: response.message || 'La compagnie a été mise à jour avec succès.',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: '' + response?.data,
          detail: '' + response.message,
        });
      }
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Une erreur est survenue lors de la mise à jour de la compagnie.',
      });
    } finally {
      this.isLoading = false;
    }
  }

  async deleteUserCompany(user: CompanyEmployee) {
    this.confirmationService.confirm({
      message: `${await t('QUESTION_MODALUSER')}`,
      header: `${await t('HEADER_MODALUser')} ${user?.firstName.split(' ')[0]}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        await this.userService.deleteUser(user);
        // await this.getUsersCompanies();
        this.messageService.add({
          severity: 'success',
          summary: `${await t('DISABLE_DONE')}`,
        });
      },
    });
  }

  async getUsersCompanies() {
    const query = { email: '', enable: true, category: UserCategory.CompanyUser };
    const res = await this.companyService.getUsersCompany(
      this.company?._id,
      query
    );

    if (res instanceof Error) {
      return this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: 'Error while gettins users company',
      });
    }
    this.usersCompany = res?.data?.sort();
  }

  ShowEditUser(
    user: CompanyEmployee = new CompanyEmployee(UserCategory.CompanyUser),
    index: number
  ): void {
    this.modalUserCompany = true;
    this.user = { ...user };
    this.index = index;
  }

  async addUserCompany(): Promise<void> {
    const mandatoryFields = {
      email: this.user?.email,
      tel: this.user?.tel,
      password: this.user?.password,
      firstName: this.user?.firstName,
      lastName: this.user?.lastName
    }
    const validationFields = this.commonService.verifyAllFieldsForm(mandatoryFields);
    if (validationFields) {
      this.messageService.add({
        severity: 'error',
        summary: await t('DATA_ERROR'),
        detail: validationFields as string
      });
      return;
    }

    this.user.tel = +this.user.tel;

    if (this.company?._id) {
      this.user.company = {
        _id: this.company?._id,
        name: this.company?.name,
        category: this.company?.category,
      };
      const res = await this.companyService.createUsersCompany(
        this.company?._id,
        this.user
      );

      if (res.status === 201) {
        this.messageService.add({
          severity: 'success',
          summary: `${await t('CREATE_ACOUNT')}`,
          detail: '' + res.message,
        });
        await this.getUsersCompanies();
        this.modalUserCompany = false;
        this.user = new CompanyEmployee(this.user.category);
      } else {
        this.messageService.add({
          severity: 'error',
          summary: '' + res.data,
          detail: '' + res.message,
        });
      }
    } else {
      this.usersCompany.push(this.user);
      this.modalUserCompany = false;
      this.user = new CompanyEmployee(this.user?.category);
    }
  }

  async updateUsers(): Promise<boolean> {
    this.isLoadingInModal = true;
    if (!this.user?.email || !this.user?.tel || !this.user?.company) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CREATE_ERROR')}`,
        detail: `${await t('EMPTY_FILL')}`,
      });
      return (this.isLoadingInModal = false);
    }

    if (this.user?.email && this.user.email.indexOf('@') <= -1) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('INVALIDE_EMAIL')}`,
        detail: `${await t('CORRECT_EMAIL')}`,
      });
      return (this.isLoadingInModal = false);
    }

    const response = await this.userService.updateUsers(this.user);
    this.getUsersCompanies();

    if (response.status < HttpStatusCode.BadGateway) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('UPDATE_ACCOUNT')}`,
        detail: '' + response?.message,
      });
      this.modalUserCompany = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response.data,
        detail: '' + response.message,
      });
    }
    return (this.isLoadingInModal = false);
  }

  async uploadLogoCompany(value: string, logoType: LogoType): Promise<void> {
    const response = await this.companyService.saveLogoCompany(this.company._id, { value, logoType });

    if (response.status < HttpStatusCode.BadGateway) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('LOGO_ACCOUNT_SAVE')}`,
        detail: '' + response?.message,
      });
      this.modalUserCompany = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + response.data,
        detail: '' + response.message,
      });
    }
  }

  async showImageSelected(event: any, keyValue: string) {
    const imageBlob = event?.files[0]?.objectURL?.changingThisBreaksApplicationSecurity;

    const reader = new FileReader();
    const blob = await fetch(imageBlob).then(r => r.blob());

    reader.readAsDataURL(blob);
    reader.onloadend = async () => {
      const image = await this.imageCompress.compressFile(reader.result as string, 0);

      if (this.imageCompress.byteCount(image) > this.maxSizeImage) {
        return this.messageService.add({
          severity: 'warn',
          summary: '',
          detail: `${await t('IMAGE_EXCEED')}`,
        });
      }

      this[keyValue] = image;
    };

  }

  back(): void {
    this.location.back();
  }

  close() {
    this.isCompanyShipping = false;
  }

  async verifyFillTel(): Promise<boolean> {
    if (
      !/\d{ 9 }|\+\d{ 1 } \(\d{ 3 } \) \d{ 3 } -\d{ 4 } /gi.test(`${this.company?.tel}`) ||
      `${this.company?.tel}`.length !== 9
    ) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('DATA_ERROR')}`,
        detail: `${await t('PHONE_ALERT')}`,
      });
      return false;
    }
    return true;
  }

  openAddShipping(companyId: any) {
    this.isCompanyShipping = true;
  }

  goTo(company: Company) {
    this.router.navigate([
      `account/companie-account/company-address/${company?._id}`,
    ]);
  }

  async getCompanyShipping() {
    this.isLoading = true;
    const query = {
      // offset: this.offset,
      // limit: this.limit,
      companyId: this.company?._id,
      ...this.filterShipping,
    };
    this.companyShippings = (
      await this.shippingService?.getShipping(query)
    )?.data?.sort();
    this.isLoading = false;
  }

  async addShipping() {
    this.isLoading = true;
    this.shipping = {
      startRef: this.shipping?.startRef,
      // endRef: this.shipping?.endRef,
      category: this.company.category,
      companyId: this.company?._id,
      label: this.shipping?.label,
      erpShipToId: this.shipping?.erpShipToId,
      erpShipToDesc: this.shipping?.erpShipToDesc,
      amount: this.shipping?.amount,
    };
    const res = await this.shippingService.createShipping(this.shipping);
    this.isLoading = false;
    if (res.status == 200) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('CREATE_ADDRESS')}`,
        detail: '' + res.message,
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + res.data,
        detail: '' + res.message,
      });
    }
    this.isCompanyShipping = false;
  }

  clearToast(): void {
    this.messageService.clear();
  }

  async getCompanies(): Promise<void> {
    const params = {
      enable: true,
      projection: 'id,category,name'
    };

    const res = await this.companyService.getAllCompanies(params);

    if (res?.data) {
      this.companies = res?.data.map(company => ({
        id: company?._id,
        category: company?.category,
        name: company?.name,
      })).sort((a, b) => a.name.localeCompare(b.name));
    }
  }


  async getOptions(): Promise<void> {
    try {
      this.commonService.isLoading = true;
      const response = await this.optionService.getOptions();
      if (response && response.data) {
        this.options = response.data;
      } else {
        console.error('No data returned from options API');
      }
    } catch (error) {
      console.error('Error fetching option:', error);
    } finally {
      this.commonService.isLoading = false;
    }
  }

  async loadCompanyDetails(companyId: string): Promise<void> {
    try {
      if (!this.company) {
        this.company = await this.companyService.find(companyId);
      }
      this.selectedOptionIds = [];
      this.selectedGlobalShippingAddress = [];

      if (this.company?.options?.length) {
        this.selectedOptionIds = this.company.options.map(opt => typeof opt === 'string' ? opt : opt._id);
      }

      if (this.company.associatedShippingOption?.optionId) {

        if (!this.selectedOptionIds.includes(this.company.associatedShippingOption.optionId)) {
          this.selectedOptionIds.push(this.company.associatedShippingOption.optionId);
        }

        if (this.company.associatedShippingOption.shippingAddressId) {
          const addressIds = Array.isArray(this.company.associatedShippingOption.shippingAddressId)
            ? this.company.associatedShippingOption.shippingAddressId
            : [this.company.associatedShippingOption.shippingAddressId];

          this.selectedGlobalShippingAddress = addressIds;
        }
      }
      this.updateShippingAddressVisibility();
    } catch (error) {
      console.error('Error loading company details:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load company details'
      });
    }
  }

  async loadDefaultShippingAddress() {
    this.isLoading = true;
    try {
      const commercialRegion = this.company?.address?.commercialRegion ? this.company?.address?.commercialRegion : null;
      const region = this.company?.address?.region ? this.company.address.region : null;

      if (!commercialRegion && !region) {
        console.warn("Commercial region or region not provided");
        return;
      }

      const defaultShipping = await this.shippingService?.getDefaultShippings({ commercialRegion: commercialRegion || region });

      if (defaultShipping?.data) { this.globalShippingAddresses = defaultShipping.data; }
    } catch (error) {
      console.error('Error loading default shipping addresses:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load shipping addresses'
      });
    } finally {
      this.isLoading = false;
    }
  }
  updateShippingAddressVisibility(): void {
    const deliveryDiscountOption = this.options?.find(
      option => option?.name === 'Remise livraison'
    );

    const hasDeliveryOption = this.selectedOptionIds.includes(
      deliveryDiscountOption?._id
    );

    this.showGlobalShippingAddress = hasDeliveryOption;
    // Si l'option de livraison est désactivée, réinitialiser les adresses sélectionnées
    if (!hasDeliveryOption) {
      this.selectedGlobalShippingAddress = [];
      if (this.company.associatedShippingOption) {
        delete this.company.associatedShippingOption;
      }
    }
  }

  updateCompanyOptions(): void {
    this.company.options = [...this.selectedOptionIds];
    // Gérer l'option de remise livraison séparément
    const deliveryDiscountOption = this.options?.find(
      option => option.name === 'Remise livraison'
    );

    if (deliveryDiscountOption?._id) {
      if (this.selectedOptionIds.includes(deliveryDiscountOption._id)) {
        this.company.associatedShippingOption = {
          optionId: deliveryDiscountOption._id,
          shippingAddressId: [...this.selectedGlobalShippingAddress]
        };
      } else {
        delete this.company.associatedShippingOption;
        this.selectedGlobalShippingAddress = [];
      }
    }
  }

  onOptionChange(event: any): void {
    this.selectedOptionIds = event?.value || [];
    this.updateShippingAddressVisibility();
    this.updateCompanyOptions();
  }

  onGlobalShippingAddressChange(event: any): void {
    this.selectedGlobalShippingAddress = event?.value || [];
    this.updateCompanyOptions();
  }
  getSelectedOptionsNames(): string {
    if (!this.options?.length || !this.selectedOptionIds?.length) { return ''; }

    const string = this.selectedOptionIds.map(id => {
      const option = this.options.find(opt => opt._id === id);
      return option?.name || '';
    }).filter(Boolean).join(', ');
    console.log('string:' + string);

    return string
  }

  getSelectedShippingAddressesNames(): string {
    if (!this.globalShippingAddresses?.length || !this.selectedGlobalShippingAddress?.length) { return ''; }

    return this.selectedGlobalShippingAddress
      .map(id => {
        const addr = this.globalShippingAddresses.find(address => address._id === id);
        return addr?.endRef || '';
      })
      .filter(Boolean).join(', ');
  }

}

