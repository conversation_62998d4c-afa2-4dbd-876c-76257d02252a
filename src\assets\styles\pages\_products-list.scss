@use '../utils/mixins' as *;


.title-container .menubar .p-menubar {
  padding: 0;
  background: none;
  border: none;
  color: white;

  p-overlayPanel {
    padding: 1rem 0.2rem !important;
  }

  .align-column {
    display: flex;
    flex-direction: column;
  }

  .section-button {
    gap: 1.5rem;

    .margin-rigth {
      margin-right: 20px;
    }
  }
}


.padding {
  padding-inline: 1rem;
}

.product-list .p-tabview-panels {
  background-color: transparent !important;
}

.cards {

  width: 100%;

  // ✅ Fusion du style de niveau de fidélité avec les anciens styles génériques
  .card-container {
    padding: 3% 0 3% 2%;

    .product-side {
      display: flex;
      justify-content: flex-start;
      row-gap: 4vw;
      column-gap: 4vw;
      flex-wrap: wrap;

      .image-banner {
        @include vertical-horizontal-center;
        flex-direction: column;
        width: 336px;
        height: 165px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
        border-radius: 13px;
        padding: 1.5rem 1rem;

        img {
          width: 98%;
          height: 125px;
          display: flex;
          justify-content: center;
          margin-bottom: 5px;
        }

        label {
          font-weight: 600;
          font-size: 0.8rem;
          line-height: 25px;
          text-align: center;
          color: var(--clr-normal-400);
        }

        .width {
          font-size: 3rem;
          color: var(--clr-primary-500);
          margin-bottom: 10px;
        }

        &:hover {
          @include scale-effect;
        }
      }

      .product {
        position: relative;
        width: 12%;
        // height: 24vh;
        padding: 1.0rem 1rem;
        border-radius: 13px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
        transition: all 0.3s ease;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

          .fidelity-overlay {
            opacity: 1;
          }
        }

        img {
          width: 100%;
          margin-bottom: 5px;
          transition: transform 0.3s ease;
          object-fit: cover;
        }

        &:hover img {
          transform: scale(1.05);
        }

        label {
          font-weight: 600;
          font-size: 0.8rem;
          line-height: 25px;
          text-align: center;
          color: var(--clr-normal-400);
        }

        .width {
          font-size: 3rem;
          color: var(--clr-primary-500);
          margin-bottom: 10px;
        }

        .fidelity-overlay {
          position: absolute;
          top: 8px;
          right: 8px;
          opacity: 0.9;
          transition: opacity 0.3s ease;
          z-index: 2;
        }

        .fidelity-badge {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 6px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
          -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
          animation: fadeInUp 0.4s ease, pulse 2s infinite;

          i {
            color: #ffd700;
            font-size: 0.9rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }

          span {
            font-family: var(--montserrat-medium);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }

          &.elite {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
          }

          &.premium {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
          }

          &.standard {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c3e50;
            box-shadow: 0 4px 12px rgba(168, 237, 234, 0.4);

            i {
              color: #f39c12;
            }
          }
        }

        .fidelity-status {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: #f8f9fa;
          color: #6c757d;
          border: 1px solid #e9ecef;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 500;

          i {
            color: #adb5bd;
            font-size: 0.9rem;
          }

          span {
            font-family: var(--montserrat-medium);
          }
        }
      }

      .product-card-footer {
        margin-top: auto;

        .fidelity-status {
          display: flex;
          align-items: center;
          font-size: 0.5rem;
          color: #999;

          i {
            margin-right: 0.4rem;
            color: gold;
          }
        }
      }
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(15px) scale(0.9);
    }

    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes pulse {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.05);
    }
  }


}

.action {
  justify-content: flex-end !important;
}

.min-heigt-dialog {
  min-height: 40vh;
}

.add-dialog .p-dialog {
  width: 40vw;
  border-radius: 5px;

  .p-dialog-header {
    padding: 8px;
    height: 0;

    .pi-times:before {
      content: none !important;
      color: var(--clr-secondary-400);
    }

    .p-dialog-header-icon {
      width: 0rem;
    }
  }

  .Product-detail {
    .p-card-body {
      padding: 0 !important;
    }
  }

  h2 {
    font-size: 1.5rem;
    font-family: var(--montserrat-semibold);
    margin-block-end: 12px
  }

  .header-button {
    @include vertical-center;
    justify-content: flex-end;
    gap: 0.5rem;

    .size {
      padding: 0.5rem;

      &:hover {
        border-radius: 5px;

        i {
          color: var(--clr-default-400) !important;
        }
      }
    }

    .size:hover {
      background-color: var(--clr-primary-100);
    }

    .disabled:hover {
      background-color: var(--clr-secondary-100);
    }

  }



  .p-card-footer {
    padding: 0;

    .product-button {
      @include vertical-horizontal-center;
      gap: 1rem;
    }

    .button {
      @include vertical-center
    }
  }

  p-messages {
    position: absolute;
    bottom: 72vh;
    width: 33vw;
    left: 68%;
  }
}

.p-inputgroup {
  span {
    cursor: pointer;

    &:hover {
      background-color: var(--clr-primary-500);
      color: var(--clr-default-400);
    }
  }

}

.p-card {
  box-shadow: none !important;
  color: var(--clr-normal-400) !important;
  font-family: var(--montserrat-regular);
  ;


  .p-card-header {
    font-size: 1.3rem;
    font-family: var(--montserrat-semibold);
    padding: 1rem 0 0 1rem;
    @include vertical-horizontal-between;
  }


  .product-form {
    display: flex;
    flex-direction: column;
    margin-block-end: 10px;
    font-size: 1rem;
    gap: 0.5em;
    font-family: var(--montserrat-regular);
    color: var(--clr-normal-400);



    .form-input {
      width: 100%;

      .p-inputnumber {
        width: 100%;
      }
    }

    label {
      font-family: var(--montserrat-medium);
      font-size: 1rem;
      color: var(--clr-normal-400);
    }

    .icon-space {
      display: flex;
      gap: 0.5rem;
      font-size: 1rem;

      span {
        font-family: var(--montserrat-regular);
      }
    }

    .fidelity-info {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 8px;
      padding: 8px 12px;
      background: #e3f2fd;
      color: #1976d2;
      border-radius: 8px;
      font-size: 0.8rem;
      border-left: 3px solid #2196f3;

      i {
        color: #2196f3;
        font-size: 0.9rem;
      }
    }
  }

  .image-banner-form {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 0.5rem;
    height: 10em;
    margin-top: 2em;
    align-items: center;

    .img-size {
      width: 7vw;
    }
  }

  .inline {
    display: block;
    align-items: center;

    label {
      line-height: 17px;
      font-family: var(--montserrat-regular);
      cursor: pointer;
    }

    label:hover {
      color: var(--clr-primary-500);
    }

    p-radioButton {
      margin-right: 0.5rem;
    }
  }


  .product {
    display: flex;
    flex-direction: column;
    font-family: var(--montserrat-semibold);
    cursor: pointer;

    .labelspace {
      text-align: center;
      line-height: 30px;
    }

  }

  .img-size {
    width: 5vw;
    align-self: center;
    cursor: pointer;
  }
}

.first-group,
.second-group {
  width: 80%;
  margin-inline: auto;
}

.confirDisable {
  border: none !important;

  // &:hover {
  //   transform: scale(0.3);
  // }

}

.shippingDialog .p-dialog {
  min-height: 14em;

  .body {
    font-family: var(--montserrat-regular);

    .title {
      @include vertical-center;
      // gap: 16.39rem;
      justify-content: space-between;
      margin-block-end: 1em;

      .title-text {
        font-family: var(--montserrat-semibold);
        font-size: 1.3rem;
        color: var(--clr-normal-400);
      }

      // .positionIcon {
      //   position: absolute;
      //   right: 15px;
      //   top: 4px;
      // }
    }

    .p-card .p-card-body {
      padding: 0;

      .input-select {
        font-family: var(--montserrat-semibold);
      }

    }

    .margin-top {
      margin-block-start: 2em;
    }

    .product-button {
      @include vertical-horizontal-center;
      gap: 1rem;
      margin-block-start: 2em;
    }

  }
}

.color-timer {
  color: var(--clr-primary-400);
  font-family: var(--montserrat-semibold);
  font-size: 1rem;
  margin-block-end: 1em;
  text-align: center;
}