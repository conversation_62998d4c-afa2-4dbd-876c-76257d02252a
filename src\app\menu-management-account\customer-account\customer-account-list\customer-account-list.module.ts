import { PasswordModule } from 'primeng/password';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { SidebarModule } from 'primeng/sidebar';
import { TooltipModule } from 'primeng/tooltip';
import { TabViewModule } from 'primeng/tabview';
import { DropdownModule } from 'primeng/dropdown';
import { PaginatorModule } from 'primeng/paginator';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SharedModule } from 'src/app/shared/shared.module';
import { CustomerAccountListComponent } from './customer-account-list.component';
import { CustomerAccountListRoutingModule } from './customer-account-list-routing.module';
import { CalendarModule } from 'primeng/calendar';



@NgModule({
  declarations: [
    CustomerAccountListComponent
  ],
  imports: [
    FormsModule,
    TableModule,
    ToastModule,
    CommonModule,
    SharedModule,
    ButtonModule,
    DialogModule,
    TabViewModule,
    TooltipModule,
    SidebarModule,
    PasswordModule,
    DropdownModule,
    InputTextModule,
    PaginatorModule,
    AutoCompleteModule,
    OverlayPanelModule,
    ConfirmDialogModule,
    CustomerAccountListRoutingModule,
    CalendarModule,
  ]
})
export class CustomerAccountListModule { }
