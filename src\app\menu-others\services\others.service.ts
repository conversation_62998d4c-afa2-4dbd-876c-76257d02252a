import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { lastValueFrom } from 'rxjs';
import { Faq } from 'src/app/shared/models/faq.model';
import { Feedback } from 'src/app/shared/models/feedback.model';
import { ImageBanner } from 'src/app/shared/models/image-banner.entity';
import { NotificationMessage } from 'src/app/shared/models/notification-type.model';
import { Product } from 'src/app/shared/models/product.model';
import { TechnicalSheet } from 'src/app/shared/models/technical-sheet.model';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { QueryResult } from 'src/app/shared/types';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OthersService {


  url: string
  urls: string
  constructor(
    private http: HttpClient,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService
  ) {
    this.url =
      this.baseUrlService.getOrigin() + environment.basePath;
    this.urls = this.baseUrlService.getOrigin() + environment.basePath;

  }

  async getAllFeedback(param?: any): Promise<{ data: Feedback[]; count: number }> {
    try {
      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { ref, status, email, offset, limit, date, enable = true, projection } = param;
      if (status.code > 0) params = params.append('status', status?.code);
      if (limit) params = params.append('limit', limit);
      if (offset) params = params.append('offset', offset);
      if (ref) params = params.append('ref', ref);
      if (email) params = params.append('user.email', email);
      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      if (projection) { params = params.append('projection', projection); }
      params = params.append('enable', enable);

      return await lastValueFrom(
        this.http.get<{ data: Feedback[]; count: number }>(`${this.url}/feedbacks`, {
          params,
        })
      );
    } catch (error) {
      return error;
    } finally {
      this.commonSrv.isLoading = false;
    }
  }

  async getAllFaq(param?: any): Promise<{ data: Faq[]; count: number }> {
    try {

      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { isLexiqued, offset, limit, date, enable = true } = param;


      if (limit) params = params.append('limit', limit);
      if (offset) params = params.append('offset', offset);
      if (isLexiqued == true || isLexiqued == false) params = params.append('isLexiqued', isLexiqued);

      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      params = params.append('enable', enable);

      return await lastValueFrom(
        this.http.get<{ data: Faq[]; count: number }>(`${this.url}/faq`, {
          params,
        })
      );
    } catch (error) {
      return error;
    } finally {
      this.commonSrv.isLoading = false;
    }
  }


  async getAllImageBanner(param?: any): Promise<{ data: ImageBanner[]; count: number }> {
    try {

      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { level, offset, limit, date, enable = true } = param;


      if (limit) params = params.append('limit', limit);
      if (offset) params = params.append('offset', offset);
      if (level) params = params.append('level', level);

      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      params = params.append('enable', enable);

      return await lastValueFrom(
        this.http.get<{ data: ImageBanner[]; count: number }>(`${this.url}/image-banner`, {
          params,
        })
      );
    } catch (error) {
      return error;
    } finally {
      this.commonSrv.isLoading = false;
    }
  }

  async postImageBanner(faq: ImageBanner): Promise<any> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.post(
          `${this.url}/image-banner`,
          faq,

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async updateImageBanner(_id: string, image: ImageBanner): Promise<QueryResult> {
    try {

      
      return await lastValueFrom(
        this.http.patch(
          `${this.url}/image-banner/${_id}`,
          image,
        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }


  async updateFeedback(feedback: Feedback): Promise<QueryResult> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.patch(
          `${this.url}/feedbacks/${feedback._id}`,
          {},

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async updateFaq(faq: Faq): Promise<QueryResult> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.patch(
          `${this.url}/faq/${faq._id}`,
          faq,

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }


  async claimNotificationMessage(notificationMessage: NotificationMessage): Promise<any> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.post(
          `${this.url}/notifications`,
          notificationMessage,

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async postFaq(faq: Faq): Promise<any> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.post(
          `${this.url}/faq`,
          faq,

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async saveNotification(data: any): Promise<any> {
    try {
      let param = new HttpParams();
      return await lastValueFrom(
        this.http.post(
          `${this.url}/notifications`,
          data,

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async getNotifications(param?: any): Promise<{ data: Faq[]; count: number }> {
    try {

      this.commonSrv.isLoading = true;
      let params = new HttpParams();
      const { offset, limit, date, enable = true } = param;


      if (limit) params = params.append('limit', limit);
      if (offset) params = params.append('offset', offset);

      if (date.start && date.end) { params = params.append('startDate', moment(date.start).format('YYYY-MM-DD')); }
      if (date.end && date.start) { params = params.append('endDate', moment(date.end).format('YYYY-MM-DD')); }
      params = params.append('enable', enable);
      params = params.append('isGeneralNotif', true);

      return await lastValueFrom(
        this.http.get<{ data: Faq[]; count: number }>(`${this.url}/notifications`, {
          params,
        })
      );
    } catch (error) {
      return error;
    } finally {
      this.commonSrv.isLoading = false;
    }
  }

  async sendNotificationToCustomers(data: any): Promise<QueryResult> {
    try {
      return await lastValueFrom(
        this.http.post(
          `${this.url}/notifications/users`,
          data

        )
      );
    } catch (error) {
      return this.commonSrv.getError("Une erreur s'est produite", error);
    }
  }

  async getTechnical(query: any): Promise<{ data: TechnicalSheet[], count: number }> {
    let params = new HttpParams();
    if (query) {
      const { offset, limit, erpRef, label, labelCateg, enable = true, created_at, projection } = query;
      if (offset !== undefined || null) { params = params.append('offset', offset); }
      if (limit) { params = params.append('limit', limit); }
      if (erpRef) { params = params.append('product.erpRef', erpRef); }
      if (label) { params = params.append('product.label', label); }
      if (labelCateg) { params = params.append('category.label', labelCateg); }
      if (projection) { params = params.append('projection', projection); }
      if (created_at) { params = params.append('created_at', created_at); }
      params = params.append('enable', enable);
    }

    try {
      return await lastValueFrom(
        this.http.get<{ data: TechnicalSheet[], count: number }>(`${this.url}/technical-sheet`, {
          params,
          headers: { Authorization: `Bearer ${this.commonSrv?.user?.accessToken}` },
        },));
    } catch (error) {
      return error;
    }
  }

  async find(idProduct: string) {
    try {
      return await lastValueFrom(
        this.http.get<any>(`${this.url}/${idProduct}products`, {
          headers: {
            Authorization: `Bearer ${this.commonSrv?.user?.accessToken}`,
          },
        })
      );
    } catch (error) {
      return error;
    }
  }

  async createTechnical(product: TechnicalSheet): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.post<QueryResult>(`${this.url}/technical-sheet`, product,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de création du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }
  }

  async updateTechnical(technical: TechnicalSheet): Promise<QueryResult> {
    try {
      this.commonSrv.isLoading = true;
      return await lastValueFrom(this.http.patch<QueryResult>(`${this.url}/technical-sheet/${technical._id}`, technical,
        { headers: { 'Authorization': `Bearer ${this.commonSrv?.user?.accessToken}` } }));
    } catch (error) {
      return await this.commonSrv.getError(
        'Echec de création du produit',
        error
      );
    } finally { this.commonSrv.isLoading = false; }
  }

}
