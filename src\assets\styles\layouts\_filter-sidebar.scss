@use '../utils/mixins' as *;

.p-sidebar .p-sidebar-header+.p-sidebar-content {
  height: 100%;

  .filter-sidebar {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-filter {
      padding: 16px;
      @include vertical-horizontal-between;
      font-weight: 400;
      border-bottom: 1px solid var(--border-color);

      &__reset {
        cursor: pointer;
        padding: 0.7em 1em;
        border-radius: 5px;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: var(--clr-tertiary-100);
        }
      }
    }

    .body-filter {
      flex: 1;
      overflow-y: auto;

      form {
        display: flex;
        flex-direction: column;
        height: 90%; // Take full height of the parent
      }

      .input-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 500;
        }
      }

      .p-calendar .p-datepicker {
        max-width: 110%;
        top: 0 !important;

        table td {
          padding: 0 !important;
        }
      }
    }

    .footer-filter {
      @include vertical-horizontal-center;

      display: flex;
      gap: 1rem;
      margin-top: auto; // Push the footer to the bottom
      column-gap: 1.5em;
      bottom: 0px;
      left: 16px;
      width: 100%;
    }


  }

}


.p-sidebar-header {
  justify-content: flex-start !important;
}

.footer-export {
  display: flex;
  justify-content: center;
  gap: 21px;
  margin-top: 2rem;
}