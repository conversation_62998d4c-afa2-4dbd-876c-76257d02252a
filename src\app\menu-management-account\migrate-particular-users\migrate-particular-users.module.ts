import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';

// PrimeNG Modules
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule } from 'primeng/paginator';

// Components
import { MigrateParticularUsersComponent } from './migrate-particular-users.component';

const routes: Routes = [
  {
    path: '',
    component: MigrateParticularUsersComponent,
  },
];

@NgModule({
  declarations: [MigrateParticularUsersComponent],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes),
    TableModule,
    DropdownModule,
    CheckboxModule,
    ButtonModule,
    ConfirmDialogModule,
    ToastModule,
    ProgressBarModule,
    DialogModule,
    InputTextModule,
    PaginatorModule,
  ],
})
export class MigrateParticularUsersModule {} 