import { Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { CommonService } from '../../services/common.service';
import { setLanguage } from '../../functions/global.function';
import { EmployeeType } from '../../enums/user-category.enum';
import { UserAction } from '../../actions/user-authorizations.action';
import { StoreAction } from '../../actions/store-authorization.action';
import { PriceAction } from '../../actions/price.authorization.action';
import { StorageService } from 'src/app/shared/services/storage.service';
import { ProductAction } from '../../actions/product-authorization.action';
import { ShippingAction } from '../../actions/shipping-authorization.action';
import { ErpItemIdAction } from '../../actions/erpItemId-authorization.action';
import { OrderAction } from 'src/app/shared/actions/order-authorization.action';
import { PlanificationAction } from '../../actions/planification-authorization.actions';
import { ReloadBalanceAction } from '../../actions/reload-balance-authorization.action';
import { CompanyAction } from '../../actions/company-authorization.actions';
import { TechnicalSheetAction } from '../../actions/technical.action';
import { FeedbackAction } from '../../actions/feedback.action';
import { FaqAction } from '../../actions/faq.action';
import { LogoAction, OptionAction } from '../../actions';

@Component({
  selector: 'mcw-header',
  templateUrl: './header.component.html',
  styles: [
  ]
})
export class HeaderComponent implements OnInit {


  constructor(
    private route: Router,
    public commonSrv: CommonService,
    private storageSrv: StorageService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) {
    storageSrv.getUserConnected();

  }

  showSignAuthModal: boolean;
  orderAction = OrderAction;
  items: MenuItem[];
  currrentLang: string;

  ngOnInit(): void {
    this.storageSrv.getUserConnected();

    const itemsFr: MenuItem[] = [
      {
        label: 'Reporting',
        icon: 'pi pi-fw pi-chart-bar',
        routerLink: 'reporting/orders',
        // items: [
        //   {
        //     label: 'Commandes', routerLink: 'reporting/orders', icon: 'pi pi-fw pi-chart-line',
        //     visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
        //   },
        //   // { label: 'Employés', routerLink: 'account/cimencam-account', icon: 'pi pi-fw pi-chart-pie' },
        //   {
        //     label: 'Paiements', routerLink: 'reporting/payment', icon: 'pi pi-fw pi-credit-card',
        //     visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
        //   },

        //   {
        //     label: 'Recharge de compte', routerLink: 'reporting/reload-balance', icon: 'pi pi-fw pi-credit-card',
        //     visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
        //   },

        //   { label: 'Employés', routerLink: 'reporting/employees', icon: 'pi pi-fw pi-credit-card' },
        // ],
      },
      {
        label: 'Commandes',
        icon: 'pi pi-fw pi-history',
        visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW),
        items: [
          {
            label: 'Clients', routerLink: 'history', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_CUSTOMERS) && ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
          },
          // {
          //   label: 'Employées', routerLink: 'history/orders-employee', icon: 'pi pi-fw pi-align-justify',
          //   visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_EMPLOYEES)
          // },
          {
            label: 'Renvoyées', routerLink: 'history/low-balance', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_CUSTOMERS) && ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)

          },
          {
            label: 'Annulées', routerLink: 'history/cancel-order', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_CUSTOMERS) && ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)

          }
        ]
      },
      {
        label: 'Market place',
        icon: 'pi pi-fw pi-th-large',
        items: [
          {
            label: 'Gérer la marketplace', routerLink: '/market-place', icon: 'pi pi-fw pi-cog',
            // visible: this.commonSrv?.user?.authorizations?.includes(OrderAction.VIEW),
          },
          {
            label: 'Commandes', routerLink: '/market-place/orders', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          { label: 'Reporting', routerLink: '/market-place/reporting', icon: 'pi pi-fw pi-chart-bar' },

        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },
      // {
      //   label: 'Gestion Bon CMD',
      //   icon: 'pi pi-fw pi-chart-bar',
      //   routerLink: 'planing/deliveries',
      //   // items: [
      //   //   { label: 'Livraisons', routerLink: 'planing/deliveries', icon: 'pi pi-fw pi-chart-line' },
      //   //   { label: 'Liste des AEs', routerLink: 'planing/list-of-aes', icon: 'pi pi-fw pi-credit-card' },
      //   //   { label: 'Reporting des AEs', routerLink: 'planing/reporting', icon: 'pi pi-fw pi-chart-bar' },
      //   //   { label: 'Reporting des BL', routerLink: 'planing/reporting-bl', icon: 'pi pi-fw pi-chart-bar' },
      //   // ],
      //   visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      // },
      {
        label: 'Comptes',
        icon: 'pi pi-fw pi-user-edit',
        visible: this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW)
          || this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
        items: [
          {
            label: 'Particuliers', routerLink: 'account', icon: 'pi pi-fw pi-id-card',
            visible: this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW),
          },
          {
            label: 'Employées', routerLink: 'account/pasta-account', icon: 'pi pi-fw pi-user',
            visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
              && this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW),

          },
          {
            label: 'Clients', routerLink: 'account/companie-account', icon: 'pi pi-building  pi-fw',
            visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
              && this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          {
            label: 'Utilisateur compagnies', routerLink: 'account/companie-account/user-company',
            icon: 'pi pi-fw pi-users',
            visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
          },
          {
            label: 'Logos Client', routerLink: 'account/logo-client', icon: 'pi pi-building  pi-fw',
            visible: this.commonSrv?.user?.authorizations?.includes(LogoAction.VIEW),
          },
        ]
      },
      {
        label: 'Administration',
        icon: 'pi pi-fw pi-cog',
        items: [
          {
            label: 'Produits', routerLink: 'admin', icon: 'pi pi-fw pi-shopping-bag',
            visible: this.commonSrv?.user?.authorizations?.includes(ProductAction.VIEW),
          },
          {
            label: 'Point de vente', routerLink: '/admin/store', icon: 'pi pi-fw pi-sitemap',
            visible: this.commonSrv?.user?.authorizations?.includes(StoreAction.VIEW),
          },
          {
            label: 'Offre de prix', routerLink: '/admin/price', icon: 'pi pi-fw pi-money-bill',
            visible: this.commonSrv?.user?.authorizations?.includes(PriceAction.VIEW),
          },
          {
            label: 'Ids JDE des produits', routerLink: '/admin/itemId', icon: 'pi pi-fw pi-th-large',
            visible: this.commonSrv?.user?.authorizations?.includes(ErpItemIdAction.VIEW),
          },
          {
            label: 'Destinations', routerLink: '/admin/shippings', icon: 'pi pi-fw pi-tags',
            visible: this.commonSrv?.user?.authorizations?.includes(ShippingAction.VIEW),
          },
          {
            label: 'Planifications', routerLink: '/admin/planifications', icon: 'pi pi-fw pi-car',
            visible: this.commonSrv?.user?.authorizations?.includes(PlanificationAction.VIEW),
          },
          {
            label: 'Gestion des Options', routerLink: '/admin/options', icon: 'pi pi-fw pi-th-large',
            visible: this.commonSrv?.user?.authorizations?.includes(OptionAction.VIEW),
          },
          {
            label: 'Gestion QR Codes', routerLink: '/admin/qr-codes', icon: 'pi pi-fw pi-qrcode',
            // visible: this.commonSrv?.user?.authorizations?.includes(OptionAction.VIEW),
          },
          // {
          //   label: 'Recharge de compte', routerLink: '/admin/reload-balance', icon: 'pi pi-fw pi-th-large',
          //   visible: this.commonSrv?.user?.authorizations?.includes(ReloadBalanceAction.SEE_RELOAD_HISTORY)
          // },
          // {
          //   label: 'Adresses de livraisons', routerLink: '/admin/location', icon: 'pi pi-fw pi-th-large',
          //   visible: this.commonSrv?.user?.authorizations?.includes(LocationAction.VIEW),
          // },

        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },
      {
        label: 'Programme fidélité',
        icon: 'pi pi-fw pi-th-large',
        items: [
          {
            label: 'Liste des commandes', routerLink: '/loyalityProgram', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(OrderAction.VIEW),
          },
          {
            label: 'Liste des distributeurs', routerLink: '/loyalityProgram/distributorlist', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          {
            label: 'Evolution des points', routerLink: '/loyalityProgram/points', icon: 'pi pi-fw pi-align-justify',
          },


          { label: 'Reporting', routerLink: '/loyalityProgram/reporting', icon: 'pi pi-fw pi-chart-bar' },

        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },
      {
        label: 'Autres',
        icon: 'pi pi-fw pi-tags',
        items: [
          {
            label: 'FAQ', icon: 'pi pi-fw pi-question', routerLink: 'others/faq',
            visible: this.commonSrv?.user?.authorizations?.includes(FaqAction.VIEW)
          },
          {
            label: 'Fiche technique', routerLink: '/others/technical', icon: 'pi pi-fw pi-file-pdf',
            visible: this.commonSrv?.user?.authorizations?.includes(TechnicalSheetAction.VIEW),
          },
          // { label: 'Lexique', icon: 'pi pi-sort-alpha-down', routerLink: 'others/lexique' },
          { label: 'Condition générale', icon: 'pi pi-fw pi-question-circle' },
          {
            label: 'Réclamations', icon: 'pi pi-fw pi-question-circle', routerLink: 'others',
            visible: this.commonSrv?.user?.authorizations?.includes(FeedbackAction.VIEW)
          },
          { label: 'Gestion des images', icon: 'pi pi-fw pi-question-circle', routerLink: '/others/images' },
          { label: 'Gestion des Notifications', icon: 'pi pi-fw pi-send', routerLink: '/others/notifications' }
        ]
      },
    ];

    const itemsEn: MenuItem[] = [
      {
        label: 'Reporting',
        icon: 'pi pi-fw pi-chart-bar',
        routerLink: 'reporting/orders'
      },
      {
        label: 'Orders',
        icon: 'pi pi-fw pi-history',
        items: [
          {
            label: 'Customers', routerLink: 'history', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_CUSTOMERS) && ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
          },
          // {
          //   label: 'Employees', routerLink: 'history/orders-employee', icon: 'pi pi-fw pi-align-justify',
          //   visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VIEW_EMPLOYEES)
          // },
          {
            label: 'Returned', routerLink: 'history/low-balance', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(this.orderAction.VALIDATE_LOW_BALANCE) && ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
          }
        ]
      },
      {
        label: 'Market place',
        icon: 'pi pi-fw pi-th-large',
        items: [
          {
            label: 'Management market', routerLink: '/market-place', icon: 'pi pi-fw pi-cog',
            // visible: this.commonSrv?.user?.authorizations?.includes(OrderAction.VIEW),
          },
          {
            label: 'Orders', routerLink: '/market-place/orders', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          { label: 'Reporting', routerLink: '/market-place/reporting', icon: 'pi pi-fw pi-chart-bar' },

        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },
      // {
      //   label: 'Aes manage',
      //   icon: 'pi pi-fw pi-car',
      //   routerLink: 'planing/deliveries',
      //   // items: [
      //   //   { label: 'Deliveries', routerLink: 'planing/deliveries', icon: 'pi pi-fw pi-align-justify' },
      //   //   { label: 'List of AEs', routerLink: 'planing/list-of-aes', icon: 'pi pi-fw pi-align-justify' },
      //   //   { label: 'Reporting of AEs', routerLink: 'planing/reporting', icon: 'pi pi-fw pi-chart-line' },
      //   //   { label: 'Reporting of BL', routerLink: 'planing/reporting-bl', icon: 'pi pi-fw pi-chart-bar' },

      //   // ],
      //   visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      // },
      {
        label: 'Account',
        icon: 'pi pi-fw pi-user-edit',
        visible: this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW)
          || this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
        items: [
          {
            label: 'Customers', routerLink: 'account', icon: 'pi pi-fw pi-id-card',
            visible: this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW),
          },
          {
            label: 'Employee', routerLink: 'account/pasta-account', icon: 'pi pi-fw pi-user',
            visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
              && this.commonSrv?.user?.authorizations?.includes(UserAction.VIEW),
          },
          {
            label: 'Companies', routerLink: 'account/companie-account', icon: 'pi pi-fw pi-users',
            visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
              && this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          {
            label: 'Logos Client', routerLink: 'account/logo-client', icon: 'pi pi-building  pi-fw',
            visible: this.commonSrv?.user?.authorizations?.includes(LogoAction.VIEW),
          },
        ]
      },
      {
        label: 'Administration',
        icon: 'pi pi-fw pi-cog',
        items: [
          {
            label: 'Products', routerLink: 'admin', icon: 'pi pi-fw pi-shopping-bag',
            visible: this.commonSrv?.user?.authorizations?.includes(ProductAction.VIEW),
          },
          {
            label: 'Stores', routerLink: '/admin/store', icon: 'pi pi-fw pi-sitemap',
            visible: this.commonSrv?.user?.authorizations?.includes(StoreAction.VIEW),
          },
          {
            label: 'Price offers', routerLink: '/admin/price', icon: 'pi pi-fw pi-money-bill',
            visible: this.commonSrv?.user?.authorizations?.includes(PriceAction.VIEW),
          },
          {
            label: 'JDE products IDs', routerLink: '/admin/itemId', icon: 'pi pi-fw pi-th-large',
            visible: this.commonSrv?.user?.authorizations?.includes(ErpItemIdAction.VIEW),
          },
          {
            label: 'Delivery addresses', routerLink: '/admin/shippings', icon: 'pi pi-fw pi-tags',
            visible: this.commonSrv?.user?.authorizations?.includes(ShippingAction.VIEW),
          },
          {
            label: 'Planifications', routerLink: '/admin/planifications', icon: 'pi pi-fw pi-car',
            visible: this.commonSrv?.user?.authorizations?.includes(PlanificationAction.VIEW),
          },
          {
            label: 'Gestion des Options', routerLink: '/admin/options', icon: 'pi pi-fw pi-th-large',
            visible: this.commonSrv?.user?.authorizations?.includes(OptionAction.VIEW),
          },
          { label: 'Promo Code', routerLink: '/admin/promo-code', icon: 'pi pi-fw pi-th-large' },
          {
            label: 'Reload Balance', routerLink: '/admin/reload-balance', icon: 'pi pi-fw pi-th-large',
            visible: this.commonSrv?.user?.authorizations?.includes(ReloadBalanceAction.SEE_RELOAD_HISTORY)
          },
          {
            label: 'Manage QR Codes', routerLink: '/admin/qr-codes', icon: 'pi pi-fw pi-qrcode',
            // visible: this.commonSrv?.user?.authorizations?.includes(OptionAction.VIEW),
          },
          // {
          //   label: 'Locations', routerLink: '/admin/location', icon: 'pi pi-fw pi-th-large',
          //   visible: this.commonSrv?.user?.authorizations?.includes(LocationAction.VIEW),
          // },
        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },
      {
        label: 'Loyalty program',
        icon: 'pi pi-fw pi-th-large',
        items: [
          {
            label: 'Order list', routerLink: '/loyalityProgram', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(OrderAction.VIEW),
          },
          {
            label: 'WholeSaler list', routerLink: '/loyalityProgram/distributorlist', icon: 'pi pi-fw pi-align-justify',
            visible: this.commonSrv?.user?.authorizations?.includes(CompanyAction.VIEW),
          },
          {
            label: 'Points evolution', routerLink: '/loyalityProgram/points', icon: 'pi pi-fw pi-align-justify',
          },
          { label: 'Reporting', routerLink: '/loyalityProgram/reporting', icon: 'pi pi-fw pi-chart-bar' },

        ],
        visible: ![EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonSrv?.user?.employeeType)
      },

      {
        label: 'Others',
        icon: 'pi pi-fw pi-tags',
        items: [
          {
            label: 'FAQ', icon: 'pi pi-fw pi-question', routerLink: 'others/faq',
            visible: this.commonSrv?.user?.authorizations?.includes(FaqAction.VIEW)
          },
          {
            label: 'Data sheet', routerLink: '/others/technical', icon: 'pi pi-fw pi-file-pdf',
            visible: this.commonSrv?.user?.authorizations?.includes(TechnicalSheetAction.VIEW),
          },
          // { label: 'Glossary', icon: 'pi pi-sort-alpha-down', routerLink: 'others/lexique' },
          { label: 'General condition', icon: 'pi pi-fw pi-question-circle' },
          {
            label: 'Complaint', icon: 'pi pi-fw pi-question-circle', routerLink: 'others',
            visible: this.commonSrv?.user?.authorizations?.includes(FeedbackAction.VIEW)

          },
          { label: 'Management images', icon: 'pi pi-fw pi-question-circle', routerLink: '/others/images' },


        ]
      },
    ];

    this.items = (this.baseHref?.replace(/\//g, '') === 'en') ? itemsEn : itemsFr;
    this.currrentLang = this.baseHref?.replace(/\//g, '');
  }

  signOut(): void {
    this.storageSrv.clearStorage();
    this.route.navigate(['']);
    this.commonSrv.user = this.storageSrv.getUserConnected();
    this.showSignAuthModal = false;
  }

  goTo(lang: any) {
    setLanguage(lang);
    window.location.href = document.location.origin + (lang == 'en' ? '/en' : '');
  }

  getLanguageStyle(lang: 'fr' | 'gb') {
    const img = (lang === 'fr') ? 'assets/icons/fr.svg' : 'assets/icons/gb.svg';
    return { 'background-image': `url('${img}')`, };
  }

}
