import {
  HttpInterceptor,
  HttpRequest,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpEvent,
} from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, from } from 'rxjs';
import { CommonService } from 'src/app/shared/services/common.service';
import { APP_BASE_HREF } from '@angular/common';

@Injectable()
export class RequestInterceptor implements HttpInterceptor {
  constructor(
    private commonSrv: CommonService,
    @Inject(APP_BASE_HREF) private baseHref:string
  ) { }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>andler): Observable<HttpEvent<any>> {

    // Allow CORS for all
    let headers = req.headers.set('Accept-Language', this.baseHref?.replace(/\//g, ''));
    // Add authorization token in header
    if (!req.headers.has('Authorization')) {
      headers = headers.set(
        'Authorization',
        `Bearer ${this.commonSrv?.user?.accessToken}`);
    }


    return next.handle(req.clone({ headers }));
  }
}
