import { Component, Inject, OnInit } from '@angular/core';
import { CommonService } from 'src/app/shared/services/common.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { t } from 'src/app/shared/functions/global.function';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { APP_BASE_HREF } from '@angular/common';
import { NgxImageCompressService } from 'ngx-image-compress';
import { OthersService } from '../services/others.service';
import * as moment from 'moment';
import { ImageBanner, LevelBanner } from 'src/app/shared/models/image-banner.entity';

@Component({
  selector: 'mcw-images-management',
  templateUrl: './images-management.component.html',
  styles: [
  ],
  providers: [MessageService, ConfirmationService]

})
export class ImagesManagementComponent implements OnInit {

  isLoading: boolean;
  selectedImage: ImageBanner = new ImageBanner();
  display: boolean = false;
  isEdit: boolean;
  statusAccount = [
    { name: 'Actif', code: true, },
    { name: 'Inactif', code: false, },
  ];
  statusAccountEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ];


  levelBanner = [
    { name: 'Banniere home de niveau 1', code: LevelBanner.HOME_1 },
    { name: 'Banniere home de niveau 2', code: LevelBanner.HOME_2 },
    { name: 'Banniere marketPlace de niveau 1', code: LevelBanner.STORE_1 },
    { name: 'Banniere marketPlace de niveau 2', code: LevelBanner.STORE_2 },
    { name: 'Banniere marketPlace de niveau 3', code: LevelBanner.STORE_3 },
    { name: 'Banniere home indirect de niveau 1', code: LevelBanner.BANNER_HOME_2_LEVEL_1 },
    { name: 'Banniere home indirect de niveau 2x', code: LevelBanner.BANNER_HOME_2_LEVEL_2 },

  ];

  filterForm = {
    level: '',
    enable: true,
    created_at: "",
    commercialRegion: [],
    date: { start: moment().startOf('year'), end: moment().endOf('year') },
  };

  images: ImageBanner[] = [];
  showSideBar: boolean;
  offset = 0;
  limit = 50;
  total = 0;
  isImage: boolean;
  isDetail: boolean = true;
  deleterImageModal: boolean;
  title: string;
  index: number = 0;
  language = this.baseHref?.replace(/\//g, '');
  isLoadingInModal: boolean;
  currentImageBanner: any;
  update: boolean = false;
  allCommercialRegions: { label: string, value: string }[] = [];
  commercialRegions: { label: string, value: string }[] = [];
  selectedRegions: string[] = [];

  constructor(

    public commonSrv: CommonService,
    public ortherSrv: OthersService,
    private messageService: MessageService,
    private imageCompress: NgxImageCompressService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    this.statusAccount = (this.language === 'en') ? this.statusAccountEn : this.statusAccount;
    await this.getCommercialRegions();
    await this.getImage();
  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getImage();
  }

  async getImage() {
    this.isLoading = true;
    const query = {
      offset: this.offset,
      limit: this.limit,
      ...this.filterForm,
    };
    try {
      const res = await this.ortherSrv.getAllImageBanner(query);
      if (res instanceof HttpErrorResponse) {
        this.messageService.add({ severity: 'error', summary: `${await t('ACTION_FAILED')}`, detail: res?.error?.message });
        return;
      }
      this.total = res.count;
      this.images = res.data;
    } catch (error) {
      this.messageService.add({ severity: 'error', summary: `${await t('ACTION_FAILED')}`, detail: 'Error loading images' });
    } finally {
      this.isLoading = false;
    }
  }

  openDeleteimageModal(position: string) {
    this.deleterImageModal = true;
    this.display = false;
  }

  openAddImageModal() {
    this.selectedImage = new ImageBanner();
    this.display = true;
    this.isDetail = false;
    this.isEdit = true;
  }

  showUpdateProduct(image: ImageBanner) {
    this.selectedImage = new ImageBanner();
    this.selectedImage = image;
    this.selectedRegions = image.commercialRegion || [];
    this.isEdit = true;
    this.update = true;
  }

  reset() {
    this.selectedImage = new ImageBanner();
    this.selectedRegions = [];
    this.offset = 0;
    this.filterForm = {
      level: '',
      enable: true,
      created_at: "",
      commercialRegion: [],
      date: {
        start: moment().startOf('year'),
        end: moment().endOf('year')
      }
    };
    this.getImage();
  }

  async addImage(): Promise<boolean> {
    this.isLoadingInModal = true;
    if (!this.selectedImage.image) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('DATA_ERROR')}`,
        detail: `${await t('IMG_IMPORT')}`
      });
      return this.isLoadingInModal = false;
    }

    const imageData = {
      ...this.selectedImage,
      commercialRegion: this.selectedRegions || [],
      redirectUrl: this.selectedImage?.redirectUrl || '',
    };

    const res = await this.ortherSrv.postImageBanner(imageData);
    if (res.status == HttpStatusCode.Ok) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('CREATE_PRODUCT')}`,
        detail: res.message
      });
      this.display = false;
      this.isImage = false;
      await this.getImage();
      this.selectedImage = new ImageBanner();
      this.selectedRegions = [];
    } else {
      this.messageService.add({
        severity: 'error',
        summary: res.data,
        detail: res.message
      });
    }
    return this.isLoadingInModal = false;
  }


  async updateImage(): Promise<boolean> {
    this.isLoadingInModal = true;
    const option = {
      ...this.selectedImage,
      level: this.filterForm.level,
      commercialRegion: this.selectedRegions || []
    };
    const { _id } = option

    delete option?.created_at;
    delete option?._id;

    const res = await this.ortherSrv.updateImageBanner(_id, option,);
    if (res.status == HttpStatusCode.Ok) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('CREATE_PRODUCT')}`,
        detail: res.message
      });
      this.update = false;
      await this.reset();
      this.selectedImage = new ImageBanner();
    } else {
      this.messageService.add({
        severity: 'error',
        summary: res.data,
        detail: res.message
      });
    }
    return this.isLoadingInModal = false;
  }

  async deleteImage(): Promise<boolean> {
    this.isLoadingInModal = true;
    const option = {
      ...this.selectedImage,
      enable: false

    }
    const { _id } = option;
    delete option?.created_at;
    delete option?._id;

    const res = await this.ortherSrv.updateImageBanner(_id, option);
    if (res.status == HttpStatusCode.Ok) {
      this.messageService.add({ severity: 'success', summary: `${await t('CREATE_PRODUCT')}`, detail: res.message });
      this.update = false;
      await this.getImage();
      this.selectedImage = new ImageBanner();
    } else {
      this.messageService.add({ severity: 'error', summary: res.data, detail: res.message });
    }
    return this.isLoadingInModal = false;
  }

  async getCommercialRegions() {
    const keyForFilters = ['address.commercialRegion'];
    const companiesFiltersOptions = await this.commonSrv.getElementForFilterByKeys('companies', { keyForFilters });

    if (companiesFiltersOptions instanceof HttpErrorResponse) {
      return this.messageService.add({
        severity: 'error',
        summary: `${await t('ACTION_FAILED')}`,
        detail: companiesFiltersOptions?.error?.message
      });
    }

    this.allCommercialRegions = companiesFiltersOptions['dataaddress.commercialRegion']?.map(commercialRegion => ({
      label: commercialRegion.label,
      value: commercialRegion.label
    }));

    if (this.allCommercialRegions?.length > 0) {
      this.commercialRegions = [...this.allCommercialRegions];
      this.selectedRegions = [];
    }
  }

  compressFile(): void {
    this.imageCompress.uploadFile().then(({ image, orientation }): any => {
      this.imageCompress.compressFile(image, orientation, 50, 50).then(async (result: any) => {
        this.selectedImage.image = result;
      });
    });
    this.isImage = true;
  }

  close() {
    this.display = false;
    this.selectedImage = new ImageBanner();
  }

}
