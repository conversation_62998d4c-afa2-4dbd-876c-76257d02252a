import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'customer',
    pathMatch: 'full',
  },
  {
    path: 'customer',
    loadChildren: () =>
      import('./customer-account/customer-account.module').then((m) => m.CustomerAccountModule),
  },
  {
    path: 'companie-account',
    loadChildren: () =>
      import('./companie-account/companie-account.module').then((m) => m.CompanieAccountModule),
  },
  {
    path: 'pasta-account',
    loadChildren: () =>
      import('./cimencam-account/cimencam-account.module').then((m) => m.CimencamAccountModule),
  },
  {
    path: 'logo-client',
    loadChildren: () =>
      import('./logo-client/logo-client.module').then((m) => m.LogoClientModule),
  },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MenuManagementAccountRoutingModule { }
