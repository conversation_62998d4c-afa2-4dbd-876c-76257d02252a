import * as moment from 'moment';
import { Subscription, interval } from 'rxjs';
import { APP_BASE_HREF } from '@angular/common';
import { DialogService } from 'primeng/dynamicdialog';
import { Component, Inject, OnInit } from '@angular/core';
import { t } from 'src/app/shared/functions/global.function';
import { MenuHistoryService } from '../menu-history.service';
import { Removal } from 'src/app/shared/models/removal.model';
import { RenderType } from 'src/app/shared/enums/render-type';
import { JdeService } from 'src/app/shared/services/jde.service';
import { ConfirmationService, MessageService, SelectItem } from 'primeng/api';
import { TranslatePipe } from 'src/app/shared/pipes/translate.pipe';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserCategory } from 'src/app/shared/enums/user-category.enum';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { Order, OrderStatus, PaymentMode } from 'src/app/shared/models/order';
import { OrderAction } from 'src/app/shared/actions/order-authorization.action';
import { CompanyCategory } from 'src/app/shared/enums/Company-category.enum';
import { QueryFilter } from 'src/app/shared/types';
import { Packaging } from 'src/app/shared/models/packaging.model';
import { PricesService } from 'src/app/menu-administration/management-prices/prices.service';
import { User } from 'src/app/shared/models/user.models';
import { ReportingAction } from 'src/app/shared/actions';
import { OrderCancellationRequestStatus } from 'src/app/shared/enums/orderCancel.enum';

@Component({
  selector: 'mcw-history-orders',
  templateUrl: './history-orders.component.html',
  styles: [
  ],
  providers: [MessageService, ConfirmationService, DialogService, TranslatePipe]
})
export class HistoryOrdersComponent implements OnInit {

  showSideBar: boolean;
  offset = 0;
  limit = 50;
  total = 0;
  totalAmount = 0;
  totalOrders = 0;
  totalTonnes = 0;
  modalReset: boolean;
  showDialogDetail: boolean;
  showDialogExport: boolean;
  user: User;
  reportingAction = ReportingAction;
  orderCancelEnum = OrderCancellationRequestStatus;


  modalDetailRemoval: boolean = false;
  subscription: Subscription;

  orders: Order[];
  dataOrder: any;
  order: Order;
  removal: Removal;
  filteredItems: any;
  orderNumbers: any;
  OrderStatus = OrderStatus;
  packagings: any[] = [];
  customerPackaging: any[] = [];
  offerPriceAmount: number[] = [];


  statusDocument = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ];

  statusDocumentEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ];

  statusPaiement = [
    { name: 'CREDIT', code: PaymentMode.CREDIT },
    { name: 'ORANGE MONEY', code: PaymentMode.ORANGE_MONEY },
    { name: 'MOBILE MONEY', code: PaymentMode.MOBILE_MONEY },
    { name: 'ACCOUNT', code: PaymentMode.MY_ACCOUNT },
    { name: 'EXPRESS UNION', code: PaymentMode.EXPRESS_EXCHANGE },
    { name: 'AFRILAND', code: PaymentMode.AFRILAND },
  ]

  renderType = [
    { name: 'RENDU', code: RenderType.RENDU, },
    { name: 'PICKUP', code: RenderType.PICKUP, },
  ];
  statusOrder = [
    { name: 'Créer', code: OrderStatus.CREATED, },
    { name: 'Crédit en validation', code: OrderStatus.CREDIT_IN_VALIDATION, },
    { name: 'Crédit pour solde insuffisant', code: OrderStatus.CREDIT_IN_AWAIT_VALIDATION, },
    { name: 'Crédit réjéter', code: OrderStatus.CREDIT_REJECTED, },
    { name: 'Annuler', code: OrderStatus.CANCELLED, },
    { name: 'Payé', code: OrderStatus.PAID, },
    { name: 'Réjéter', code: OrderStatus.CREDIT_REJECTED, },
    { name: 'Échouer', code: OrderStatus.FAILD, },
    { name: 'Valider', code: OrderStatus.VALIDATED, },
  ];

  statusOrderEn = [
    { name: 'Create', code: OrderStatus.CREATED, },
    { name: 'Credit in validation', code: OrderStatus.CREDIT_IN_VALIDATION, },
    { name: 'Credit for low balance', code: OrderStatus.CREDIT_IN_AWAIT_VALIDATION, },
    { name: 'Rejected credit', code: OrderStatus.CREDIT_REJECTED, },
    { name: 'Cancelled', code: OrderStatus.CANCELLED, },
    { name: 'Paid', code: OrderStatus.PAID, },
    { name: 'Failed', code: OrderStatus.FAILD, },
    { name: 'Rejected', code: OrderStatus.CREDIT_REJECTED, },
    { name: 'Validated', code: OrderStatus.VALIDATED, },
  ];

  categoriesUsers = [
    { name: 'Particulier', code: UserCategory.Particular },
    { name: 'Employé(e)', code: UserCategory.EmployeeEntity },
    { name: 'Utilisateurs compagnie', code: UserCategory.CompanyUser },
  ]

  filterForm = {
    status: `{"$in": [${OrderStatus.CREATED}, ${OrderStatus.PAID}, ${OrderStatus.VALIDATED}, ${OrderStatus.CREDIT_REJECTED}]}`,
    appReference: '',
    date: { start: moment().startOf('year'), end: moment().endOf('year') },
    userCategory: null,
    renderType: '',
    paymentMode: null,
    customer: '',
    product: '',
    region: '',
    enable: true,
  };

  region = [
    { name: 'LSO', code: 'LSO' },
    { name: 'GNO', code: 'GNO' },
    { name: 'GNO 1', code: 'GNO 1' },
    { name: 'GNO 2', code: 'GNO 2' },
    { name: 'CS', code: 'CS' },
    { name: 'ONO', code: 'ONO' },
  ];

  rejectionOptions: SelectItem[];
  selectedRejectionOption: string;
  showCustomReasonInput: boolean;
  filterFormExport = {
    status: '',
    appReference: '',
    date: { start: moment().startOf('year'), end: moment().endOf('year') },
    userCategory: null,
    renderType: '',
    paymentMode: null,
    customer: '',
    product: '',
    erpSoldToId: '',
    enable: true,
    limit: 10000
  };
  language = this.baseHref?.replace(/\//g, '');

  orderAction = OrderAction;
  totalTonnage: number = 0;
  totalPrice: number;
  tonne: number;
  tonnes: number;
  isValid: boolean;
  isReject: boolean;
  isLoading: boolean;
  productlabels: string[];
  userLabels: string[];
  isRegisterInJDE: boolean;
  isLoadingInModal: boolean;
  isCancelJdeNber: boolean;
  totalToExport: number;
  modalPurchaseORder: boolean;
  reference: string = '';
  modalEditOrder: boolean;
  prices: number[] = [];
  rejectReason: string = '';
  cancelReason: string = '';
  orderStatus = OrderStatus;
  isCancel: boolean;
  isRequestCancelOrder: boolean;
  erpSoldToIdLabels: { label: string }[];

  constructor(
    private jdeService: JdeService,
    public commonSrv: CommonService,
    private priceSrv: PricesService,
    public dialogService: DialogService,
    private messageService: MessageService,
    public historyOrderSrv: MenuHistoryService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    this.statusOrder = (this.language === 'en') ? this.statusOrderEn : this.statusOrder;
    this.statusDocument = (this.language === 'en') ? this.statusDocumentEn : this.statusDocument;

    await this.getOrders();
    await this.getElementsForFilters();
    await this.getProductsForFilters();
    await this.getUsersForFilters();
    this.rejectionOptions = [
      { label: 'Encours atteint', value: 'Encours atteint' },
      { label: 'Facture échue', value: 'Facture échue' },
      { label: 'Autre', value: 'Autre' }
    ];
  }

  async ngAfterContentInit(): Promise<void> {
    const source = interval(180000);
    this.subscription = source.subscribe(async () => {
      this.filterForm.renderType = null;
      await this.getOrders();
    });
  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    await this.getOrders();
  }

  async getOrders(): Promise<boolean> {
    this.isLoading = true;
    if ((this.filterForm.date.start || this.filterForm.date.end) && (this.filterForm.date.start > this.filterForm.date.end)) {
      this.messageService.add({ severity: 'error', summary: '', detail: await t('ERROR_INTERVAL_DATE') })
      return this.isLoading = false;
    }
    const query = {
      limit: this.limit,
      offset: this.offset,
      ...this.filterForm,
    };
    const result = await this.historyOrderSrv.getOrdersByCommercials(query);

    // const recapQuery = {
    //   ...query,
    //   status: `{"$in": [${OrderStatus.VALIDATED}]}`
    // };

    const res: any = await this.historyOrderSrv.getRecapInfosForOrderList(query);
    this.totalAmount = res[0]?.totalAmount;
    this.totalTonnes = res[0]?.totalTonnes;
    this.totalOrders = res[0]?.numberOfOrders;

    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message
      });
      return this.isLoading = false;
    }

    this.total = result?.count;
    this.orders = result?.data;
    this.totalPrice = this.orders?.map(order => order?.cart?.amount?.TTC)?.reduce((sum, total) => sum + total, 0);
    this.erpSoldToIdLabels = Array.from(new Set(this.orders?.map(order => order?.company?.erpSoldToId))).map(ref => ({ label: ref }));

    for (const order of this.orders) {
      const map = order?.cart?.items?.map(item => item?.quantity / (item?.packaging?.unit?.ratioToTone))
      this.totalTonnage = +(map?.reduce((sum, tonne) => sum + tonne, 0)) + this.totalTonnage;
    }
    return this.isLoading = false;
  }

  async getDataOrders(query: QueryFilter): Promise<{ count: number, data: Order[] }> {
    const { date } = query;
    if ((date?.start || date?.end) && (date?.start > date?.end)) {
      this.messageService.add({ severity: 'error', summary: '', detail: await t('ERROR_INTERVAL_DATE') })
      return;
    }
    const res = await this.historyOrderSrv.getOrdersByCommercials({ ...query });

    if (res instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: res.error.message
      });
    }

    return res as { count: number, data: Order[] };
  }

  async getElementsForFilters() {
    const keyForFilters = ['appReference'];
    const orderNumbers = await this.commonSrv.getElementForFilterByKeys('orders', { keyForFilters })
    this.orderNumbers = orderNumbers?.dataappReference;
  }

  async getProductsForFilters() {
    const keyForFilters = ['label'];
    const productsFiltersOptions = await this.commonSrv.getElementForFilterByKeys('products', { keyForFilters });
    this.productlabels = productsFiltersOptions?.datalabel;
  }

  async getUsersForFilters() {
    const keyForFilters = ['name'];
    const companiesFiltersOptions = await this.commonSrv.getElementForFilterByKeys('companies', { keyForFilters });
    this.userLabels = companiesFiltersOptions?.dataname;
  }

  showModalDetail(order: Order) {
    this.order = order;
    this.historyOrderSrv.modalDetail = true;
  }

  showModalPurchaseORder(order: Order) {
    this.order = order;
    this.modalPurchaseORder = true;
  }

  showModalRemovalDetail(order: Order) {
    this.order = order;
    this.modalDetailRemoval = true;
  }

  showModalValidate(order: Order) {
    this.order = order;
    this.isValid = true;
  }
  showModalReject(order: Order) {
    this.order = order;
    this.isReject = true;
  }

  showOrderRequestCancelMOdal(order: Order) {
    this.order = order;
    this.isRequestCancelOrder = true;
  }

  showModalCancel(order: Order) {
    this.order = order;
    this.isCancel = true;
  }
  showModalCancelJdeNber(order: Order) {
    this.order = order;
    this.isCancelJdeNber = true;
  }

  showModalToSendInJDE(order: Order) {
    this.order = order;
    this.isRegisterInJDE = true;
  }

  async showModalEditOrder(order: Order) {
    const offerPrices = await this.priceSrv.getPricesForOrder({ storeId: order?.cart?.store?._id, userId: order?.user?._id });
    this.order = { ...order };
    this.order?.cart?.items?.forEach((item, index) => {
      const itemOfferPrices = offerPrices?.filter(offerPrice => {
        if (offerPrice?.packaging && (offerPrice?.renderType == order?.cart?.renderType) && (offerPrice?.product?._id == item?.product?._id))
          return offerPrice;
      }).map((price) => {
        return { packaging: price?.packaging, amount: price?.amount }
      });

      this.customerPackaging[index] = itemOfferPrices;
      this.packagings[index] = itemOfferPrices.map(item => item?.packaging);
      // this.prices[index] = this.calculatePrice(item);
    });
    this.modalEditOrder = true;
  }

  calculatePrice(item: any): number {
    if (item?.unitPrice && item?.quantity) {
      return item?.quantity * item?.unitPrice;
    } else {
      return 0;
    }
  }

  getTotalPrice(event: any, packaging: Packaging, i: number) {
    const amount = this.customerPackaging[i]?.find((price: { packaging: { _id: string; }; }) => price?.packaging?._id == packaging?._id)?.amount ?? 0;
    this.offerPriceAmount[i] = event?.value * amount;
  }
  resetQuantity() {
  }

  async reset(): Promise<void> {
    this.filterForm = {
      status: `{"$in": [${OrderStatus.CREATED}, ${OrderStatus.PAID}, ${OrderStatus.VALIDATED}, ${OrderStatus.CREDIT_REJECTED}]}`,
      date: { start: moment().startOf('year'), end: moment().endOf('year') },
      enable: true,
      paymentMode: null,
      userCategory: null,
      renderType: null,
      customer: '',
      region: '',
      product: '',
      appReference: null,
    };
    await this.getOrders();
  }

  async saveErpReference() {
    const res = await this.historyOrderSrv.saveErpReference(this?.order?._id, this.reference);
    if (res.status < HttpStatusCode.BadRequest) {
      await this.getOrders();
      this.messageService.add({
        severity: 'success',
        summary: `${await t('VALID_ACTION')}`,
        detail: '' + res?.message,
      });
      this.isValid = false;
    }
    this.reference = '';
  }

  async changeStatus() {
    this.isLoadingInModal = true;
    this.order.erpReference = this.reference;
    await this.saveErpReference();
    const res = await this.historyOrderSrv.CommercialValidatedOrder(this.order);
    if (res.status < HttpStatusCode.BadRequest) {
      await this.reset();
      this.messageService.add({
        severity: 'success',
        summary: `${await t('VALID_ACTION')}`,
        detail: '' + res?.message,
      });
      this.isValid = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: res?.data || await t('ACTION_FAILED'),
        detail: res?.message,
      });
    }
    this.isLoadingInModal = false;
  }

  async rejectOrder() {
    await this.handleOrderAction('reject');
  }

  async cancelOrder() {
    await this.handleOrderAction('cancel');
  }

  async handleOrderAction(actionType: 'reject' | 'cancel') {
    this.isLoadingInModal = true;
    let reason: string;
    let successMessage: string;
    let failureMessage: string;

    if (actionType === 'reject') {
      this.order.rejectReason = this.rejectReason;
      reason = this.rejectReason;
      successMessage = await t('REJECT_ACTION');
      failureMessage = await t('REJECT_ACTION_FAILED');
    } else if (actionType === 'cancel') {
      this.order.cancelReason = this.cancelReason;
      reason = this.cancelReason;
      successMessage = await t('CANCEL_ACTION');
      failureMessage = await t('CANCEL_ACTION_FAILED');
    }

    const res = await this.historyOrderSrv.commercialHandleOrderAction(this.order, actionType);
    if (res.status < HttpStatusCode.BadRequest) {
      await this.reset();
      this.messageService.add({
        severity: 'success',
        summary: successMessage,
        detail: '' + res?.message,
      });
      if (actionType === 'reject') {
        this.isReject = false;
      } else if (actionType === 'cancel') {
        this.isCancel = false;
      }
    } else {
      this.messageService.add({
        severity: 'error',
        summary: res?.data || failureMessage,
        detail: res?.message,
      });
    }
    reason = '';
    this.isLoadingInModal = false;
  }



  async resendInJDE() {
    this.isLoadingInModal = true;
    const res = await this.jdeService.resendOrderInJDE(this.order);
    this.isLoadingInModal = false;

    if (res?.status >= HttpStatusCode.BadRequest) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('ACTION_FAILED')}`,
        detail: '' + res?.message,
      });
    } else {
      await this.getOrders();
      this.messageService.add({
        severity: 'success',
        summary: res?.data || await t('VALID_ACTION'),
        detail: res?.message,
      });
      this.isRegisterInJDE = false;
    }
  }

  async cancelJdeNumber() {
    this.isLoadingInModal = true;
    const order: Order = { ...this.order };
    const canceledJdeNumbers: any[] = order?.canceledJdeNumbers || [];
    canceledJdeNumbers.push(order?.erpReference);

    const objOrder = {
      canceledJdeNumbers: canceledJdeNumbers,
      erpReference: ''
    }

    const res = await this.historyOrderSrv.updateOrderJdeNumber(order._id, objOrder);
    if (res?.status >= HttpStatusCode.BadRequest) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('ACTION_FAILED')}`,
        detail: '' + res?.message,
      });
    } else {
      await this.getOrders();
      this.messageService.add({
        severity: 'success',
        summary: res?.data || await t('JDE_NBER_DELETED'),
        detail: await t('JDE_NUMBERS_CANCELED') || res?.data,
      });
    }
    this.isLoadingInModal = false;
    this.isCancelJdeNber = false;
  }


  async rejectOrderCancelRequest() {
    this.isLoadingInModal = true;
    const res = await this.historyOrderSrv.rejectDemandToCancelOrder(this.order._id, { cancellationStatus: OrderCancellationRequestStatus.REFUSED });
    if (res?.status >= HttpStatusCode.BadRequest) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('ACTION_FAILED')}`,
        detail: '' + res?.message,
      });
    } else {
      await this.getOrders();
      this.messageService.add({
        severity: 'success',
        summary: 'Opération réussie',
        detail: 'Rejet de la demande d annulation réussie ',
      });
    }
    this.isLoadingInModal = false;
    this.isRequestCancelOrder = false;
  }

  async exportToExcel() {
    this.isLoading = true;
    const { data, count } = await this.getDataOrders(this.filterFormExport);
    this.totalToExport = count;
    if (!data) return;

    this.dataOrder = data.map(elt => {
      const dataElt = {};
      dataElt['N° Mycimencam '] = elt?.appReference || 'N/A';
      dataElt['N° JDE'] = elt?.erpReference || 'N/A';
      dataElt['Type'] = RenderType[+elt?.cart?.renderType] || 'N/A';
      dataElt['Client'] = elt?.company?.name ?? elt?.user?.firstName + ' ' + elt?.user?.lastName;
      dataElt['Catégorie'] = CompanyCategory[+elt?.company?.category] ?? UserCategory[+elt?.user?.category];
      dataElt['Region'] = elt?.company?.address?.commercialRegion || 'N/A';
      dataElt['Montant'] = elt?.cart?.amount?.TTC || 0 + ' XAF' || 'N/A';
      dataElt['Date'] = moment(elt?.created_at).format('YYYY-MM-DD') || 'N/A';
      dataElt['Code client'] = elt?.company?.erpSoldToId || 'N/A';
      dataElt['Status de la commande'] = OrderStatus[+elt?.status] || 'N/A'
      return dataElt;
    });
    this.commonSrv.exportRetriveExcelFile(this.dataOrder, 'list_commande');
    await this.resetExportFilters();
    this.isLoading = false;
  }

  resetExportFilters() {
    this.filterFormExport = {
      status: '',
      appReference: '',
      date: { start: moment().startOf('year'), end: moment().endOf('year') },
      userCategory: null,
      renderType: '',
      paymentMode: null,
      customer: '',
      product: '',
      erpSoldToId: '',
      enable: true,
      limit: 1000
    };
  }

  getProductForSchedule(itemId: string) {
    return this.order?.cart?.items.find(item => item?.product?._id == itemId)?.product?.image;
  }

  getQuantityProductForSchedule(itemId: string) {
    return this.order?.cart?.items.find(item => item?.product?._id == itemId)?.quantity;
  }

  onRejectionOptionChange(event: any) {
    if (event.value === 'Autre') {
      this.showCustomReasonInput = true;
    } else {
      this.rejectReason = event.value;
      this.showCustomReasonInput = false;
    }
  }

}
